{"user": "小罗", "created_time": "2025-07-08T10:57:35.243653", "description": "新增联系人记录文件", "note": "此文件记录后续新增的联系人，首次获取的联系人不会记录", "records": [{"timestamp": "2025-07-09T13:01:16.918359", "detection_time": "2025-07-09 13:01:16", "count": 6, "detection_method": "file_comparison", "contacts": [{"UserName": "48042715126@chatroom", "Alias": "", "Remark": "", "NickName": "顾问部门微信好友查重工具安装"}, {"UserName": "gh_cadcfc42aec6", "Alias": "", "Remark": "", "NickName": "广告数据同步备用账号3"}, {"UserName": "wxid_357lzexgzu8r21", "Alias": "<PERSON><PERSON><PERSON>", "Remark": "", "NickName": "婷婷(闭关勿扰）"}, {"UserName": "wxid_eyxtggpju0o422", "Alias": "new_wechat_123456", "Remark": "hgw", "NickName": "H"}, {"UserName": "wxid_z3gwvp8wc9wi11", "Alias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remark": "邓晴（猎聘）", "NickName": "长不高的丸子"}, {"UserName": "zhenyang163875", "Alias": "", "Remark": "刘振洋（猎聘）2023才出国", "NickName": "振洋"}]}, {"timestamp": "2025-07-15T08:19:59.623592", "detection_time": "2025-07-15 08:19:59", "count": 6, "detection_method": "monitor_auto_detection", "contacts": [{"UserName": "48042715126@chatroom", "Alias": "", "Remark": "", "NickName": "顾问部门微信好友查重工具安装"}, {"UserName": "gh_cadcfc42aec6", "Alias": "", "Remark": "", "NickName": "广告数据同步备用账号3"}, {"UserName": "wxid_357lzexgzu8r21", "Alias": "<PERSON><PERSON><PERSON>", "Remark": "", "NickName": "婷婷(闭关勿扰）"}, {"UserName": "wxid_eyxtggpju0o422", "Alias": "new_wechat_123456", "Remark": "hgw", "NickName": "H"}, {"UserName": "wxid_z3gwvp8wc9wi11", "Alias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remark": "邓晴（猎聘）", "NickName": "长不高的丸子"}, {"UserName": "zhenyang163875", "Alias": "", "Remark": "刘振洋（猎聘）2023才出国", "NickName": "振洋"}]}], "last_updated": "2025-07-15T08:19:59.623592"}