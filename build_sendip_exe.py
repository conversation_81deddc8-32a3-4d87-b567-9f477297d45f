#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SendIp.py 打包脚本 - 将SendIp.py打包成SendIp.exe
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

def build_sendip_exe():
    """构建SendIp.exe文件"""
    print("🔨 开始构建SendIp.exe文件...")
    
    source_file = "templates/SendIp.py"
    
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    try:
        # 使用Python模块方式调用PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',  # 打包成单个exe文件
            '--windowed',  # 不显示控制台窗口（GUI应用）
            '--name=SendIp',  # 指定exe文件名
            '--distpath=dist',  # 输出目录
            '--workpath=build_sendip',  # 工作目录
            '--specpath=.',  # spec文件目录
            '--clean',  # 清理缓存
            '--icon=NONE',  # 暂时不设置图标
            source_file
        ]
        
        print(f"📝 执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ SendIp.exe构建成功!")
            
            # 检查生成的文件
            exe_path = Path('dist/SendIp.exe')
            if exe_path.exists():
                file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📁 生成的exe文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.2f} MB")
                return True
            else:
                print("❌ SendIp.exe文件未找到")
                return False
        else:
            print("❌ SendIp.exe构建失败")
            print(f"标准输出: {result.stdout}")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def create_sendip_config():
    """创建SendIp配置文件"""
    config_content = f"""# SendIp.exe 配置文件
# 服务器地址 - 修改为你的Flask服务器地址
http://127.0.0.1:8080/report_ip

# 检查间隔（秒）
60

# 用户名（可选，如果为空会提示输入）
"""
    
    config_path = "dist/config.txt"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 创建配置文件: {config_path}")

def create_sendip_readme():
    """创建SendIp使用说明"""
    readme_content = f"""# SendIp.exe - IP地址监控工具

## 📋 功能说明

SendIp.exe 是一个Windows桌面应用程序，用于监控本机IP地址变化并自动报告给服务器。

## 🚀 使用方法

### 1. 配置服务器地址
编辑 `config.txt` 文件，设置正确的服务器地址：
```
http://your-server:8080/report_ip
```

### 2. 运行程序
双击 `SendIp.exe` 启动程序

### 3. 设置用户名
首次运行时会提示输入用户名，这个用户名将用于标识你的设备

## ⚙️ 功能特性

- **自动IP监控**: 每60秒检查一次IP地址变化
- **图形界面**: 简洁的Windows GUI界面
- **自动报告**: IP变化时自动发送到服务器
- **开机自启**: 可设置开机自动启动
- **系统托盘**: 最小化到系统托盘运行

## 📁 文件说明

- `SendIp.exe` - 主程序文件
- `config.txt` - 配置文件
- `README.md` - 本说明文件

## 🔧 配置选项

### config.txt 格式：
```
# 第一行：服务器地址
http://127.0.0.1:8080/report_ip

# 第二行：检查间隔（秒）
60

# 第三行：用户名（可选）
my_computer
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器地址是否正确
   - 确认服务器正在运行
   - 检查网络连接

2. **程序无法启动**
   - 确保config.txt文件存在
   - 检查文件权限
   - 以管理员身份运行

3. **IP检测不准确**
   - 程序会自动检测公网IP
   - 如果在内网环境，可能显示内网IP

## 📊 工作原理

1. 程序启动后会读取配置文件
2. 定期检查当前IP地址
3. 如果IP地址发生变化，发送POST请求到服务器
4. 服务器接收到请求后更新用户文件夹下的JSON文件名

## 🔒 安全说明

- 程序只发送用户名和IP地址信息
- 不收集其他个人信息
- 所有通信使用HTTP协议（可升级为HTTPS）

---
生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
版本: 1.0.0
"""
    
    readme_path = "dist/README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 创建说明文件: {readme_path}")

def create_sendip_batch():
    """创建启动批处理文件"""
    batch_content = '''@echo off
echo 启动SendIp IP地址监控工具
echo ============================
echo.

REM 检查exe文件是否存在
if not exist "SendIp.exe" (
    echo 错误: SendIp.exe 文件不存在
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config.txt" (
    echo 警告: config.txt 配置文件不存在，将使用默认配置
)

REM 运行SendIp.exe
echo 正在启动IP监控程序...
start SendIp.exe

echo 程序已启动，请查看系统托盘
echo 按任意键退出...
pause >nul
'''
    
    batch_path = "dist/run_sendip.bat"
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✅ 创建启动脚本: {batch_path}")

def clean_build_files():
    """清理构建临时文件"""
    print("🧹 清理临时文件...")
    
    cleanup_items = [
        'build_sendip',
        '__pycache__',
        'SendIp.spec'
    ]
    
    for item in cleanup_items:
        if os.path.exists(item):
            try:
                if os.path.isdir(item):
                    shutil.rmtree(item)
                    print(f"🗑️ 删除目录: {item}")
                else:
                    os.remove(item)
                    print(f"🗑️ 删除文件: {item}")
            except Exception as e:
                print(f"⚠️ 删除失败 {item}: {e}")

def main():
    """主函数"""
    print("🚀 SendIp.py 打包工具")
    print("=" * 50)
    
    # 检查源文件是否存在
    if not os.path.exists('templates/SendIp.py'):
        print("❌ 源文件 templates/SendIp.py 不存在")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 创建输出目录
    os.makedirs('dist', exist_ok=True)
    
    # 构建exe文件
    if not build_sendip_exe():
        return False
    
    # 创建配置和说明文件
    create_sendip_config()
    create_sendip_readme()
    create_sendip_batch()
    
    # 清理临时文件
    clean_build_files()
    
    print("\n🎉 SendIp.exe 打包完成!")
    print("=" * 50)
    print("📁 生成的文件:")
    print("   - dist/SendIp.exe (主程序)")
    print("   - dist/config.txt (配置文件)")
    print("   - dist/run_sendip.bat (启动脚本)")
    print("   - dist/README.md (使用说明)")
    
    print("\n💡 使用方法:")
    print("1. 编辑 dist/config.txt 设置服务器地址")
    print("2. 双击 dist/run_sendip.bat 或直接运行 SendIp.exe")
    print("3. 首次运行时输入用户名")
    print("4. 程序将在系统托盘中运行，监控IP变化")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 打包成功完成!")
        else:
            print("\n❌ 打包失败!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 打包过程中出现异常: {e}")
        sys.exit(1)
