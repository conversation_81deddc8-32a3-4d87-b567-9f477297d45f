# 用户JSON文件更新测试工具 - 安装和使用指南

## 📦 包含文件

- `UserJsonUpdateTest.exe` - 主程序文件 (约13.7MB)
- `run_test.bat` - 批处理启动脚本
- `README.md` - 详细使用说明
- `INSTALL_GUIDE.md` - 本安装指南

## 🚀 快速开始

### 1. 准备工作
确保以下条件满足：
- Flask服务器正在运行在 `http://127.0.0.1:8080`
- 存在 `data/` 目录，包含用户文件夹和JSON文件
- 网络连接正常

### 2. 运行程序

**方法一：使用批处理文件（推荐）**
```
双击 run_test.bat
```

**方法二：直接运行exe**
```
双击 UserJsonUpdateTest.exe
```

**方法三：命令行运行**
```cmd
cd /d "程序所在目录"
UserJsonUpdateTest.exe
```

## 📋 测试流程

程序会自动执行以下测试：

1. **数据结构检查**
   - 扫描 `data/` 目录下的用户文件夹
   - 检查JSON文件格式和命名规范
   - 显示用户统计信息

2. **IP变化测试**
   - 选择一个测试用户
   - 模拟不同的IP地址变化场景
   - 验证JSON文件名更新功能
   - 检查IP变化日志生成

3. **结果验证**
   - 确认文件重命名是否正确
   - 验证IP变化日志记录
   - 显示测试统计结果

## 📊 预期结果

### 成功的测试应该显示：
```
🎉 所有测试都通过了！用户JSON文件更新功能正常工作

💡 功能说明:
1. 当SendIp.exe报告IP变化时，系统会自动更新对应用户文件夹下的JSON文件名
2. 文件名格式: {新IP地址}_{用户名}.json (IP中的点替换为下划线)
3. 系统会在用户文件夹中生成ip_change_log.json记录变化历史
4. 旧的JSON文件会被重命名为新的文件名，保持数据完整性
```

### 测试示例：
- 用户: `kaiyanzhu`
- 原文件: `192_168_2_50_kaiyanzhu.json`
- 新IP: `**********`
- 新文件: `10_0_0_100_kaiyanzhu.json`

## 🐛 故障排除

### 常见错误及解决方案

**1. 连接失败**
```
❌ 请求异常: ConnectionError
```
**解决方案：**
- 检查Flask服务器是否运行：访问 http://127.0.0.1:8080
- 确认端口8080未被其他程序占用
- 检查防火墙设置

**2. 数据目录不存在**
```
❌ 数据目录不存在: data
```
**解决方案：**
- 确保程序运行目录下有 `data/` 文件夹
- 检查用户文件夹结构：`data/用户名/IP_用户名.json`

**3. 权限问题**
```
❌ 更新用户JSON文件名失败: PermissionError
```
**解决方案：**
- 以管理员身份运行程序
- 检查文件和文件夹权限
- 确保文件未被其他程序占用

**4. 文件格式错误**
```
⚠️ 未找到用户 xxx 的基准JSON文件
```
**解决方案：**
- 检查JSON文件命名格式：`IP地址_用户名.json`
- 确认文件不是以 `new_contacts` 开头
- 验证JSON文件内容格式正确

## 📁 文件结构示例

```
程序目录/
├── UserJsonUpdateTest.exe
├── run_test.bat
├── README.md
├── INSTALL_GUIDE.md
└── data/
    ├── kaiyanzhu/
    │   ├── 192_168_2_50_kaiyanzhu.json
    │   ├── ip_change_log.json
    │   └── new_contacts_log.json
    ├── 小马/
    │   ├── 192_168_2_4_小马.json
    │   └── new_contacts_log.json
    └── ...
```

## 🔧 高级配置

### 修改服务器地址
如果Flask服务器运行在其他地址，需要修改源代码中的API地址：
```python
api_url = "http://your-server:port/report_ip"
```

### 自定义测试参数
程序支持以下测试场景：
- 相同IP地址测试
- 内网IP地址测试
- 公网IP地址测试
- 批量用户测试

## 📞 技术支持

### 系统要求
- Windows 7/8/10/11
- 网络连接
- 至少50MB可用磁盘空间

### 性能说明
- 程序大小：约13.7MB
- 内存占用：约20-50MB
- 运行时间：通常1-3分钟

### 联系方式
如需技术支持，请提供：
1. 错误截图或日志
2. 系统环境信息
3. Flask服务器状态
4. 数据目录结构

---
**版本信息**
- 程序版本: 1.0.0
- 构建时间: 2025-07-15 10:08:07
- Python版本: 3.12+
- 打包工具: PyInstaller 6.14.2
