{"user": "小罗", "contact_id": "48148", "contact_info": {"UserName": "wang<PERSON>ling<PERSON>z", "Alias": "Absolute_Peace", "Remark": "48148 王新玲", "NickName": "oo"}, "fetch_time": "2025-07-09T13:19:58.068647", "message_count": 84, "chatlog_data": [{"seq": 1724749762000, "time": "2024-08-27T17:09:22+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 10000, "subType": 0, "content": "以上是打招呼的内容"}, {"seq": 1724749762000, "time": "2024-08-27T17:09:22+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "我是领英上黄乐乐推荐的"}, {"seq": 1724749762000, "time": "2024-08-27T17:09:22+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 10000, "subType": 0, "content": "Sky horse刚刚把你添加到通讯录，现在可以开始聊天了。"}, {"seq": 1724749904000, "time": "2024-08-27T17:11:44+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 10000, "subType": 0, "content": "你已添加了Sky horse，现在可以开始聊天了。"}, {"seq": 1724749933000, "time": "2024-08-27T17:12:13+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士，您好！我们是专注于高薪挖掘海外博士，教授，院士的专业国际猎头公司Top talents Group."}, {"seq": 1724753847000, "time": "2024-08-27T18:17:27+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "您好"}, {"seq": 1724755539000, "time": "2024-08-27T18:45:39+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士 方便给我发一份您的最新简历吗？咱们约一个时间语音沟通一下？"}, {"seq": 1724755596000, "time": "2024-08-27T18:46:36+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "好的 回头发给你 我想回国找个兼职高校教职"}, {"seq": 1724761737000, "time": "2024-08-27T20:28:57+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以的"}, {"seq": 1726302793000, "time": "2024-09-14T16:33:13+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "月圆家圆人圆事圆圆圆团团，国和家和人和事和和和美美，祝您全家幸福和气满堂，阖家欢乐！"}, {"seq": 1726302794000, "time": "2024-09-14T16:33:14+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "", "contents": {"md5": "f2b8f3bdceae1e768b54348854288373"}}, {"seq": 1726302832000, "time": "2024-09-14T16:33:52+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 47, "subType": 0, "content": ""}, {"seq": 1726302840000, "time": "2024-09-14T16:34:00+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "中秋节快乐🎑"}, {"seq": 1726645483000, "time": "2024-09-18T15:44:43+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博 您好! 今天下午4点方便沟通吗？"}, {"seq": 1726650792000, "time": "2024-09-18T17:13:12+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 47, "subType": 0, "content": ""}, {"seq": 1726650796000, "time": "2024-09-18T17:13:16+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "没问题"}, {"seq": 1726650826000, "time": "2024-09-18T17:13:46+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博 现在已经5点多了 您这边再晚一点可以吗？"}, {"seq": 1726650852000, "time": "2024-09-18T17:14:12+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1726650864000, "time": "2024-09-18T17:14:24+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "您不在国内？"}, {"seq": 1726650891000, "time": "2024-09-18T17:14:51+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "哦哦"}, {"seq": 1726650894000, "time": "2024-09-18T17:14:54+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "算错了"}, {"seq": 1726650917000, "time": "2024-09-18T17:15:17+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "您的4点是我的10点"}, {"seq": 1726650924000, "time": "2024-09-18T17:15:24+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "对不起 算错了"}, {"seq": 1726650976000, "time": "2024-09-18T17:16:16+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没关系 那明天还是这个时间可以吗？"}, {"seq": 1726650987000, "time": "2024-09-18T17:16:27+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "可以 那明天吧"}, {"seq": 1726650989000, "time": "2024-09-18T17:16:29+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "抱歉"}, {"seq": 1726651024000, "time": "2024-09-18T17:17:04+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没关系的"}, {"seq": 1726651032000, "time": "2024-09-18T17:17:12+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "[抱拳]"}, {"seq": 1726731948000, "time": "2024-09-19T15:45:48+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "你好，15分钟后，我准备好了"}, {"seq": 1726731982000, "time": "2024-09-19T15:46:22+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[OK]"}, {"seq": 1726732800000, "time": "2024-09-19T16:00:00+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "现在方便吗?"}, {"seq": 1726732939000, "time": "2024-09-19T16:02:19+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": ""}, {"seq": 1726733207000, "time": "2024-09-19T16:06:47+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "哎呀"}, {"seq": 1726733219000, "time": "2024-09-19T16:06:59+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "我没听见 现在可以吗"}, {"seq": 1726733248000, "time": "2024-09-19T16:07:28+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 50, "subType": 0, "content": ""}, {"seq": 1726735695000, "time": "2024-09-19T16:48:15+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "您好，听您提供的信息后，我打算后年申请海优回去，不过明年我还是打算找个国内的兼职"}, {"seq": 1726735719000, "time": "2024-09-19T16:48:39+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "看您那里是否有高校兼职的资源"}, {"seq": 1726735736000, "time": "2024-09-19T16:48:56+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的 王博 有合适的机会我告诉您"}, {"seq": 1726735737000, "time": "2024-09-19T16:48:57+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "比如二本 或者 贵州那边听说有"}, {"seq": 1726735742000, "time": "2024-09-19T16:49:02+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "好哒，多谢"}, {"seq": 1726735752000, "time": "2024-09-19T16:49:12+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "我随时可以应聘"}, {"seq": 1726735775000, "time": "2024-09-19T16:49:35+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的 王博"}, {"seq": 1726735786000, "time": "2024-09-19T16:49:46+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 47, "subType": 0, "content": ""}, {"seq": 1747967043000, "time": "2025-05-23T10:24:03+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博 您好!"}, {"seq": 1747971315000, "time": "2025-05-23T11:35:15+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "您好"}, {"seq": 1747971422000, "time": "2025-05-23T11:37:02+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博 我是小罗 去年有联系过  您是想看一下国内的一些高校兼职机会 明年申报人才项目 是吧？"}, {"seq": 1747971433000, "time": "2025-05-23T11:37:13+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "是的"}, {"seq": 1747971466000, "time": "2025-05-23T11:37:46+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "不过我今年9月可能回国"}, {"seq": 1747971486000, "time": "2025-05-23T11:38:06+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "全职的机会也可以考虑是吧？"}, {"seq": 1747971504000, "time": "2025-05-23T11:38:24+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "对 目前在招全职的"}, {"seq": 1747971512000, "time": "2025-05-23T11:38:32+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "最近发表了一篇论文"}, {"seq": 1747971535000, "time": "2025-05-23T11:38:55+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "ok 您有了解过浙江的省级人才项目吗？"}, {"seq": 1747971542000, "time": "2025-05-23T11:39:02+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "没有"}, {"seq": 1747971561000, "time": "2025-05-23T11:39:21+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "目前打算看看北京 上海"}, {"seq": 1747971574000, "time": "2025-05-23T11:39:34+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "因为留学回国人员可以落户"}, {"seq": 1747971584000, "time": "2025-05-23T11:39:44+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "浙江的省级人才项目就是全职的招聘"}, {"seq": 1747971594000, "time": "2025-05-23T11:39:54+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "哦哦"}, {"seq": 1747971603000, "time": "2025-05-23T11:40:03+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "西湖大学"}, {"seq": 1747971611000, "time": "2025-05-23T11:40:11+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "或者其他大学"}, {"seq": 1747971611000, "time": "2025-05-23T11:40:11+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "都可以落户的 只是北京上海的含金量大一些的"}, {"seq": 1747971619000, "time": "2025-05-23T11:40:19+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您有申请过西湖大学吗？"}, {"seq": 1747971620000, "time": "2025-05-23T11:40:20+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "对"}, {"seq": 1747971625000, "time": "2025-05-23T11:40:25+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "没有呢"}, {"seq": 1747971640000, "time": "2025-05-23T11:40:40+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "我还在积攒成果"}, {"seq": 1747971688000, "time": "2025-05-23T11:41:28+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您9月份回国的话 现在可以开始联系了"}, {"seq": 1747971972000, "time": "2025-05-23T11:46:12+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "是的"}, {"seq": 1747971981000, "time": "2025-05-23T11:46:21+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "我的成果还不够"}, {"seq": 1747972001000, "time": "2025-05-23T11:46:41+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那您的成果什么时候可以出来呢?"}, {"seq": 1747972011000, "time": "2025-05-23T11:46:51+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "10月"}, {"seq": 1747972023000, "time": "2025-05-23T11:47:03+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "还有一篇文章"}, {"seq": 1747972032000, "time": "2025-05-23T11:47:12+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "预计发表"}, {"seq": 1747972034000, "time": "2025-05-23T11:47:14+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那您申请明年的人才项目应该没有问题"}, {"seq": 1747972042000, "time": "2025-05-23T11:47:22+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "哦"}, {"seq": 1747972050000, "time": "2025-05-23T11:47:30+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "可是我不想去浙江"}, {"seq": 1747972077000, "time": "2025-05-23T11:47:57+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "您可以看看北京上海吗"}, {"seq": 1747972119000, "time": "2025-05-23T11:48:39+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "也可以的"}, {"seq": 1747972128000, "time": "2025-05-23T11:48:48+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢"}, {"seq": 1747972129000, "time": "2025-05-23T11:48:49+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博 方便给我发一份您的最新简历吗？"}, {"seq": 1747972139000, "time": "2025-05-23T11:48:59+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "好的 稍等"}, {"seq": 1747972276000, "time": "2025-05-23T11:51:16+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 49, "subType": 6, "content": "", "contents": {"md5": "093433d976581d01a079056f469a93c4", "title": "简历 王新玲2025.pdf"}}, {"seq": 1747973830000, "time": "2025-05-23T12:17:10+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到 您主要是考虑教职是吗？"}, {"seq": 1747974173000, "time": "2025-05-23T12:22:53+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "对 大学教职"}, {"seq": 1747977343000, "time": "2025-05-23T13:15:43+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "ok"}, {"seq": 1747977353000, "time": "2025-05-23T13:15:53+08:00", "talker": "wang<PERSON>ling<PERSON>z", "talkerName": "", "isChatRoom": false, "sender": "wang<PERSON>ling<PERSON>z", "senderName": "48148 王新玲", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢"}]}