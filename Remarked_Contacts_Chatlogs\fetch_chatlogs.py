# 获取所有设置了"数字+任意字符"格式备注的联系人的聊天记录

import json
import os
import re
import requests
import urllib.parse
from pathlib import Path
from datetime import datetime
import time

class ChatlogFetcher:
    def __init__(self):
        self.data_dir = Path("../data")  # 相对于当前文件夹的data目录
        self.output_dir = Path(".")  # 当前目录作为输出目录
        self.excluded_folders = {"test_ip_change_user"}
        self.remark_pattern = re.compile(r'^\d+.*$')  # 匹配"数字+任意字符"格式
        self.current_date = datetime.now().strftime("%Y-%m-%d")
        self.time_range = f"2023-01-01%7E{self.current_date}"
        
    def extract_ip_from_filename(self, filename):
        """从文件名中提取IP地址"""
        # 文件名格式：192_168_2_49_小马.json
        parts = filename.replace('.json', '').split('_')
        if len(parts) >= 4:
            # 前4部分是IP地址
            ip_parts = parts[:4]
            ip_address = '.'.join(ip_parts)
            return ip_address
        return None
    
    def get_user_contacts_and_ip(self, user_folder):
        """获取用户的联系人数据和IP地址"""
        username = user_folder.name
        
        # 查找联系人文件
        contact_files = [f for f in user_folder.glob("*.json") 
                        if not f.name.startswith(("new_contacts", "ip_change"))]
        
        if not contact_files:
            print(f"   ❌ {username}: 没有找到联系人文件")
            return None, None, None
        
        # 使用最新的联系人文件
        latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
        
        # 提取IP地址
        ip_address = self.extract_ip_from_filename(latest_file.name)
        if not ip_address:
            print(f"   ❌ {username}: 无法从文件名提取IP地址 ({latest_file.name})")
            return None, None, None
        
        # 读取联系人数据
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                contacts = json.load(f)
            
            if not isinstance(contacts, list):
                print(f"   ❌ {username}: 联系人文件格式错误")
                return None, None, None
            
            return contacts, ip_address, latest_file.name
            
        except Exception as e:
            print(f"   ❌ {username}: 读取联系人文件失败 - {e}")
            return None, None, None
    
    def get_talker_value(self, contact):
        """获取talker参数值，按优先级：Remark > NickName > Alias > UserName"""
        for field in ['Remark', 'NickName', 'Alias', 'UserName']:
            value = contact.get(field, '')
            if value is None:
                value = ''
            else:
                value = str(value).strip()
            if value:
                return value
        return None
    
    def fetch_chatlog_for_contact(self, ip_address, contact, debug=False):
        """为单个联系人获取聊天记录"""
        talker = self.get_talker_value(contact)
        if not talker:
            return None, "无有效talker参数"

        # URL编码talker参数
        encoded_talker = urllib.parse.quote(talker)

        # 构建API URL (添加format=json参数)
        api_url = f"http://{ip_address}:5030/api/v1/chatlog?time={self.time_range}&talker={encoded_talker}&format=json"

        if debug:
            print(f"       🔗 API URL: {api_url}")

        try:
            # 发送请求
            response = requests.get(api_url, timeout=10)
            response.raise_for_status()

            # 检查响应内容
            response_text = response.text
            if debug:
                print(f"       📄 响应内容 (前200字符): {response_text[:200]}")

            # 尝试解析JSON响应
            try:
                chatlog_data = response.json()
                return chatlog_data, None
            except json.JSONDecodeError:
                # 如果不是JSON，返回原始文本用于调试
                return None, f"响应不是有效的JSON: {response_text[:100]}"

        except requests.exceptions.Timeout:
            return None, "请求超时"
        except requests.exceptions.ConnectionError:
            return None, "连接失败"
        except requests.exceptions.HTTPError as e:
            return None, f"HTTP错误: {e}"
        except Exception as e:
            return None, f"未知错误: {e}"
    
    def process_user(self, user_folder):
        """处理单个用户的所有符合条件的联系人"""
        username = user_folder.name
        print(f"\n👤 处理用户: {username}")
        print("-" * 50)
        
        # 获取联系人数据和IP地址
        contacts, ip_address, filename = self.get_user_contacts_and_ip(user_folder)
        if not contacts:
            return
        
        print(f"   📄 文件: {filename}")
        print(f"   🌐 IP地址: {ip_address}")
        
        # 筛选符合条件的联系人
        remarked_contacts = []
        for contact in contacts:
            remark = contact.get("Remark", "")
            if remark is None:
                remark = ""
            else:
                remark = str(remark).strip()

            if remark and self.remark_pattern.match(remark):
                remarked_contacts.append(contact)
        
        if not remarked_contacts:
            print(f"   📝 没有找到符合条件的联系人")
            return
        
        print(f"   ✅ 找到 {len(remarked_contacts)} 个符合条件的联系人")
        
        # 创建用户文件夹
        user_output_dir = self.output_dir / username
        user_output_dir.mkdir(exist_ok=True)
        
        # 准备聊天记录数据
        all_chatlogs = {
            "user": username,
            "ip_address": ip_address,
            "fetch_time": datetime.now().isoformat(),
            "time_range": f"2023-01-01 ~ {self.current_date}",
            "total_contacts": len(remarked_contacts),
            "successful_fetches": 0,
            "failed_fetches": 0,
            "chatlogs": []
        }
        
        # 为每个联系人获取聊天记录
        for i, contact in enumerate(remarked_contacts, 1):
            remark = contact.get("Remark", "")
            nickname = contact.get("NickName", "无昵称")
            talker = self.get_talker_value(contact)

            print(f"   {i:3d}/{len(remarked_contacts)} 获取 {remark} ({nickname}) 的聊天记录...")

            chatlog_data, error = self.fetch_chatlog_for_contact(ip_address, contact)

            contact_chatlog = {
                "contact_info": {
                    "UserName": contact.get("UserName", ""),
                    "Alias": contact.get("Alias", ""),
                    "Remark": contact.get("Remark", ""),
                    "NickName": contact.get("NickName", "")
                },
                "talker_used": talker,
                "fetch_success": chatlog_data is not None,
                "fetch_time": datetime.now().isoformat(),
                "chatlog_data": chatlog_data,
                "error": error
            }

            all_chatlogs["chatlogs"].append(contact_chatlog)

            if chatlog_data is not None:
                all_chatlogs["successful_fetches"] += 1
                print(f"       ✅ 成功，获得 {len(chatlog_data) if isinstance(chatlog_data, list) else 1} 条记录")
            else:
                all_chatlogs["failed_fetches"] += 1
                print(f"       ❌ 失败: {error}")

            # 实时保存进度（每处理5个联系人保存一次）
            if i % 5 == 0 or i == len(remarked_contacts):
                self.save_chatlog_data(user_output_dir, username, all_chatlogs, is_final=(i == len(remarked_contacts)))

            # 添加延迟避免请求过于频繁
            time.sleep(0.5)
        
        # 最终保存（如果还没有保存过）
        if not hasattr(self, '_final_saved') or not self._final_saved:
            self.save_chatlog_data(user_output_dir, username, all_chatlogs, is_final=True)

    def save_chatlog_data(self, user_output_dir, username, all_chatlogs, is_final=False):
        """实时保存聊天记录数据"""
        output_file = user_output_dir / f"chatlog_{username}.json"
        temp_file = user_output_dir / f"chatlog_{username}_temp.json"

        try:
            # 更新统计信息
            all_chatlogs["last_updated"] = datetime.now().isoformat()
            all_chatlogs["is_complete"] = is_final

            # 先写入临时文件
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(all_chatlogs, f, ensure_ascii=False, indent=2)

            # 原子性替换
            if temp_file.exists():
                if output_file.exists():
                    output_file.unlink()
                temp_file.rename(output_file)

            if is_final:
                print(f"   💾 聊天记录已保存到: {output_file}")
                print(f"   📊 成功: {all_chatlogs['successful_fetches']}, 失败: {all_chatlogs['failed_fetches']}")
                self._final_saved = True
            else:
                print(f"   💾 进度已保存 ({all_chatlogs['successful_fetches'] + all_chatlogs['failed_fetches']}/{all_chatlogs['total_contacts']})")

        except Exception as e:
            print(f"   ❌ 保存文件失败: {e}")
            # 清理临时文件
            if temp_file.exists():
                temp_file.unlink()
    
    def run(self):
        """运行聊天记录获取程序"""
        print("🔍 开始获取已备注人员的聊天记录...")
        print(f"📅 时间范围: 2023-01-01 ~ {self.current_date}")
        print("="*60)
        
        if not self.data_dir.exists():
            print("❌ data目录不存在")
            return
        
        # 获取所有用户文件夹
        user_folders = [d for d in self.data_dir.iterdir() 
                       if d.is_dir() and d.name not in self.excluded_folders]
        
        if not user_folders:
            print("❌ 没有找到用户文件夹")
            return
        
        print(f"📂 找到 {len(user_folders)} 个用户文件夹")
        
        # 处理每个用户
        total_processed = 0
        for user_folder in sorted(user_folders):
            try:
                self.process_user(user_folder)
                total_processed += 1
            except Exception as e:
                print(f"❌ 处理用户 {user_folder.name} 时发生错误: {e}")
        
        print("\n" + "="*60)
        print(f"🎉 处理完成! 共处理 {total_processed} 个用户")
        print(f"📁 聊天记录已保存在各用户文件夹中")

def main():
    """主函数"""
    print("📱 已备注人员聊天记录获取工具")
    print("="*60)
    
    fetcher = ChatlogFetcher()
    
    # 确认是否开始
    confirm = input("⚠️  即将开始获取聊天记录，这可能需要较长时间。确认开始？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 已取消")
        return
    
    fetcher.run()

if __name__ == "__main__":
    main()
