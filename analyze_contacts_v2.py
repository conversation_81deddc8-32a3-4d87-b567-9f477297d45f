"""
重写版本：分析各用户文件夹中的微信联系人重复情况
适配新的文件夹结构：data/用户名/联系人文件.json
"""

import json
import os
import csv
from collections import defaultdict
from datetime import datetime
from pathlib import Path
import logging

class ContactAnalyzerV2:
    def __init__(self, data_dir="data", reports_dir="analysis_reports"):
        self.data_dir = Path(data_dir)
        self.reports_dir = Path(reports_dir)
        self.user_contacts = {}  # {用户名: {文件名: [联系人列表]}}
        self.all_contacts_index = defaultdict(list)  # {联系人标识: [(用户名, 文件名, 联系人数据)]}
        self.duplicate_contacts = {}  # 重复联系人数据
        
        # 创建报告目录
        self.reports_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        log_file = self.reports_dir / "analysis.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def scan_user_folders(self):
        """扫描所有用户文件夹"""
        self.logger.info("开始扫描用户文件夹...")

        if not self.data_dir.exists():
            self.logger.error(f"数据目录不存在: {self.data_dir}")
            return False

        # 排除特定文件夹
        excluded_folders = {"test_ip_change_user"}
        all_folders = [d for d in self.data_dir.iterdir() if d.is_dir()]
        user_folders = [d for d in all_folders if d.name not in excluded_folders]

        # 记录排除的文件夹
        excluded_found = [d for d in all_folders if d.name in excluded_folders]
        if excluded_found:
            self.logger.info(f"排除文件夹: {[d.name for d in excluded_found]}")

        if not user_folders:
            self.logger.warning("没有找到用户文件夹")
            return False

        self.logger.info(f"找到 {len(user_folders)} 个用户文件夹")
        
        total_files = 0
        total_contacts = 0
        
        for user_folder in user_folders:
            username = user_folder.name
            self.logger.info(f"处理用户: {username}")
            
            # 查找联系人JSON文件（排除日志文件）
            contact_files = [f for f in user_folder.glob("*.json") 
                           if not f.name.startswith(("new_contacts", "ip_change"))]
            
            if not contact_files:
                self.logger.warning(f"用户 {username} 没有联系人文件")
                continue
                
            self.user_contacts[username] = {}
            
            for contact_file in contact_files:
                try:
                    with open(contact_file, 'r', encoding='utf-8') as f:
                        contacts = json.load(f)
                    
                    if isinstance(contacts, list):
                        self.user_contacts[username][contact_file.name] = contacts
                        total_files += 1
                        total_contacts += len(contacts)

                        # 建立联系人索引并获取个人联系人数量
                        personal_count = self.build_contact_index(username, contact_file.name, contacts)

                        self.logger.info(f"  {contact_file.name}: {len(contacts)} 个联系人 (个人联系人: {personal_count})")
                    else:
                        self.logger.warning(f"  {contact_file.name}: 格式错误，跳过")
                        
                except Exception as e:
                    self.logger.error(f"  读取 {contact_file.name} 失败: {str(e)}")
        
        # 计算个人联系人总数
        total_personal_contacts = sum(
            len([c for c in contacts if self.is_personal_contact(c)])
            for files in self.user_contacts.values()
            for contacts in files.values()
        )

        self.logger.info(f"扫描完成: {len(self.user_contacts)} 个用户, {total_files} 个文件")
        self.logger.info(f"总联系人: {total_contacts} 个, 个人联系人: {total_personal_contacts} 个")
        return True
        
    def is_personal_contact(self, contact):
        """判断是否为个人联系人（排除群聊、公众号等）"""
        username = contact.get("UserName", "")
        nickname = contact.get("NickName", "")

        # 排除系统账号
        system_accounts = {
            "filehelper", "weixin", "fmessage", "floatbottle", "medianote",
            "notifymessage", "tmessage", "qmessage", "qqmail", "wxitil",
            "brandcontact", "helper_entry", "pc_share", "newsapp"
        }

        if username in system_accounts:
            return False

        # 排除明显的企业/服务账号昵称模式
        if nickname:
            # 包含公司、企业等明显标识
            if any(pattern in nickname for pattern in [
                "有限公司", "股份有限", "集团", "企业", "公司-", "工作室", "工作号",
                "行政人事", "管理部", "客服", "服务", "售后", "登报服务",
                "生产商", "制造", "厂家", "供应商", "AIST", "智能"
            ]):
                return False

            # 包含特殊符号的可能是装饰性昵称，但某些模式明显是企业
            if any(pattern in nickname for pattern in ["【", "】", "®", "™", "©"]):
                # 但是如果只是装饰性的个人昵称，可能需要更细致的判断
                # 这里先保守一些，只过滤明显的企业标识
                if any(corp_word in nickname for corp_word in ["公司", "企业", "管理", "服务"]):
                    return False

        # 排除群聊（通常以@chatroom结尾）
        if username.endswith("@chatroom"):
            return False

        # 排除公众号（通常以gh_开头）
        if username.startswith("gh_"):
            return False

        # 排除企业微信群聊（通常包含@openim）
        if "@openim" in username:
            return False

        # 排除长数字ID（可能是系统生成的）
        if username.isdigit() and len(username) > 8:
            return False

        # 排除包含特殊企业标识的UserName
        if any(pattern in username for pattern in ["@stranger", "v3_020b"]):
            return False

        # 排除一些明显的非个人联系人关键词
        non_personal_keywords = [
            # 基础过滤
            "公众号", "订阅号", "服务号", "小程序", "群聊", "微信群",
            "客服", "官方", "通知", "助手", "机器人", "bot",
            # 企业相关
            "公司", "企业", "工作号", "工作室", "有限公司", "股份", "集团",
            "Corp", "Company", "Ltd", "Inc", "Co.", "Enterprise",
            # 服务相关
            "服务", "支持", "帮助", "售后", "客服", "service", "support",
            # 管理相关
            "管理", "行政", "人事", "admin", "管理部", "行政部",
            # 系统相关
            "系统", "平台", "中心", "智能", "自动", "AI",
            # 其他
            "生产商", "制造", "厂家", "供应商"
        ]

        if nickname:  # 确保nickname不为None
            nickname_lower = nickname.lower()
            for keyword in non_personal_keywords:
                if keyword in nickname or keyword in nickname_lower:
                    return False

        # 检查别名中的企业标识
        alias = contact.get("Alias", "")
        if alias:
            alias_lower = alias.lower()
            enterprise_alias_keywords = [
                "corp", "company", "ltd", "inc", "admin", "service", "support",
                "工作", "公司", "企业", "管理", "客服", "服务"
            ]
            for keyword in enterprise_alias_keywords:
                if keyword in alias_lower:
                    return False

        return True

    def build_contact_index(self, username, filename, contacts):
        """建立联系人索引"""
        personal_count = 0
        total_count = len(contacts)

        for contact in contacts:
            # 只处理个人联系人
            if self.is_personal_contact(contact):
                contact_key = self.generate_contact_key(contact)
                if contact_key:
                    self.all_contacts_index[contact_key].append((username, filename, contact))
                    personal_count += 1

        filtered_count = total_count - personal_count
        if filtered_count > 0:
            self.logger.info(f"    过滤掉 {filtered_count} 个非个人联系人")

        return personal_count
                
    def generate_contact_key(self, contact):
        """生成联系人的唯一标识符"""
        def safe_get(field_name):
            value = contact.get(field_name)
            return value.strip() if value and isinstance(value, str) else ""
            
        # 使用UserName作为主要标识符
        username = safe_get("UserName")
        if username:
            return username
            
        # 如果没有UserName，尝试使用其他字段组合
        nickname = safe_get("NickName")
        alias = safe_get("Alias")
        remark = safe_get("Remark")
        
        # 创建组合键
        key_parts = []
        if nickname:
            key_parts.append(f"nick:{nickname}")
        if alias:
            key_parts.append(f"alias:{alias}")
        if remark:
            key_parts.append(f"remark:{remark}")
            
        return "|".join(key_parts) if key_parts else None
        
    def analyze_duplicates(self):
        """分析重复联系人"""
        self.logger.info("开始分析重复联系人...")
        
        duplicate_count = 0
        
        for contact_key, appearances in self.all_contacts_index.items():
            if len(appearances) > 1:  # 出现在多个地方
                # 分析出现的用户和文件
                users = set()
                files = set()
                
                for username, filename, contact_data in appearances:
                    users.add(username)
                    files.add(f"{username}/{filename}")
                
                self.duplicate_contacts[contact_key] = {
                    'contact_data': appearances[0][2],  # 取第一个作为代表
                    'appearances': appearances,
                    'user_count': len(users),
                    'file_count': len(appearances),
                    'users': list(users),
                    'files': list(files)
                }
                
                duplicate_count += 1
        
        self.logger.info(f"发现 {duplicate_count} 个重复联系人")
        return duplicate_count
        
    def generate_reports(self):
        """生成分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. 生成详细JSON报告
        self.generate_json_report(timestamp)
        
        # 2. 生成CSV摘要报告
        self.generate_csv_summary(timestamp)
        
        # 3. 生成用户对比报告
        self.generate_user_comparison_report(timestamp)
        
        # 4. 生成统计报告
        self.generate_statistics_report(timestamp)
        
        self.logger.info("所有报告已生成完成")
        
    def generate_json_report(self, timestamp):
        """生成详细的JSON报告"""
        report_file = self.reports_dir / f"duplicate_analysis_{timestamp}.json"
        
        # 计算统计数据
        total_all_contacts = sum(
            sum(len(contacts) for contacts in files.values())
            for files in self.user_contacts.values()
        )

        total_personal_contacts = sum(
            len([c for c in contacts if self.is_personal_contact(c)])
            for files in self.user_contacts.values()
            for contacts in files.values()
        )

        report_data = {
            "analysis_info": {
                "timestamp": datetime.now().isoformat(),
                "analysis_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_users": len(self.user_contacts),
                "total_all_contacts": total_all_contacts,
                "total_personal_contacts": total_personal_contacts,
                "total_duplicate_contacts": len(self.duplicate_contacts),
                "duplicate_rate": f"{(len(self.duplicate_contacts) / total_personal_contacts * 100):.1f}%" if total_personal_contacts > 0 else "0%"
            },
            "user_summary": {},
            "duplicate_contacts": []
        }

        # 用户摘要
        for username, files in self.user_contacts.items():
            total_contacts = sum(len(contacts) for contacts in files.values())
            personal_contacts = sum(
                len([c for c in contacts if self.is_personal_contact(c)])
                for contacts in files.values()
            )

            report_data["user_summary"][username] = {
                "files_count": len(files),
                "total_contacts": total_contacts,
                "personal_contacts": personal_contacts,
                "personal_rate": f"{(personal_contacts / total_contacts * 100):.1f}%" if total_contacts > 0 else "0%",
                "files": list(files.keys())
            }
        
        # 重复联系人详情
        for contact_key, duplicate_info in self.duplicate_contacts.items():
            contact_data = duplicate_info['contact_data']
            
            duplicate_entry = {
                "contact_key": contact_key,
                "username": contact_data.get("UserName", ""),
                "nickname": contact_data.get("NickName", ""),
                "alias": contact_data.get("Alias", ""),
                "remark": contact_data.get("Remark", ""),
                "appears_in_users": duplicate_info['users'],
                "appears_in_files": duplicate_info['files'],
                "user_count": duplicate_info['user_count'],
                "file_count": duplicate_info['file_count'],
                "full_contact_data": contact_data,
                "all_appearances": [
                    {
                        "user": username,
                        "file": filename,
                        "contact": contact
                    }
                    for username, filename, contact in duplicate_info['appearances']
                ]
            }
            
            report_data["duplicate_contacts"].append(duplicate_entry)
        
        # 按用户数量排序
        report_data["duplicate_contacts"].sort(key=lambda x: x["user_count"], reverse=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"JSON报告已生成: {report_file}")
        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {str(e)}")
            
    def generate_csv_summary(self, timestamp):
        """生成CSV摘要报告"""
        csv_file = self.reports_dir / f"duplicate_summary_{timestamp}.csv"
        
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                
                # 写入表头
                writer.writerow([
                    "联系人标识", "用户名", "昵称", "别名", "备注",
                    "出现用户数", "出现文件数", "出现的用户", "出现的文件"
                ])
                
                # 按用户数量排序
                sorted_duplicates = sorted(
                    self.duplicate_contacts.items(),
                    key=lambda x: x[1]['user_count'],
                    reverse=True
                )
                
                for contact_key, duplicate_info in sorted_duplicates:
                    contact_data = duplicate_info['contact_data']
                    
                    writer.writerow([
                        contact_key,
                        contact_data.get("UserName", ""),
                        contact_data.get("NickName", ""),
                        contact_data.get("Alias", ""),
                        contact_data.get("Remark", ""),
                        duplicate_info['user_count'],
                        duplicate_info['file_count'],
                        "; ".join(duplicate_info['users']),
                        "; ".join(duplicate_info['files'])
                    ])
            
            self.logger.info(f"CSV摘要已生成: {csv_file}")
        except Exception as e:
            self.logger.error(f"生成CSV摘要失败: {str(e)}")
            
    def generate_user_comparison_report(self, timestamp):
        """生成用户对比报告"""
        report_file = self.reports_dir / f"user_comparison_{timestamp}.csv"
        
        try:
            with open(report_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                
                # 写入表头
                users = list(self.user_contacts.keys())
                header = ["联系人"] + users + ["总出现次数"]
                writer.writerow(header)
                
                # 为每个重复联系人创建对比行
                for contact_key, duplicate_info in self.duplicate_contacts.items():
                    contact_data = duplicate_info['contact_data']
                    nickname = contact_data.get("NickName", contact_key)
                    
                    row = [nickname]
                    
                    # 检查每个用户是否有这个联系人
                    for user in users:
                        has_contact = user in duplicate_info['users']
                        row.append("✓" if has_contact else "")
                    
                    # 总出现次数
                    row.append(duplicate_info['user_count'])
                    
                    writer.writerow(row)
            
            self.logger.info(f"用户对比报告已生成: {report_file}")
        except Exception as e:
            self.logger.error(f"生成用户对比报告失败: {str(e)}")
            
    def generate_statistics_report(self, timestamp):
        """生成统计报告"""
        report_file = self.reports_dir / f"statistics_{timestamp}.txt"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("微信联系人重复分析统计报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # 计算统计数据
                total_files = sum(len(files) for files in self.user_contacts.values())
                total_all_contacts = sum(
                    sum(len(contacts) for contacts in files.values())
                    for files in self.user_contacts.values()
                )
                total_personal_contacts = sum(
                    len([c for c in contacts if self.is_personal_contact(c)])
                    for files in self.user_contacts.values()
                    for contacts in files.values()
                )

                # 基本统计
                f.write("基本统计:\n")
                f.write(f"  总用户数: {len(self.user_contacts)}\n")
                f.write(f"  总文件数: {total_files}\n")
                f.write(f"  总联系人数: {total_all_contacts}\n")
                f.write(f"  个人联系人数: {total_personal_contacts}\n")
                f.write(f"  重复个人联系人数: {len(self.duplicate_contacts)}\n")

                if total_personal_contacts > 0:
                    duplicate_rate = (len(self.duplicate_contacts) / total_personal_contacts) * 100
                    f.write(f"  重复率: {duplicate_rate:.1f}%\n")

                f.write("\n")
                
                # 用户详情
                f.write("用户详情:\n")
                for username, files in self.user_contacts.items():
                    user_total = sum(len(contacts) for contacts in files.values())
                    user_personal = sum(
                        len([c for c in contacts if self.is_personal_contact(c)])
                        for contacts in files.values()
                    )
                    personal_rate = (user_personal / user_total * 100) if user_total > 0 else 0
                    f.write(f"  {username}: {len(files)} 个文件, {user_total} 个联系人 (个人联系人: {user_personal}, {personal_rate:.1f}%)\n")
                f.write("\n")
                
                # 重复度最高的联系人
                f.write("重复度最高的联系人 (前10名):\n")
                sorted_duplicates = sorted(
                    self.duplicate_contacts.items(),
                    key=lambda x: x[1]['user_count'],
                    reverse=True
                )
                
                for i, (contact_key, duplicate_info) in enumerate(sorted_duplicates[:10], 1):
                    contact_data = duplicate_info['contact_data']
                    nickname = contact_data.get("NickName", "无昵称")
                    username = contact_data.get("UserName", "")
                    
                    f.write(f"  {i:2d}. {nickname} ({username})\n")
                    f.write(f"      出现在 {duplicate_info['user_count']} 个用户中\n")
                    f.write(f"      用户: {', '.join(duplicate_info['users'])}\n\n")
            
            self.logger.info(f"统计报告已生成: {report_file}")
        except Exception as e:
            self.logger.error(f"生成统计报告失败: {str(e)}")
            
    def run_analysis(self):
        """运行完整分析"""
        self.logger.info("开始微信联系人重复分析...")
        
        # 1. 扫描用户文件夹
        if not self.scan_user_folders():
            return False
        
        # 2. 分析重复联系人
        duplicate_count = self.analyze_duplicates()
        
        if duplicate_count == 0:
            self.logger.info("没有发现重复联系人")
            return True
        
        # 3. 生成报告
        self.generate_reports()
        
        self.logger.info("分析完成！")
        return True

def main():
    analyzer = ContactAnalyzerV2()
    
    try:
        success = analyzer.run_analysis()
        if success:
            print(f"\n✅ 分析完成！报告已保存到: {analyzer.reports_dir}")
        else:
            print("\n❌ 分析失败！")
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
