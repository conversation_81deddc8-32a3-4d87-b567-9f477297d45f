# 重复联系人查找工具

这是一个Python脚本，用于分析data目录中所有JSON文件，找出在多个文件中都出现的重复联系人。

## 功能特性

- 🔍 扫描data目录中的所有JSON文件
- 📊 识别在多个文件中出现的重复联系人
- 💾 生成详细的分析报告（JSON和CSV格式）
- 📈 提供统计摘要和排序结果
- 🛡️ 安全处理数据中的空值和异常情况

## 安装和运行

### 方法1: 使用批处理文件（推荐）
```bash
# 双击运行
run_duplicate_finder.bat
```

### 方法2: 手动运行
```bash
# 直接运行Python脚本
python find_duplicate_contacts.py
```

## 工作原理

### 1. 数据加载
- 扫描`data`目录中的所有`.json`文件
- 加载每个文件中的联系人列表
- 统计总文件数和联系人数

### 2. 重复检测
- 使用`UserName`字段作为主要标识符
- 如果`UserName`为空，则使用昵称、别名、备注的组合
- 建立联系人索引，记录每个联系人出现的文件

### 3. 结果分析
- 识别出现在2个或更多文件中的联系人
- 按出现次数排序
- 生成详细报告

## 输出文件

### JSON报告文件
文件名格式: `duplicate_contacts_YYYYMMDD_HHMMSS.json`

包含内容:
- 扫描时间和统计信息
- 每个重复联系人的详细信息
- 出现的文件列表
- 完整的联系人数据

### CSV报告文件
文件名格式: `duplicate_contacts_YYYYMMDD_HHMMSS.csv`

包含列:
- 联系人标识
- 用户名
- 昵称
- 别名
- 备注
- 出现次数
- 出现的文件列表

## 示例输出

```
============================================================
重复联系人分析摘要
============================================================
扫描的文件数量: 3
总联系人数量: 22760
重复联系人数量: 22

重复次数最多的联系人:
 1. 文件传输助手 (filehelper)
    出现次数: 3
    出现文件: 127_0_0_1_胡贵稳.json, 192_168_1_191_用户_安妮.json, 192_168_1_191_用户_小郭.json

 2. 漂流瓶 (floatbottle)
    出现次数: 3
    出现文件: 127_0_0_1_胡贵稳.json, 192_168_1_191_用户_安妮.json, 192_168_1_191_用户_小郭.json
```

## 使用场景

### 1. 联系人去重
- 识别系统默认联系人（如文件传输助手、微信团队等）
- 找出真正的共同联系人
- 分析用户间的联系人重叠情况

### 2. 数据分析
- 统计最常见的联系人
- 分析用户群体的共同特征
- 识别可能的机器人或公众号账户

### 3. 数据清理
- 为数据去重提供依据
- 识别需要特殊处理的系统账户
- 优化存储空间

## 技术特点

### 安全性
- 处理None值和空字符串
- 异常处理和错误恢复
- 文件读取错误的容错机制

### 性能
- 高效的字典索引算法
- 内存友好的数据处理
- 支持大量联系人数据

### 可扩展性
- 易于修改联系人匹配规则
- 支持添加新的输出格式
- 可自定义分析逻辑

## 配置选项

### 修改数据目录
```python
finder = ContactDuplicateFinder(data_dir="your_data_directory")
```

### 自定义联系人匹配规则
修改`generate_contact_key`方法来改变联系人的匹配逻辑。

## 常见问题

### Q: 为什么某些联系人没有被识别为重复？
A: 可能是因为联系人的关键字段（UserName、昵称等）不完全匹配。可以检查原始数据或修改匹配规则。

### Q: 如何处理大量文件？
A: 脚本已经优化了内存使用，可以处理大量文件。如果遇到性能问题，可以分批处理。

### Q: 输出文件在哪里？
A: 输出文件会生成在脚本运行的当前目录中，文件名包含时间戳。

## 文件结构

```
├── find_duplicate_contacts.py     # 主脚本文件
├── run_duplicate_finder.bat       # Windows启动脚本
├── DUPLICATE_FINDER_README.md     # 本说明文件
├── data/                          # 联系人数据目录
│   ├── 127_0_0_1_胡贵稳.json
│   ├── 192_168_1_191_用户_安妮.json
│   └── 192_168_1_191_用户_小郭.json
└── duplicate_contacts_*.json      # 生成的报告文件
└── duplicate_contacts_*.csv       # 生成的CSV报告
```

## 注意事项

1. **数据隐私**: 请确保在处理敏感联系人数据时遵守相关隐私法规
2. **文件权限**: 确保脚本有读取data目录和写入当前目录的权限
3. **数据备份**: 建议在运行分析前备份原始数据文件

## 故障排除

### 常见错误

1. **"目录不存在"错误**
   - 确保data目录存在
   - 检查目录路径是否正确

2. **"没有找到JSON文件"错误**
   - 确保data目录中有.json文件
   - 检查文件扩展名是否正确

3. **"加载文件失败"错误**
   - 检查JSON文件格式是否正确
   - 确保文件没有被其他程序占用

如有其他问题，请查看控制台输出的详细错误信息。
