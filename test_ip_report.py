#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time
import random

def test_ip_report_api():
    """测试IP地址报告接口"""
    print("🌐 测试IP地址报告接口")
    print("=" * 50)
    
    # 测试数据
    test_cases = [
        {
            "name": "正常IP报告",
            "data": {
                "username": "test_user_1",
                "ip": "*************"
            }
        },
        {
            "name": "另一个用户的IP报告",
            "data": {
                "username": "test_user_2", 
                "ip": "*********"
            }
        },
        {
            "name": "公网IP报告",
            "data": {
                "username": "remote_user",
                "ip": "************"
            }
        }
    ]
    
    api_url = "http://127.0.0.1:8080/report_ip"
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"👤 用户名: {test_case['data']['username']}")
        print(f"🌐 IP地址: {test_case['data']['ip']}")
        
        try:
            response = requests.post(
                api_url,
                json=test_case['data'],
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ IP报告提交成功!")
                print(f"📄 响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                success_count += 1
            else:
                print(f"❌ IP报告提交失败: {response.status_code}")
                try:
                    error_result = response.json()
                    print(f"📄 错误内容:")
                    print(json.dumps(error_result, indent=2, ensure_ascii=False))
                except:
                    print(f"📄 错误内容: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        
        # 添加延迟避免请求过快
        if i < len(test_cases):
            time.sleep(1)
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个测试通过")
    return success_count == len(test_cases)

def test_ip_history_api():
    """测试IP历史记录查询接口"""
    print("\n📚 测试IP历史记录查询接口")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:8080/api/ip_history"
    
    test_cases = [
        {
            "name": "查询所有记录",
            "params": {"limit": 10}
        },
        {
            "name": "按用户名过滤",
            "params": {"username": "test_user_1", "limit": 5}
        },
        {
            "name": "查询更多记录",
            "params": {"limit": 50}
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"📊 查询参数: {test_case['params']}")
        
        try:
            response = requests.get(
                api_url,
                params=test_case['params'],
                timeout=10
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 历史记录查询成功!")
                
                if result['status'] == 'success':
                    data = result['data']
                    print(f"📄 查询结果:")
                    print(f"   总记录数: {data['total_count']}")
                    print(f"   用户名过滤: {data['username_filter'] or '无'}")
                    print(f"   查询限制: {data['limit']}")
                    print(f"   返回记录数: {len(data['records'])}")
                    
                    # 显示前几条记录
                    if data['records']:
                        print(f"   最新记录:")
                        for j, record in enumerate(data['records'][:3]):
                            print(f"     {j+1}. {record['username']} -> {record['ip_address']} ({record['timestamp']})")
                    
                    success_count += 1
                else:
                    print(f"❌ 查询失败: {result['message']}")
            else:
                print(f"❌ 查询失败: {response.status_code}")
                print(f"📄 错误内容: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(0.5)
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个测试通过")
    return success_count == len(test_cases)

def test_error_cases():
    """测试错误情况处理"""
    print("\n🚨 测试错误情况处理")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:8080/report_ip"
    
    error_cases = [
        {
            "name": "缺少用户名",
            "data": {"ip": "***********"},
            "expected_error": "缺少用户名参数"
        },
        {
            "name": "缺少IP地址",
            "data": {"username": "test_user"},
            "expected_error": "缺少IP地址参数"
        },
        {
            "name": "空用户名",
            "data": {"username": "", "ip": "***********"},
            "expected_error": "缺少用户名参数"
        },
        {
            "name": "空IP地址",
            "data": {"username": "test_user", "ip": ""},
            "expected_error": "缺少IP地址参数"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(error_cases, 1):
        print(f"\n📋 错误测试 {i}: {test_case['name']}")
        print(f"📊 测试数据: {test_case['data']}")
        
        try:
            response = requests.post(
                api_url,
                json=test_case['data'],
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 400:  # 期望的错误状态码
                result = response.json()
                if test_case['expected_error'] in result.get('message', ''):
                    print("✅ 错误处理正确!")
                    success_count += 1
                else:
                    print(f"❌ 错误消息不匹配")
                    print(f"   期望包含: {test_case['expected_error']}")
                    print(f"   实际消息: {result.get('message', '')}")
            else:
                print(f"❌ 期望状态码400，实际: {response.status_code}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(0.5)
    
    print(f"\n📊 错误测试结果: {success_count}/{len(error_cases)} 个测试通过")
    return success_count == len(error_cases)

def simulate_sendip_client():
    """模拟SendIp.py客户端的行为"""
    print("\n🤖 模拟SendIp.py客户端行为")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:8080/report_ip"
    
    # 模拟不同用户的IP变化
    users = ["user_home", "user_office", "user_mobile"]
    ip_ranges = [
        ["192.168.1.{}".format(i) for i in range(100, 110)],
        ["10.0.0.{}".format(i) for i in range(50, 60)],
        ["172.16.1.{}".format(i) for i in range(20, 30)]
    ]
    
    print("🔄 开始模拟IP变化报告...")
    
    success_count = 0
    total_reports = 0
    
    for round_num in range(1, 4):  # 模拟3轮IP变化
        print(f"\n🔄 第 {round_num} 轮IP变化:")
        
        for i, username in enumerate(users):
            # 随机选择一个IP
            new_ip = random.choice(ip_ranges[i])
            
            data = {
                "username": username,
                "ip": new_ip
            }
            
            print(f"   📡 {username} -> {new_ip}")
            
            try:
                response = requests.post(
                    api_url,
                    json=data,
                    timeout=10,
                    headers={'Content-Type': 'application/json'}
                )
                
                total_reports += 1
                
                if response.status_code == 200:
                    success_count += 1
                    print(f"      ✅ 报告成功")
                else:
                    print(f"      ❌ 报告失败: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                print(f"      ❌ 请求异常: {e}")
                total_reports += 1
            
            time.sleep(0.5)  # 模拟真实的时间间隔
        
        time.sleep(2)  # 轮次间隔
    
    print(f"\n📊 模拟结果: {success_count}/{total_reports} 个报告成功")
    return success_count == total_reports

def main():
    """主测试函数"""
    print("🚀 IP地址报告接口全面测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: IP报告接口
    print("\n🧪 测试1: IP地址报告接口")
    result1 = test_ip_report_api()
    test_results.append(("IP报告接口", result1))
    
    # 测试2: IP历史查询接口
    print("\n🧪 测试2: IP历史记录查询接口")
    result2 = test_ip_history_api()
    test_results.append(("IP历史查询", result2))
    
    # 测试3: 错误情况处理
    print("\n🧪 测试3: 错误情况处理")
    result3 = test_error_cases()
    test_results.append(("错误处理", result3))
    
    # 测试4: 模拟客户端行为
    print("\n🧪 测试4: 模拟SendIp.py客户端")
    result4 = simulate_sendip_client()
    test_results.append(("客户端模拟", result4))
    
    # 显示测试结果汇总
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！IP地址报告接口正常工作")
        print("\n💡 接下来可以:")
        print("1. 访问 http://127.0.0.1:8080/ip_monitor 查看监控页面")
        print("2. 修改 SendIp.py 中的服务器地址为: http://127.0.0.1:8080/report_ip")
        print("3. 运行 SendIp.py 开始实际的IP监控")
    else:
        print("⚠️ 部分测试失败，请检查服务器状态")
        print("\n💡 常见问题排查:")
        print("1. 确保Flask应用正在运行 (http://127.0.0.1:8080)")
        print("2. 检查防火墙和网络连接")
        print("3. 查看服务器日志获取详细错误信息")

if __name__ == "__main__":
    main()
