# 聊天记录增量更新工具

import json
import os
import re
import requests
import urllib.parse
from pathlib import Path
from datetime import datetime, timedelta
import time

class ChatlogUpdater:
    def __init__(self):
        self.data_dir = Path("../data")
        self.output_dir = Path(".")
        self.excluded_folders = {"test_ip_change_user"}
        self.remark_pattern = re.compile(r'^\d+.*$')
        
    def extract_ip_from_filename(self, filename):
        """从文件名中提取IP地址"""
        parts = filename.replace('.json', '').split('_')
        if len(parts) >= 4:
            ip_parts = parts[:4]
            ip_address = '.'.join(ip_parts)
            return ip_address
        return None
    
    def get_user_contacts_and_ip(self, user_folder):
        """获取用户的联系人数据和IP地址"""
        username = user_folder.name
        
        contact_files = [f for f in user_folder.glob("*.json") 
                        if not f.name.startswith(("new_contacts", "ip_change"))]
        
        if not contact_files:
            return None, None, None
        
        latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
        ip_address = self.extract_ip_from_filename(latest_file.name)
        
        if not ip_address:
            return None, None, None
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                contacts = json.load(f)
            
            if not isinstance(contacts, list):
                return None, None, None
            
            return contacts, ip_address, latest_file.name
            
        except Exception as e:
            print(f"   ❌ {username}: 读取联系人文件失败 - {e}")
            return None, None, None
    
    def get_talker_value(self, contact):
        """获取talker参数值"""
        for field in ['Remark', 'NickName', 'Alias', 'UserName']:
            value = contact.get(field, '')
            if value is None:
                value = ''
            else:
                value = str(value).strip()
            if value:
                return value
        return None
    
    def load_existing_chatlog(self, user_folder, username):
        """加载现有的聊天记录"""
        chatlog_file = user_folder / f"chatlog_{username}.json"
        
        if not chatlog_file.exists():
            return None
        
        try:
            with open(chatlog_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"   ❌ 读取现有聊天记录失败: {e}")
            return None
    
    def get_last_update_time(self, existing_chatlog, contact_username):
        """获取联系人的最后更新时间"""
        if not existing_chatlog or 'chatlogs' not in existing_chatlog:
            return "2023-01-01"
        
        # 查找该联系人的最后更新时间
        for chatlog_entry in existing_chatlog['chatlogs']:
            contact_info = chatlog_entry.get('contact_info', {})
            if contact_info.get('UserName') == contact_username:
                # 从聊天数据中找最新的消息时间
                chatlog_data = chatlog_entry.get('chatlog_data', [])
                if isinstance(chatlog_data, list) and chatlog_data:
                    # 假设聊天记录按时间排序，取最后一条的时间
                    last_msg = chatlog_data[-1]
                    msg_time = last_msg.get('timestamp', last_msg.get('time', ''))
                    if msg_time:
                        try:
                            # 转换时间格式
                            if 'T' in msg_time:
                                dt = datetime.fromisoformat(msg_time.replace('Z', '+00:00'))
                            else:
                                dt = datetime.strptime(msg_time, '%Y-%m-%d %H:%M:%S')
                            return dt.strftime('%Y-%m-%d')
                        except:
                            pass
                
                # 如果没有消息数据，使用fetch_time
                fetch_time = chatlog_entry.get('fetch_time', '')
                if fetch_time:
                    try:
                        dt = datetime.fromisoformat(fetch_time)
                        return dt.strftime('%Y-%m-%d')
                    except:
                        pass
        
        # 如果找不到，返回一周前
        week_ago = datetime.now() - timedelta(days=7)
        return week_ago.strftime('%Y-%m-%d')
    
    def fetch_incremental_chatlog(self, ip_address, contact, last_update_time):
        """获取增量聊天记录"""
        talker = self.get_talker_value(contact)
        if not talker:
            return None, "无有效talker参数"
        
        current_date = datetime.now().strftime("%Y-%m-%d")
        time_range = f"{last_update_time}%7E{current_date}"
        
        encoded_talker = urllib.parse.quote(talker)
        api_url = f"http://{ip_address}:5030/api/v1/chatlog?time={time_range}&talker={encoded_talker}&format=json"
        
        try:
            response = requests.get(api_url, timeout=10)
            response.raise_for_status()
            
            chatlog_data = response.json()
            return chatlog_data, None
            
        except requests.exceptions.Timeout:
            return None, "请求超时"
        except requests.exceptions.ConnectionError:
            return None, "连接失败"
        except requests.exceptions.HTTPError as e:
            return None, f"HTTP错误: {e}"
        except json.JSONDecodeError:
            return None, "响应不是有效的JSON"
        except Exception as e:
            return None, f"未知错误: {e}"
    
    def merge_chatlog_data(self, existing_entry, new_data):
        """合并聊天记录数据"""
        if not new_data or not isinstance(new_data, list):
            return existing_entry
        
        existing_data = existing_entry.get('chatlog_data', [])
        if not isinstance(existing_data, list):
            existing_data = []
        
        # 简单合并：将新数据追加到现有数据
        # TODO: 实现更智能的去重逻辑
        merged_data = existing_data + new_data
        
        # 更新条目
        existing_entry['chatlog_data'] = merged_data
        existing_entry['last_incremental_update'] = datetime.now().isoformat()
        existing_entry['incremental_messages_added'] = len(new_data)
        
        return existing_entry
    
    def update_user_chatlogs(self, username):
        """更新单个用户的聊天记录"""
        print(f"\n🔄 更新用户: {username}")
        print("-" * 50)
        
        user_folder = self.data_dir / username
        if not user_folder.exists():
            print(f"   ❌ 用户文件夹不存在")
            return False
        
        # 获取联系人数据和IP地址
        contacts, ip_address, filename = self.get_user_contacts_and_ip(user_folder)
        if not contacts:
            return False
        
        print(f"   📄 文件: {filename}")
        print(f"   🌐 IP地址: {ip_address}")
        
        # 筛选符合条件的联系人
        remarked_contacts = []
        for contact in contacts:
            remark = contact.get("Remark", "")
            if remark is None:
                remark = ""
            else:
                remark = str(remark).strip()
            
            if remark and self.remark_pattern.match(remark):
                remarked_contacts.append(contact)
        
        if not remarked_contacts:
            print(f"   📝 没有找到符合条件的联系人")
            return False
        
        print(f"   ✅ 找到 {len(remarked_contacts)} 个符合条件的联系人")
        
        # 创建用户输出目录
        user_output_dir = self.output_dir / username
        user_output_dir.mkdir(exist_ok=True)
        
        # 加载现有聊天记录
        existing_chatlog = self.load_existing_chatlog(user_output_dir, username)
        
        if existing_chatlog is None:
            print(f"   ⚠️  没有现有聊天记录，建议先运行完整获取")
            return False
        
        print(f"   📚 加载现有聊天记录，共 {len(existing_chatlog.get('chatlogs', []))} 个联系人")
        
        # 更新统计
        updated_count = 0
        new_messages_total = 0
        
        # 为每个联系人检查更新
        for i, contact in enumerate(remarked_contacts, 1):
            remark = contact.get("Remark", "")
            nickname = contact.get("NickName", "无昵称")
            username_field = contact.get("UserName", "")
            
            # 获取最后更新时间
            last_update_time = self.get_last_update_time(existing_chatlog, username_field)
            
            print(f"   {i:3d}/{len(remarked_contacts)} 检查 {remark} ({nickname}) - 自 {last_update_time}")
            
            # 获取增量数据
            new_data, error = self.fetch_incremental_chatlog(ip_address, contact, last_update_time)
            
            if new_data is not None and isinstance(new_data, list) and len(new_data) > 0:
                # 找到对应的聊天记录条目并更新
                for chatlog_entry in existing_chatlog['chatlogs']:
                    contact_info = chatlog_entry.get('contact_info', {})
                    if contact_info.get('UserName') == username_field:
                        self.merge_chatlog_data(chatlog_entry, new_data)
                        updated_count += 1
                        new_messages_total += len(new_data)
                        print(f"       ✅ 新增 {len(new_data)} 条消息")
                        break
                else:
                    print(f"       ⚠️  未找到对应的聊天记录条目")
            elif error:
                print(f"       ❌ 失败: {error}")
            else:
                print(f"       📝 无新消息")
            
            time.sleep(0.3)  # 减少请求频率
        
        # 保存更新后的聊天记录
        if updated_count > 0:
            existing_chatlog['last_incremental_update'] = datetime.now().isoformat()
            existing_chatlog['incremental_update_summary'] = {
                'updated_contacts': updated_count,
                'new_messages_total': new_messages_total,
                'update_time': datetime.now().isoformat()
            }
            
            output_file = user_output_dir / f"chatlog_{username}.json"
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_chatlog, f, ensure_ascii=False, indent=2)
                
                print(f"   💾 更新已保存: {updated_count} 个联系人，{new_messages_total} 条新消息")
                
            except Exception as e:
                print(f"   ❌ 保存失败: {e}")
                return False
        else:
            print(f"   📝 无更新需要保存")
        
        return True
    
    def update_all_users(self):
        """更新所有用户的聊天记录"""
        print("🔄 开始增量更新所有用户的聊天记录...")
        print(f"📅 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        if not self.data_dir.exists():
            print("❌ data目录不存在")
            return
        
        user_folders = [d for d in self.data_dir.iterdir() 
                       if d.is_dir() and d.name not in self.excluded_folders]
        
        if not user_folders:
            print("❌ 没有找到用户文件夹")
            return
        
        print(f"📂 找到 {len(user_folders)} 个用户文件夹")
        
        success_count = 0
        for user_folder in sorted(user_folders):
            try:
                if self.update_user_chatlogs(user_folder.name):
                    success_count += 1
            except Exception as e:
                print(f"❌ 处理用户 {user_folder.name} 时发生错误: {e}")
        
        print("\n" + "="*60)
        print(f"🎉 增量更新完成! 成功更新 {success_count}/{len(user_folders)} 个用户")

def main():
    """主函数"""
    print("📱 聊天记录增量更新工具")
    print("="*60)
    
    updater = ChatlogUpdater()
    
    import sys
    if len(sys.argv) > 1:
        username = sys.argv[1]
        print(f"🎯 更新指定用户: {username}")
        updater.update_user_chatlogs(username)
    else:
        confirm = input("⚠️  即将开始增量更新所有用户的聊天记录。确认开始？(y/N): ").strip().lower()
        if confirm == 'y':
            updater.update_all_users()
        else:
            print("❌ 已取消")

if __name__ == "__main__":
    main()
