#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试个性化标签生成
"""

import requests
import json

def test_different_contacts():
    """测试不同联系人生成不同标签"""
    print("🧪 测试个性化标签生成")
    print("=" * 60)
    
    # 测试数据1：技术人员
    tech_data = {
        "contact_info": {
            "id": "tech_001",
            "remark": "张工程师Java后端5年经验",
            "nickname": "TechZhang"
        },
        "messages": [
            {
                "type": "text",
                "content": "你好，我是一名Java后端开发工程师，有5年工作经验",
                "time": "2024-01-15 10:00:00",
                "isSelf": False
            },
            {
                "type": "text",
                "content": "我熟悉Spring、SpringBoot框架，希望找全职工作",
                "time": "2024-01-15 10:05:00",
                "isSelf": False
            },
            {
                "type": "text",
                "content": "期望年薪35万左右，我对这个职位很感兴趣",
                "time": "2024-01-15 10:10:00",
                "isSelf": False
            },
            {
                "type": "text",
                "content": "我可以推荐几个朋友，他们也在找工作",
                "time": "2024-01-15 10:15:00",
                "isSelf": False
            }
        ],
        "total_count": 4
    }
    
    # 测试数据2：产品经理
    pm_data = {
        "contact_info": {
            "id": "pm_002", 
            "remark": "李产品经理英国海归",
            "nickname": "ProductLi"
        },
        "messages": [
            {
                "type": "text",
                "content": "我是产品经理，刚从英国回来",
                "time": "2024-01-15 10:00:00",
                "isSelf": False
            },
            {
                "type": "text",
                "content": "有3年产品经验，希望找兼职或者远程工作",
                "time": "2024-01-15 10:05:00",
                "isSelf": False
            },
            {
                "type": "file",
                "content": "[文件] 我的简历.pdf",
                "time": "2024-01-15 10:10:00",
                "isSelf": False
            }
        ],
        "total_count": 3
    }
    
    # 测试数据3：教授
    prof_data = {
        "contact_info": {
            "id": "prof_003",
            "remark": "王教授清华大学长江学者",
            "nickname": "ProfWang"
        },
        "messages": [
            {
                "type": "text",
                "content": "我是清华大学的教授，长江学者",
                "time": "2024-01-15 10:00:00",
                "isSelf": False
            },
            {
                "type": "text",
                "content": "主要研究人工智能和机器学习",
                "time": "2024-01-15 10:05:00",
                "isSelf": False
            }
        ],
        "total_count": 2
    }
    
    test_cases = [
        ("技术人员", "tech_001", tech_data),
        ("产品经理", "pm_002", pm_data),
        ("教授", "prof_003", prof_data)
    ]
    
    results = []
    
    for name, contact_id, data in test_cases:
        print(f"\n🔍 测试 {name} (ID: {contact_id})")
        print(f"📝 备注: {data['contact_info']['remark']}")
        print(f"💬 消息数: {data['total_count']}")
        
        try:
            url = f"http://127.0.0.1:8080/api/auto_generate_tags/{contact_id}"
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    tags = result.get('tags', {})
                    comprehensive = tags.get('comprehensive', [])
                    
                    print(f"✅ 成功生成标签")
                    print(f"🏷️ 综合标签: {comprehensive}")
                    
                    # 显示分类标签
                    categories = [
                        ('judgment', '判断备注'),
                        ('manual', '人工检索'),
                        ('ai', 'AI对话'),
                        ('remark', '备注提取')
                    ]
                    
                    for key, cat_name in categories:
                        cat_tags = tags.get(key, [])
                        if cat_tags:
                            print(f"  - {cat_name}: {cat_tags}")
                    
                    results.append((name, comprehensive))
                else:
                    print(f"❌ 标签生成失败: {result.get('message')}")
                    results.append((name, []))
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误: {response.text}")
                results.append((name, []))
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append((name, []))
    
    # 分析结果
    print(f"\n📊 个性化标签分析")
    print("=" * 60)
    
    for i, (name1, tags1) in enumerate(results):
        for j, (name2, tags2) in enumerate(results):
            if i < j:  # 避免重复比较
                common_tags = set(tags1) & set(tags2)
                unique_tags1 = set(tags1) - set(tags2)
                unique_tags2 = set(tags2) - set(tags1)
                
                print(f"\n🔍 {name1} vs {name2}:")
                print(f"  共同标签: {list(common_tags) if common_tags else '无'}")
                print(f"  {name1}独有: {list(unique_tags1) if unique_tags1 else '无'}")
                print(f"  {name2}独有: {list(unique_tags2) if unique_tags2 else '无'}")
    
    # 检查个性化程度
    all_tags = [tags for _, tags in results if tags]
    if len(all_tags) >= 2:
        if all_tags[0] == all_tags[1]:
            print(f"\n⚠️ 警告: 不同联系人生成了相同的标签！")
        else:
            print(f"\n✅ 成功: 不同联系人生成了不同的个性化标签！")
    
    return results

def main():
    """主函数"""
    print("🚀 个性化标签测试开始")
    test_different_contacts()
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
