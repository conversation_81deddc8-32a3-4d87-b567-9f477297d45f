# Excel在线表聊天记录自动注入实现逻辑

## 📋 核心逻辑概述

### 🎯 目标
将聊天记录数据按照备注中的ID自动注入到Excel在线表的"备注"字段中，实现数据的结构化展示和管理。

### 🔍 数据流程
```
聊天记录JSON → ID提取 → 数据格式化 → Excel生成 → 在线表注入
```

## 🏗️ 实现架构

### 1. 数据提取层
```python
# 从备注中提取ID
def extract_id_from_remark(remark):
    # 示例: "16669李好" → "16669"
    # 示例: "10200 李博士" → "10200"
    match = re.match(r'^(\d+)', str(remark).strip())
    return match.group(1) if match else None
```

### 2. 数据映射层
```python
# 创建ID到聊天记录的映射
id_to_chatlog = {
    "16669": {
        "remark": "16669李好",
        "nickname": "李好",
        "chatlog": "07-08 14:30 | 你好\n07-08 14:31 | 在吗？",
        "message_count": 54
    }
}
```

### 3. 格式化层
```python
# 聊天记录格式化为Excel友好格式
def format_chatlog_for_excel(chatlog_data):
    formatted_messages = []
    for msg in chatlog_data[-50:]:  # 最近50条
        time_str = format_timestamp(msg['timestamp'])
        content = truncate_content(msg['content'], 100)
        formatted_msg = f"{time_str} | {content}"
        formatted_messages.append(formatted_msg)
    return "\n".join(formatted_messages)
```

## 📊 Excel表结构设计

### 表格列定义
| 列名 | 宽度 | 数据类型 | 说明 |
|------|------|----------|------|
| ID | 10 | 数字 | 从备注提取的数字ID |
| 备注 | 25 | 文本 | 完整的备注信息 |
| 昵称 | 15 | 文本 | 联系人昵称 |
| 消息数量 | 12 | 数字 | 聊天记录总数 |
| 聊天记录 | 80 | 长文本 | 格式化的聊天内容 |
| 最后更新 | 20 | 日期时间 | 数据更新时间 |

### 格式化规则
```python
# 聊天记录单元格格式
cell_format = {
    "wrap_text": True,           # 自动换行
    "vertical_align": "top",     # 顶部对齐
    "font_size": 9,              # 小字体节省空间
    "row_height": "auto"         # 自动行高
}
```

## 🌐 在线表注入方案

### 方案A: Microsoft Excel Online
```python
# 使用Microsoft Graph API
def inject_to_office365(data):
    # 1. 获取访问令牌
    token = get_access_token()
    
    # 2. 创建或更新工作簿
    workbook_id = create_or_get_workbook()
    
    # 3. 批量更新单元格
    for row, record in enumerate(data, 2):
        update_cell_range(workbook_id, f"A{row}:F{row}", record)
    
    return workbook_url
```

### 方案B: Google Sheets
```python
# 使用Google Sheets API
def inject_to_google_sheets(data):
    # 1. 认证和授权
    service = build('sheets', 'v4', credentials=creds)
    
    # 2. 创建新表格
    spreadsheet = service.spreadsheets().create(body={
        'properties': {'title': 'WeChat聊天记录'}
    }).execute()
    
    # 3. 批量写入数据
    values = [list(record.values()) for record in data]
    service.spreadsheets().values().update(
        spreadsheetId=spreadsheet['spreadsheetId'],
        range='A1:F1000',
        valueInputOption='RAW',
        body={'values': values}
    ).execute()
```

### 方案C: 腾讯文档
```python
# 使用腾讯文档API (如果可用)
def inject_to_tencent_docs(data):
    # 1. 获取API密钥
    # 2. 创建在线表格
    # 3. 批量导入数据
    pass
```

### 方案D: 通用CSV导入
```python
# 生成CSV文件供手动导入
def export_to_csv(data):
    df = pd.DataFrame(data)
    csv_file = f"chatlog_export_{timestamp}.csv"
    df.to_csv(csv_file, encoding='utf-8-sig', index=False)
    return csv_file
```

## 🔄 自动化流程

### 1. 批处理模式
```bash
# 处理所有用户
python excel_chatlog_injector.py --all

# 处理特定用户
python excel_chatlog_injector.py --user 小马

# 自动上传到指定平台
python excel_chatlog_injector.py --user 小马 --platform office365
```

### 2. 定时更新
```python
# 定时任务配置
def schedule_excel_update():
    # 每天凌晨2点更新Excel表
    schedule.every().day.at("02:00").do(update_all_excel_tables)
    
    # 每小时增量更新活跃联系人
    schedule.every().hour.do(update_active_contacts_excel)
```

### 3. 实时同步
```python
# 监听聊天记录变化
class ExcelSyncMonitor:
    def on_chatlog_updated(self, username, contact_id):
        # 1. 重新格式化该联系人的聊天记录
        # 2. 更新对应的Excel行
        # 3. 同步到在线表格
        pass
```

## 📱 API集成详细方案

### Microsoft Graph API集成
```python
# 配置
GRAPH_CONFIG = {
    'client_id': 'your_client_id',
    'client_secret': 'your_client_secret',
    'tenant_id': 'your_tenant_id',
    'scope': ['https://graph.microsoft.com/.default']
}

# 实现步骤
class Office365Injector:
    def __init__(self):
        self.token = self.get_access_token()
    
    def create_workbook(self, name):
        # POST /me/drive/root/children
        pass
    
    def update_worksheet(self, workbook_id, data):
        # PATCH /me/drive/items/{workbook_id}/workbook/worksheets/{worksheet_id}
        pass
```

### Google Sheets API集成
```python
# 配置
GOOGLE_CONFIG = {
    'credentials_file': 'credentials.json',
    'scopes': ['https://www.googleapis.com/auth/spreadsheets']
}

# 实现步骤
class GoogleSheetsInjector:
    def __init__(self):
        self.service = self.authenticate()
    
    def create_spreadsheet(self, title):
        # 创建新的电子表格
        pass
    
    def batch_update(self, spreadsheet_id, data):
        # 批量更新数据
        pass
```

## 🛠️ 使用步骤

### 1. 环境准备
```bash
# 安装依赖
pip install pandas openpyxl requests google-api-python-client

# 配置API密钥（如需要）
cp config.example.json config.json
# 编辑config.json填入API密钥
```

### 2. 运行脚本
```bash
cd Remarked_Contacts_Chatlogs
python excel_chatlog_injector.py
```

### 3. 选择操作
```
📊 Excel聊天记录注入工具
============================================
📂 找到 3 个用户文件夹:
   1. 小马
   2. 小司  
   3. 小罗

处理选项:
1. 处理所有用户
2. 选择特定用户

请选择 (1-2): 2
请输入用户编号 (1-3): 1

📊 处理用户: 小马
--------------------------------------------------
   ✅ 找到 1247 个有ID的联系人
   💾 Excel文件已生成: excel_output/chatlog_excel_小马_20250709_143022.xlsx
   🌐 是否注入到在线Excel表格? (y/N): y

📊 在线Excel注入选项:
1. Microsoft Excel Online (Office 365)
2. Google Sheets  
3. 腾讯文档
4. 石墨文档
5. 导出为CSV格式

请选择目标平台 (1-5): 1
```

## 🔧 高级功能

### 1. 数据过滤
```python
# 只导出有聊天记录的联系人
def filter_active_contacts(id_to_chatlog):
    return {k: v for k, v in id_to_chatlog.items() 
            if v['message_count'] > 0}

# 按时间范围过滤
def filter_by_date_range(chatlog_data, start_date, end_date):
    # 过滤指定时间范围内的消息
    pass
```

### 2. 数据压缩
```python
# 压缩长聊天记录
def compress_long_chatlogs(chatlog_text, max_length=1000):
    if len(chatlog_text) > max_length:
        lines = chatlog_text.split('\n')
        # 保留开头和结尾的消息
        compressed = lines[:10] + ['...'] + lines[-10:]
        return '\n'.join(compressed)
    return chatlog_text
```

### 3. 多语言支持
```python
# 支持不同语言的时间格式
def format_timestamp_localized(timestamp, locale='zh_CN'):
    # 根据地区格式化时间显示
    pass
```

这个实现方案提供了完整的Excel在线表注入解决方案，支持多个平台，具有良好的扩展性和实用性！
