"""
聊天记录读取器
根据联系人JSON文件中的用户信息，调用聊天记录API获取聊天记录
"""

import json
import os
import requests
import urllib.parse
from datetime import datetime
import csv
import time
import logging

class ChatLogReader:
    def __init__(self, data_dir="data", output_dir="chatlogs"):
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.setup_logging()
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 默认时间范围
        self.default_time_range = "2023-01-01~2025-07-07"
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('chatlog_reader.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def parse_filename(self, filename):
        """从文件名中解析IP地址和用户名"""
        # 文件名格式: IP_用户名.json 或 IP_用户_用户名.json
        if not filename.endswith('.json'):
            return None, None
            
        name_part = filename[:-5]  # 移除.json
        parts = name_part.split('_')
        
        if len(parts) >= 4:
            # 格式: 192_168_1_191_小马.json
            ip = '.'.join(parts[:4])
            username = '_'.join(parts[4:])
            return ip, username
        else:
            self.logger.warning(f"无法解析文件名格式: {filename}")
            return None, None
            
    def load_contacts_from_file(self, filepath):
        """从JSON文件加载联系人列表"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                contacts = json.load(f)
            
            if not isinstance(contacts, list):
                self.logger.error(f"文件格式错误，不是联系人列表: {filepath}")
                return []
                
            self.logger.info(f"从 {filepath} 加载了 {len(contacts)} 个联系人")
            return contacts
            
        except Exception as e:
            self.logger.error(f"加载文件失败 {filepath}: {str(e)}")
            return []
            
    def fetch_chatlog(self, ip, talker, time_range=None):
        """获取指定用户的聊天记录"""
        if not time_range:
            time_range = self.default_time_range
            
        # 构建API URL
        base_url = f"http://{ip}:5030/api/v1/chatlog"
        params = {
            'time': time_range,
            'talker': talker
        }
        
        # URL编码参数
        query_string = urllib.parse.urlencode(params)
        full_url = f"{base_url}?{query_string}"
        
        self.logger.info(f"请求聊天记录: {full_url}")
        
        try:
            response = requests.get(full_url, timeout=30)
            response.raise_for_status()
            
            # 检查响应内容
            content = response.text.strip()
            if not content:
                self.logger.info(f"用户 {talker} 的聊天记录为空")
                return None
                
            # 尝试解析JSON
            try:
                chatlog_data = response.json()
                if not chatlog_data:
                    self.logger.info(f"用户 {talker} 的聊天记录为空")
                    return None
                return chatlog_data
            except json.JSONDecodeError:
                # 如果不是JSON格式，返回原始文本
                self.logger.info(f"用户 {talker} 返回非JSON格式数据，长度: {len(content)}")
                return content
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求聊天记录失败 {talker}: {str(e)}")
            return None
            
    def save_chatlog(self, chatlog_data, filename, talker, nickname=""):
        """保存聊天记录到文件"""
        if not chatlog_data:
            return False
            
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            if isinstance(chatlog_data, (dict, list)):
                # JSON格式数据
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(chatlog_data, f, ensure_ascii=False, indent=2)
                self.logger.info(f"聊天记录已保存: {filepath} (JSON格式)")
            else:
                # 文本格式数据
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(str(chatlog_data))
                self.logger.info(f"聊天记录已保存: {filepath} (文本格式)")
                
            return True
            
        except Exception as e:
            self.logger.error(f"保存聊天记录失败 {filepath}: {str(e)}")
            return False
            
    def process_contact_file(self, filename):
        """处理单个联系人文件"""
        filepath = os.path.join(self.data_dir, filename)
        
        # 解析文件名获取IP和用户名
        ip, file_username = self.parse_filename(filename)
        if not ip or not file_username:
            self.logger.error(f"无法解析文件名: {filename}")
            return
            
        self.logger.info(f"处理文件: {filename} (IP: {ip}, 用户: {file_username})")
        
        # 加载联系人列表
        contacts = self.load_contacts_from_file(filepath)
        if not contacts:
            return
            
        # 统计信息
        total_contacts = len(contacts)
        processed_count = 0
        success_count = 0
        
        # 为每个联系人获取聊天记录
        for i, contact in enumerate(contacts, 1):
            username = contact.get("UserName", "").strip()
            nickname = contact.get("NickName", "").strip()
            
            if not username:
                self.logger.warning(f"联系人 {i} 没有UserName，跳过")
                continue
                
            self.logger.info(f"处理联系人 {i}/{total_contacts}: {nickname} ({username})")
            
            # 获取聊天记录
            chatlog_data = self.fetch_chatlog(ip, username)
            
            if chatlog_data:
                # 生成输出文件名
                safe_nickname = "".join(c for c in nickname if c.isalnum() or c in "._-") if nickname else "unknown"
                safe_username = "".join(c for c in username if c.isalnum() or c in "._-@")
                
                output_filename = f"{ip.replace('.', '_')}_{file_username}_{safe_nickname}_{safe_username}.json"
                
                # 保存聊天记录
                if self.save_chatlog(chatlog_data, output_filename, username, nickname):
                    success_count += 1
                    
            processed_count += 1
            
            # 添加延迟避免请求过于频繁
            time.sleep(0.5)
            
        self.logger.info(f"文件 {filename} 处理完成: 总联系人 {total_contacts}, 已处理 {processed_count}, 成功获取 {success_count}")
        
    def process_all_files(self):
        """处理data目录中的所有JSON文件"""
        if not os.path.exists(self.data_dir):
            self.logger.error(f"数据目录不存在: {self.data_dir}")
            return
            
        json_files = [f for f in os.listdir(self.data_dir) if f.endswith('.json')]
        
        if not json_files:
            self.logger.error(f"在 {self.data_dir} 目录中没有找到JSON文件")
            return
            
        self.logger.info(f"找到 {len(json_files)} 个JSON文件")
        
        for filename in json_files:
            self.logger.info(f"开始处理文件: {filename}")
            self.process_contact_file(filename)
            self.logger.info(f"文件 {filename} 处理完成\n")
            
    def process_specific_file(self, filename):
        """处理指定的文件"""
        if not filename.endswith('.json'):
            filename += '.json'
            
        filepath = os.path.join(self.data_dir, filename)
        if not os.path.exists(filepath):
            self.logger.error(f"文件不存在: {filepath}")
            return
            
        self.process_contact_file(filename)
        
    def generate_summary_report(self):
        """生成聊天记录获取摘要报告"""
        if not os.path.exists(self.output_dir):
            self.logger.info("没有聊天记录输出目录")
            return
            
        chatlog_files = [f for f in os.listdir(self.output_dir) if f.endswith('.json')]
        
        if not chatlog_files:
            self.logger.info("没有找到聊天记录文件")
            return
            
        # 生成CSV报告
        report_file = os.path.join(self.output_dir, f"chatlog_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
        
        try:
            with open(report_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(["文件名", "IP地址", "用户名", "昵称", "UserName", "文件大小(KB)", "创建时间"])
                
                for filename in sorted(chatlog_files):
                    filepath = os.path.join(self.output_dir, filename)
                    
                    # 解析文件名
                    parts = filename[:-5].split('_')  # 移除.json
                    if len(parts) >= 6:
                        ip = '.'.join(parts[:4])
                        file_user = parts[4]
                        nickname = parts[5] if len(parts) > 5 else ""
                        username = parts[6] if len(parts) > 6 else ""
                    else:
                        ip = file_user = nickname = username = "unknown"
                        
                    # 文件信息
                    file_size = os.path.getsize(filepath) / 1024  # KB
                    create_time = datetime.fromtimestamp(os.path.getctime(filepath)).strftime('%Y-%m-%d %H:%M:%S')
                    
                    writer.writerow([filename, ip, file_user, nickname, username, f"{file_size:.2f}", create_time])
                    
            self.logger.info(f"摘要报告已生成: {report_file}")
            
        except Exception as e:
            self.logger.error(f"生成摘要报告失败: {str(e)}")

def main():
    import sys
    
    reader = ChatLogReader()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--all":
            # 处理所有文件
            reader.process_all_files()
        elif sys.argv[1] == "--file" and len(sys.argv) > 2:
            # 处理指定文件
            reader.process_specific_file(sys.argv[2])
        elif sys.argv[1] == "--report":
            # 生成摘要报告
            reader.generate_summary_report()
        else:
            print("用法:")
            print("  python chatlog_reader.py --all                    # 处理所有文件")
            print("  python chatlog_reader.py --file 文件名.json       # 处理指定文件")
            print("  python chatlog_reader.py --report                 # 生成摘要报告")
    else:
        # 默认处理所有文件
        print("开始处理所有联系人文件的聊天记录...")
        reader.process_all_files()
        reader.generate_summary_report()
        print("处理完成！")

if __name__ == "__main__":
    main()
