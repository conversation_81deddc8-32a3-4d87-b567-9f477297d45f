{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "48876", "contact_info": {"UserName": "wxid_wfn6be0qsosx32", "Alias": "", "Remark": "48876李思婷", "NickName": "李思婷"}, "fetch_time": "2025-07-09T13:23:46.630654", "message_count": 49, "chatlog_data": [{"seq": 1745732032000, "time": "2025-04-27T13:33:52+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "我通过了你的朋友验证请求，现在我们可以开始聊天了"}, {"seq": 1745732043000, "time": "2025-04-27T13:34:03+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士您好呀"}, {"seq": 1745732050000, "time": "2025-04-27T13:34:10+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "您好，我是李思婷"}, {"seq": 1745732055000, "time": "2025-04-27T13:34:15+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们是一家专业的猎头公司，搭建高层次人才与国内企业高校就业创业全职兼职合作的桥梁；可以柔性合作或者全职回国就业创业。"}, {"seq": 1745732076000, "time": "2025-04-27T13:34:36+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "请问有什么职位我比较合适"}, {"seq": 1745732080000, "time": "2025-04-27T13:34:40+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们主要是负责对接江浙一带的单位哦"}, {"seq": 1745732086000, "time": "2025-04-27T13:34:46+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1745732099000, "time": "2025-04-27T13:34:59+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "企业和高校都有吗"}, {"seq": 1745732112000, "time": "2025-04-27T13:35:12+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "企业、高校、研究所都有的"}, {"seq": 1745732116000, "time": "2025-04-27T13:35:16+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1745732129000, "time": "2025-04-27T13:35:29+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您有时间的话，我跟您语音聊下您看机会的诉求呀"}, {"seq": 1745732139000, "time": "2025-04-27T13:35:39+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "没问题 您现在方便吗"}, {"seq": 1745732149000, "time": "2025-04-27T13:35:49+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的，我马上拨给您"}, {"seq": 1745732188001, "time": "2025-04-27T13:36:28+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[已在其它设备接听]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>530283330943280275</roomid>\n<roomkey>0</roomkey>\n<inviteid>0</inviteid>\n<msg_type>101</msg_type>\n<timestamp>1745732188907</timestamp>\n<identity><![CDATA[4730314082082938834]]></identity>\n<duration>0</duration>\n<inviteid64>0</inviteid64>\n<business>1</business>\n<caller_memberid>0</caller_memberid>\n<callee_memberid>1</callee_memberid>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1745732575000, "time": "2025-04-27T13:42:55+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那李博咱们保持联系，后续有什么问题咱们也随时沟通~"}, {"seq": 1745732593000, "time": "2025-04-27T13:43:13+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1745732595000, "time": "2025-04-27T13:43:15+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "多谢"}, {"seq": 1745732609000, "time": "2025-04-27T13:43:29+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername=\"wxid_6ahbbsar7g8i22\" tousername=\"wxid_wfn6be0qsosx32\" type=\"2\" androidmd5=\"26122ec19aed2acce50ee9ded270770c\" androidlen=\"5050\" aeskey=\"772fda894cee44c7857ac9cfbb90ca2f\" encrypturl=\"http://vweixinf.tc.qq.com/110/20402/stodownload?m=7837d8314d2685c916ee03a7068763fe&amp;filekey=30440201010430302e02016e04025348042037383337643833313464323638356339313665653033613730363837363366650203017370040d00000004627466730000000132&amp;hy=SH&amp;storeid=2640b0be10008210b0b0a68d00000006e02004fb25348111418b0b6c6cbfc9&amp;ef=2&amp;bizid=1022\" externurl=\"http://vweixinf.tc.qq.com/110/20403/stodownload?m=807123b2398ad16c99c156a9ef6544c5&amp;filekey=3043020101042f302d02016e0402534804203830373132336232333938616431366339396331353661396566363534346335020213c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2640b0be10008c1ba0b0a68d00000006e03004fb35348111418b0b6c6cbfd4&amp;ef=3&amp;bizid=1022\" externmd5=\"0a02e7bbdef44dc0a6848a4cffe73309\"/></msg>"}, {"seq": 1745734045000, "time": "2025-04-27T14:07:25+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n\t\t<title>简历_李思婷.pdf</title>\n\t\t<type>6</type>\n\t\t<action>view</action>\n\t\t<appattach>\n\t\t\t<totallen>361317</totallen>\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<attachid>@cdn_3057020100044b3049020100020496a378fa02032de37a02041581af2b0204680db764042466616437393638332d366263372d343338302d613639362d3836346435353731356163330204011c00050201000405004c51e500_591c8cc1438c1912ba2a5c1edb75356d_1</attachid>\n\t\t\t<cdnattachurl>3057020100044b3049020100020496a378fa02032de37a02041581af2b0204680db764042466616437393638332d366263372d343338302d613639362d3836346435353731356163330204011c00050201000405004c51e500</cdnattachurl>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey>591c8cc1438c1912ba2a5c1edb75356d</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t\t<filekey>wxid_6ahbbsar7g8i22_21_1745734045</filekey>\n\t\t\t<overwrite_newmsgid>5225398948840725483</overwrite_newmsgid>\n\t\t\t<fileuploadtoken>v1_4Dvlu3JqS5gH3Cy+PGraElu+XMvs8SP8AqXp/WnbF0GrFHWaZc0Q7INhB54ERfonyut34bVBaP0GFtNb5acX6XfpdZTiB33khUidXfEOr9MAgJE2yd302LsxLKZ/o/Hy4r1XQwmAw9Ui+oHZY9R5SBqN57gHeUwuBi8k7M7/e2u12cGvU9o4At/Z0XVwKsl5a7/Pf33dBgav46ifkSZNGvpEagY=</fileuploadtoken>\n\t\t</appattach>\n\t\t<md5>3bc46ade5e9523c9b9e270650994750d</md5>\n\t</appmsg>\n\t<fromusername>wxid_wfn6be0qsosx32</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>7</version>\n\t\t<appname>微信电脑版</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n\u0000", "contents": {"md5": "3bc46ade5e9523c9b9e270650994750d", "title": "简历_李思婷.pdf"}}, {"seq": 1745734059000, "time": "2025-04-27T14:07:39+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好滴好滴"}, {"seq": 1745734069000, "time": "2025-04-27T14:07:49+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "忘了给您发我的中文版简历，之前只发了英文版"}, {"seq": 1745734076000, "time": "2025-04-27T14:07:56+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢"}, {"seq": 1745734094000, "time": "2025-04-27T14:08:14+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "辛苦您啦~"}, {"seq": 1745734113000, "time": "2025-04-27T14:08:33+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "多谢"}, {"seq": 1745734119000, "time": "2025-04-27T14:08:39+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "那麻烦您了"}, {"seq": 1745734133000, "time": "2025-04-27T14:08:53+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "有消息我第一时间联系您哦"}, {"seq": 1745734142000, "time": "2025-04-27T14:09:02+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1749785721000, "time": "2025-06-13T11:35:21+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "对啦，李博，您今年有参与申报QM吗"}, {"seq": 1749785768000, "time": "2025-06-13T11:36:08+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "因为现在qm开始了第二批报名，我们也为您匹配到了单位，我发您看看"}, {"seq": 1749785772000, "time": "2025-06-13T11:36:12+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "https://www.nbmksw.com/  这是公司官网，您可以看下"}, {"seq": 1749787309000, "time": "2025-06-13T12:01:49+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "不参加啦谢谢"}, {"seq": 1749790799000, "time": "2025-06-13T12:59:59+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您是参与了qm的申报了是吗"}, {"seq": 1749792509000, "time": "2025-06-13T13:28:29+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "没有呢 我不打算参加，因为海外工作经历不满三年"}, {"seq": 1749792773000, "time": "2025-06-13T13:32:53+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "如果是博后的岗位您会考虑吗"}, {"seq": 1749794784000, "time": "2025-06-13T14:06:24+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "不考虑"}, {"seq": 1749794890000, "time": "2025-06-13T14:08:10+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的，那我为您看看其他的机会哦"}, {"seq": 1749794904000, "time": "2025-06-13T14:08:24+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "先不用了"}, {"seq": 1749794926000, "time": "2025-06-13T14:08:46+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "我已经找到工作了"}, {"seq": 1749794938000, "time": "2025-06-13T14:08:58+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是在哪里呀"}, {"seq": 1749794955000, "time": "2025-06-13T14:09:15+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "具体不方便透露[破涕为笑]"}, {"seq": 1749795005000, "time": "2025-06-13T14:10:05+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "哈哈，明白，那其实这样的话您可以看看配合申报呀，配合申报的话不需要您入职，我们还会给您5k-1w的报酬的呀"}, {"seq": 1749795056000, "time": "2025-06-13T14:10:56+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "不太好吧"}, {"seq": 1749795068000, "time": "2025-06-13T14:11:08+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "本来就是有这种政策的呀"}, {"seq": 1749795079000, "time": "2025-06-13T14:11:19+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "社保就只能有一家呀"}, {"seq": 1749795098000, "time": "2025-06-13T14:11:38+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这个不涉及入职，所以也就不牵扯社保呀"}, {"seq": 1749795114000, "time": "2025-06-13T14:11:54+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "没事 我不打算弄这个"}, {"seq": 1749795117000, "time": "2025-06-13T14:11:57+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢你了"}, {"seq": 1749795130000, "time": "2025-06-13T14:12:10+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好滴好滴，如果您朋友有兴趣随时联系~"}, {"seq": 1749795138000, "time": "2025-06-13T14:12:18+08:00", "talker": "wxid_wfn6be0qsosx32", "talkerName": "", "isChatRoom": false, "sender": "wxid_wfn6be0qsosx32", "senderName": "48876李思婷", "isSelf": false, "type": 1, "subType": 0, "content": "嗯嗯"}]}