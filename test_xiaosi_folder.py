"""
专门测试小司文件夹创建
"""

import requests
import json

def test_xiaosi_folder():
    """测试为小司创建文件夹"""
    print("🧪 测试为小司创建文件夹...")
    
    # 模拟小司的数据
    test_data = {
        "client_id": "192_168_1_5_小司",
        "contacts": [
            {
                "UserName": "xiaosi_test_001",
                "NickName": "小司测试联系人",
                "Alias": "",
                "Remark": "测试小司文件夹创建功能"
            }
        ]
    }
    
    server_url = "http://127.0.0.1:8080/upload_contacts"
    
    try:
        print(f"📤 发送请求到: {server_url}")
        print(f"📋 Client ID: {test_data['client_id']}")
        
        response = requests.post(server_url, json=test_data, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        print(f"✅ 服务器响应: {result}")
        
        print(f"\n📁 请检查是否创建了以下结构:")
        print(f"   data/小司/")
        print(f"   ├── 192_168_1_5_小司.json")
        print(f"   └── new_contacts_log.json")
        
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    test_xiaosi_folder()
