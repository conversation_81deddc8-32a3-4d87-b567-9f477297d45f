{"user": "小罗", "contact_id": "49233", "contact_info": {"UserName": "wxid_yzd1etq0jwbf12", "Alias": "jnwgbeh42", "Remark": "49233 <PERSON>(领英)", "NickName": "JX"}, "fetch_time": "2025-07-09T13:22:14.424238", "message_count": 14, "chatlog_data": [{"seq": 1751250159000, "time": "2025-06-30T10:22:39+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士 您好！我们是专注于高薪挖掘 海外博士/教授/院士的专业国际猎头公司TopTalents Group"}, {"seq": 1751268495000, "time": "2025-06-30T15:28:15+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_yzd1etq0jwbf12", "senderName": "49233 <PERSON>(领英)", "isSelf": false, "type": 1, "subType": 0, "content": "您好，您这边有什么符合我背景的潜在职位吗？"}, {"seq": 1751269727000, "time": "2025-06-30T15:48:47+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您好 博士方便给我发一份您的最新简历吗？我这边好帮您匹配单位"}, {"seq": 1751269924000, "time": "2025-06-30T15:52:04+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_yzd1etq0jwbf12", "senderName": "49233 <PERSON>(领英)", "isSelf": false, "type": 49, "subType": 6, "content": "", "contents": {"md5": "82db50503004a4166bdfe6d1a846eec3", "title": "<PERSON> (CV).pdf"}}, {"seq": 1751270425000, "time": "2025-06-30T16:00:25+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到"}, {"seq": 1751270566000, "time": "2025-06-30T16:02:46+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博 您一般什么时间方便 咱们约一个时间语音沟通一下？"}, {"seq": 1751270735000, "time": "2025-06-30T16:05:35+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_yzd1etq0jwbf12", "senderName": "49233 <PERSON>(领英)", "isSelf": false, "type": 1, "subType": 0, "content": "明天还是后天北京时间下午四点可以吗？"}, {"seq": 1751270784000, "time": "2025-06-30T16:06:24+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "明天下午4点 我这边有约了   后天可以吗？"}, {"seq": 1751270796000, "time": "2025-06-30T16:06:36+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_yzd1etq0jwbf12", "senderName": "49233 <PERSON>(领英)", "isSelf": false, "type": 1, "subType": 0, "content": "后天也可以"}, {"seq": 1751270824000, "time": "2025-06-30T16:07:04+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的 那就后天下午4点我跟您联系"}, {"seq": 1751443284000, "time": "2025-07-02T16:01:24+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博 现在方便沟通吗？"}, {"seq": 1751443299000, "time": "2025-07-02T16:01:39+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_yzd1etq0jwbf12", "senderName": "49233 <PERSON>(领英)", "isSelf": false, "type": 1, "subType": 0, "content": "方便"}, {"seq": 1751444150000, "time": "2025-07-02T16:15:50+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": ""}, {"seq": 1751445059000, "time": "2025-07-02T16:30:59+08:00", "talker": "wxid_yzd1etq0jwbf12", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "l<PERSON><PERSON><PERSON>@email.techtalentsuk.com 王博 这是我的邮箱"}]}