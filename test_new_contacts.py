"""
测试新增联系人检测功能
创建一个测试用的联系人文件，然后运行分析来验证新增检测功能
"""

import json
import os
import time
from datetime import datetime
from find_duplicate_contacts import ScheduledContactAnalyzer

def create_test_contact_file():
    """创建一个测试用的联系人文件"""
    test_contacts = [
        {
            "UserName": "test_user_001",
            "Alias": "testalias",
            "Remark": "测试联系人1",
            "NickName": "测试小明"
        },
        {
            "UserName": "test_user_002",
            "Alias": "",
            "Remark": "",
            "NickName": "测试小红"
        },
        {
            "UserName": "filehelper",  # 这个会被识别为重复联系人
            "Alias": "",
            "Remark": "",
            "NickName": "文件传输助手"
        }
    ]
    
    # 生成测试文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_filename = f"test_user_{timestamp}.json"
    test_filepath = os.path.join("data", test_filename)
    
    # 保存测试文件
    with open(test_filepath, 'w', encoding='utf-8') as f:
        json.dump(test_contacts, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建测试文件: {test_filepath}")
    print(f"   包含 {len(test_contacts)} 个联系人")
    
    return test_filepath, test_filename

def run_test():
    """运行新增联系人检测测试"""
    print("="*60)
    print("新增联系人检测功能测试")
    print("="*60)
    
    # 1. 运行一次分析建立基线
    print("\n步骤1: 建立基线状态...")
    analyzer = ScheduledContactAnalyzer()
    analyzer.run_manual_analysis()
    
    print("\n等待3秒...")
    time.sleep(3)
    
    # 2. 创建测试文件
    print("\n步骤2: 创建测试联系人文件...")
    test_filepath, test_filename = create_test_contact_file()
    
    print("\n等待3秒...")
    time.sleep(3)
    
    # 3. 再次运行分析检测新增
    print("\n步骤3: 运行分析检测新增联系人...")
    analyzer2 = ScheduledContactAnalyzer()
    analyzer2.run_manual_analysis()
    
    # 4. 清理测试文件
    print(f"\n步骤4: 清理测试文件...")
    try:
        os.remove(test_filepath)
        print(f"✅ 已删除测试文件: {test_filename}")
    except Exception as e:
        print(f"⚠️  删除测试文件失败: {str(e)}")
    
    print("\n" + "="*60)
    print("测试完成！请查看reports目录中的分析报告")
    print("="*60)

def cleanup_test_files():
    """清理所有测试文件"""
    data_dir = "data"
    if not os.path.exists(data_dir):
        return
        
    test_files = [f for f in os.listdir(data_dir) if f.startswith("test_user_")]
    
    if not test_files:
        print("没有找到测试文件")
        return
        
    print(f"找到 {len(test_files)} 个测试文件:")
    for filename in test_files:
        filepath = os.path.join(data_dir, filename)
        try:
            os.remove(filepath)
            print(f"✅ 已删除: {filename}")
        except Exception as e:
            print(f"❌ 删除失败 {filename}: {str(e)}")

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup":
        cleanup_test_files()
    else:
        run_test()

if __name__ == "__main__":
    main()
