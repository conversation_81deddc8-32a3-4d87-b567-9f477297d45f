#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试语音消息识别脚本
检查为什么语音消息没有被正确处理
"""

import json
import requests
from pathlib import Path

def debug_voice_messages(contact_id):
    """调试指定联系人的语音消息"""
    print("=" * 60)
    print(f"🔍 调试语音消息识别 - 联系人ID: {contact_id}")
    print("=" * 60)
    
    # 获取聊天记录
    try:
        response = requests.get(f"http://127.0.0.1:8080/api/get_chatlog_by_id/{contact_id}")
        if response.status_code != 200:
            print(f"❌ 获取聊天记录失败: {response.status_code}")
            return
        
        chat_data = response.json()
        messages = chat_data.get('messages', [])
        
        print(f"📊 总消息数: {len(messages)}")
        
        # 统计消息类型
        message_types = {}
        voice_messages = []
        audio_messages = []
        
        for i, msg in enumerate(messages):
            msg_type = msg.get('type', 'unknown')
            attachment_type = msg.get('attachment_type', None)
            content = msg.get('content', '')
            url = msg.get('url', '')
            filename = msg.get('filename', '')
            
            # 统计消息类型
            if attachment_type:
                key = f"{msg_type}({attachment_type})"
            else:
                key = msg_type
            message_types[key] = message_types.get(key, 0) + 1
            
            # 查找语音相关消息
            content_lower = content.lower()
            url_lower = url.lower()
            filename_lower = filename.lower()
            
            is_voice = False
            voice_indicators = []
            
            # 检查各种语音识别条件
            if attachment_type == 'voice':
                is_voice = True
                voice_indicators.append("attachment_type=voice")
            
            if '/voice/' in url_lower:
                is_voice = True
                voice_indicators.append("URL包含/voice/")
            
            if any(ext in url_lower for ext in ['.mp3', '.wav', '.m4a', '.aac', '.ogg']):
                is_voice = True
                voice_indicators.append(f"URL包含音频扩展名")
            
            if any(keyword in content_lower for keyword in ['语音', 'voice', 'audio', '音频']):
                is_voice = True
                voice_indicators.append("内容包含语音关键词")
            
            if any(ext in filename_lower for ext in ['.mp3', '.wav', '.m4a', '.aac', '.ogg']):
                is_voice = True
                voice_indicators.append("文件名包含音频扩展名")
            
            # 特别检查audio/mp3格式
            if 'audio/mp3' in content_lower or 'audio/mp3' in url_lower:
                audio_messages.append({
                    'index': i,
                    'content': content[:100],
                    'url': url,
                    'filename': filename,
                    'type': msg_type,
                    'attachment_type': attachment_type,
                    'timestamp': msg.get('time', ''),
                    'is_self': msg.get('isSelf', False)
                })
                voice_indicators.append("包含audio/mp3格式")
                is_voice = True
            
            if is_voice:
                voice_messages.append({
                    'index': i,
                    'content': content[:100],
                    'url': url,
                    'filename': filename,
                    'type': msg_type,
                    'attachment_type': attachment_type,
                    'timestamp': msg.get('time', ''),
                    'is_self': msg.get('isSelf', False),
                    'voice_indicators': voice_indicators
                })
        
        print("\n📊 消息类型统计:")
        for msg_type, count in sorted(message_types.items()):
            print(f"  {msg_type}: {count}条")
        
        print(f"\n🎤 发现语音消息: {len(voice_messages)}条")
        for i, voice_msg in enumerate(voice_messages):
            print(f"\n--- 语音消息 {i+1} ---")
            print(f"索引: {voice_msg['index']}")
            print(f"时间: {voice_msg['timestamp']}")
            print(f"发送者: {'我' if voice_msg['is_self'] else '对方'}")
            print(f"类型: {voice_msg['type']}")
            print(f"附件类型: {voice_msg['attachment_type']}")
            print(f"内容: {voice_msg['content']}")
            print(f"URL: {voice_msg['url']}")
            print(f"文件名: {voice_msg['filename']}")
            print(f"识别依据: {', '.join(voice_msg['voice_indicators'])}")
        
        print(f"\n🎵 发现audio/mp3消息: {len(audio_messages)}条")
        for i, audio_msg in enumerate(audio_messages):
            print(f"\n--- Audio/MP3消息 {i+1} ---")
            print(f"索引: {audio_msg['index']}")
            print(f"时间: {audio_msg['timestamp']}")
            print(f"发送者: {'我' if audio_msg['is_self'] else '对方'}")
            print(f"类型: {audio_msg['type']}")
            print(f"附件类型: {audio_msg['attachment_type']}")
            print(f"内容: {audio_msg['content']}")
            print(f"URL: {audio_msg['url']}")
            print(f"文件名: {audio_msg['filename']}")
        
        # 保存调试信息到文件
        debug_info = {
            'contact_id': contact_id,
            'total_messages': len(messages),
            'message_types': message_types,
            'voice_messages': voice_messages,
            'audio_messages': audio_messages
        }
        
        with open(f'debug_voice_{contact_id}.json', 'w', encoding='utf-8') as f:
            json.dump(debug_info, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 调试信息已保存到: debug_voice_{contact_id}.json")
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")

def main():
    """主函数"""
    print("🎤 语音消息调试工具")
    
    # 你可以在这里输入要调试的联系人ID
    contact_id = input("请输入联系人ID (例如: 42828): ").strip()
    
    if not contact_id:
        print("❌ 请输入有效的联系人ID")
        return
    
    debug_voice_messages(contact_id)

if __name__ == "__main__":
    main()
