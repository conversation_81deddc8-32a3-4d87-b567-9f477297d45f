# 📱 微信消息发送功能使用指南

本文档介绍如何使用Flask应用中的微信消息发送功能，该功能可以通过API接口向指定的微信联系人发送消息。

## 🚀 功能概述

- **Web界面发送**: 通过浏览器界面发送微信消息
- **API接口调用**: 支持程序化调用发送消息
- **多种调用方式**: 支持Python、PowerShell、curl等多种调用方式
- **错误处理**: 完善的错误处理和状态反馈
- **目标主机配置**: 可配置不同的微信API服务器地址

## 📋 前置条件

1. **微信API服务**: 目标机器上需要运行微信API服务（通常在端口8000）
2. **Flask应用**: 本Flask应用需要正常运行（默认端口5000）
3. **网络连接**: 确保Flask应用能够访问微信API服务器
4. **微信好友**: 接收人必须是微信好友，且昵称要准确

## 🌐 Web界面使用

### 访问发送页面
```
http://localhost:5000/send_message
```

### 操作步骤
1. 打开浏览器访问发送页面
2. 填写目标主机地址（如：`localhost:8000`）
3. 输入接收人昵称（微信中显示的昵称）
4. 输入要发送的消息内容
5. 点击"发送消息"按钮
6. 查看发送结果

## 🔧 API接口调用

### 接口信息
- **URL**: `POST /api/send_wechat_message`
- **Content-Type**: `application/json`
- **超时时间**: 30秒

### 请求参数
```json
{
  "target_host": "localhost:8000",    // 微信API服务器地址
  "receiver": "小马",                 // 接收人昵称
  "msg": "Hello, 这是测试消息"        // 消息内容
}
```

### 响应格式

#### 成功响应
```json
{
  "status": "success",
  "message": "消息发送成功",
  "data": {
    "receiver": "小马",
    "msg": "Hello, 这是测试消息",
    "target_host": "localhost:8000",
    "response": {
      // 微信API的原始响应
    }
  }
}
```

#### 失败响应
```json
{
  "status": "error",
  "message": "错误描述",
  "data": {
    // 错误详细信息
  }
}
```

## 💻 调用示例

### Python调用
```python
import requests

# 发送消息
data = {
    "target_host": "localhost:8000",
    "receiver": "小马",
    "msg": "Hello from Python!"
}

response = requests.post(
    "http://localhost:5000/api/send_wechat_message",
    json=data,
    timeout=30
)

result = response.json()
if result['status'] == 'success':
    print("消息发送成功")
else:
    print(f"消息发送失败: {result['message']}")
```

### PowerShell调用
```powershell
$body = @{
    target_host = "localhost:8000"
    receiver = "小马"
    msg = "Hello from PowerShell!"
} | ConvertTo-Json

$response = Invoke-RestMethod -Uri "http://localhost:5000/api/send_wechat_message" -Method Post -Body $body -ContentType "application/json"

if ($response.status -eq "success") {
    Write-Host "消息发送成功" -ForegroundColor Green
} else {
    Write-Host "消息发送失败: $($response.message)" -ForegroundColor Red
}
```

### curl调用
```bash
# 通过Flask应用发送
curl -X POST http://localhost:5000/api/send_wechat_message \
  -H "Content-Type: application/json" \
  -d '{
    "target_host": "localhost:8000",
    "receiver": "小马",
    "msg": "Hello from curl!"
  }'

# 直接调用微信API
curl -X POST http://localhost:8000/api/sendMsg \
  -H "Content-Type: application/json" \
  -d '{
    "receiver": "小马",
    "msg": "Hello from curl!"
  }'
```

## 🧪 测试工具

### Python测试脚本
```bash
# 运行所有测试
python test_wechat_send.py

# 只测试Flask API
python test_wechat_send.py flask

# 只测试微信API
python test_wechat_send.py direct

# 显示curl示例
python test_wechat_send.py curl
```

### PowerShell测试脚本
```powershell
# 运行PowerShell测试
.\test_wechat_send.ps1
```

## ⚠️ 注意事项

### 网络配置
- 确保Flask应用能够访问微信API服务器
- 检查防火墙设置，确保端口通信正常
- 如果使用不同的IP地址，请相应修改`target_host`参数

### 微信限制
- 接收人必须是微信好友
- 昵称必须与微信中显示的完全一致
- 避免发送过于频繁，可能被微信限制

### 错误处理
- 连接超时：检查网络连接和服务器状态
- 接收人不存在：确认昵称正确且为微信好友
- 服务器错误：检查微信API服务是否正常运行

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查微信API服务是否运行
   - 确认端口号正确（默认8000）
   - 检查网络连接

2. **接收人不存在**
   - 确认昵称拼写正确
   - 检查是否为微信好友
   - 尝试使用备注名或其他显示名称

3. **消息发送失败**
   - 检查消息内容是否包含敏感词
   - 确认消息长度不超过限制
   - 检查微信客户端状态

### 调试方法
- 查看Flask应用控制台输出
- 使用测试脚本验证功能
- 直接调用微信API进行对比测试

## 📚 相关文档

- [微信API文档](http://localhost:8000/api/sendMsg) - 微信API接口说明
- [Flask应用主页](http://localhost:5000/) - 应用主页
- [聊天记录查看](http://localhost:5000/chatlog) - 聊天记录功能

## 🆕 更新日志

- **v1.0**: 初始版本，支持基本的消息发送功能
- **v1.1**: 添加Web界面和错误处理
- **v1.2**: 增加测试脚本和文档
