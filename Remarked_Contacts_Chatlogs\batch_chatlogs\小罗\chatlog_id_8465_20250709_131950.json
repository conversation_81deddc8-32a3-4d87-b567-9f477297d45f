{"user": "小罗", "contact_id": "8465", "contact_info": {"UserName": "heiz<PERSON><PERSON>", "Alias": "", "Remark": "8465  赵静", "NickName": "黑子🌰"}, "fetch_time": "2025-07-09T13:19:50.430390", "message_count": 40, "chatlog_data": [{"seq": 1681454426000, "time": "2023-04-14T14:40:26+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵博士您好，很久没联系您了，您现在在国内工作了吗？"}, {"seq": 1681463314000, "time": "2023-04-14T17:08:34+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "还没有"}, {"seq": 1681463331000, "time": "2023-04-14T17:08:51+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "不过我计划今年9月回去看看家人"}, {"seq": 1681463337000, "time": "2023-04-14T17:08:57+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "您好"}, {"seq": 1681463367000, "time": "2023-04-14T17:09:27+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "我在想有没有远程工作的职位"}, {"seq": 1681463474000, "time": "2023-04-14T17:11:14+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "有的博士，有远程合作的企业"}, {"seq": 1681463521000, "time": "2023-04-14T17:12:01+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "如果我能力范围内的职位我想试试"}, {"seq": 1681463565000, "time": "2023-04-14T17:12:45+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1681463596000, "time": "2023-04-14T17:13:16+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您方便发一份简历吗?"}, {"seq": 1681464578000, "time": "2023-04-14T17:29:38+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "我今天晚一点时候发给您"}, {"seq": 1681464591000, "time": "2023-04-14T17:29:51+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的 麻烦赵博了"}, {"seq": 1681464609000, "time": "2023-04-14T17:30:09+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "您别这么客气 我叫赵静"}, {"seq": 1681464621000, "time": "2023-04-14T17:30:21+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": ""}, {"seq": 1681696975000, "time": "2023-04-17T10:02:55+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵静博士上午好，您今天有空的时候方便发一份您的简历吗[愉快]"}, {"seq": 1688632597000, "time": "2023-07-06T16:36:37+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 49, "subType": 6, "content": "", "contents": {"md5": "123fb443be4485f8c772157a6d96673c", "title": "CV_Jing_Zhao.pdf"}}, {"seq": 1688632863000, "time": "2023-07-06T16:41:03+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到赵博"}, {"seq": 1688640454000, "time": "2023-07-06T18:47:34+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "抱歉我发的这么晚 那天扭脸就忘了 微信我电脑同时登陆 就总错过新消息提示"}, {"seq": 1688689625000, "time": "2023-07-07T08:27:05+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[愉快]没关系的啦"}, {"seq": 1688723007000, "time": "2023-07-07T17:43:27+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "俞赵博您好，根据您的专业领域为您匹配了嘉兴秀洲高新区-嘉兴维亚生物科技有限公司，您看是否有意向了解？（企业介绍/官网、推荐理由）https://www.vivabiotech.com.cn"}, {"seq": 1688723027000, "time": "2023-07-07T17:43:47+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "根据博士的研究领域和嘉兴维亚生物科技有限公司所从事的领域，可以在以下方面合作：\n新型药物研发：企业主要致力于为全球创新药研发企业提供综合服务，而博士在药物研发方面有丰富的经验，特别是在COVID-19疫苗、智能算法和疾病风险评分等方面有深入的研究。可以通过利用自己的研究成果和专业知识，为企业提供药物设计、药物化学、化合物合成和相关的生物活性测试等服务。\n技术平台支持：企业拥有多个先进的技术平台，包括X射线蛋白晶体技术、冷冻电镜技术、亲和力质谱筛选技术、表面等离子共振技术和氢氘交换质谱技术等。博士可以利用自己的研究背景和专业知识，为企业提供技术支持和研究服务，包括数据分析、实验设计和结果解释等方面的问题。\n计算机辅助药物设计：博士在人工智能和机器学习方面的研究经验可以帮助企业开发更高效的药物设计工具和算法。可以提供方法学建议，改善机器学习代码和方法的自包含性和解释性，并帮助企业改善模拟数据和多国研究的质量。\n药物研发的全程支持：博士在大学和研究机构的经历，使他们熟悉科研项目的全程管理和执行。他们可以提供项目管理支持、数据分析和实验设计等方面的帮助，并在文献和报告的撰写方面提供支持。\n通过和博士的合作，企业可以获得创新的研究成果、高效的药物设计工具和算法以及专业的研究支持，从而推动新药研发和临床需求的满足。   "}, {"seq": 1707295935000, "time": "2024-02-07T16:52:15+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "", "contents": {"md5": "5004166554de4838763f4af2186ff0cb"}}, {"seq": 1707295935000, "time": "2024-02-07T16:52:15+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "不辞加一岁，唯喜到三春；[福][福][福]\n正值腊月，岁序更新，华章再续。\n但入新年，在这辞旧迎新之际，家国同乐之时，我谨代表我司全体员工祝您佳节共欢同乐，也向您致以最诚挚的感激，惟愿我司与您的合作在新的一年辰龙而上，逐光而行[烟花][烟花][烟花]"}, {"seq": 1747275129000, "time": "2025-05-15T10:12:09+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵博 您好 最近还有在考虑国内的工作机会吗？"}, {"seq": 1747617257000, "time": "2025-05-19T09:14:17+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到，赵博，您这边什么时间方便，咱们约一个时间语音沟通一下？"}, {"seq": 1747724439000, "time": "2025-05-20T15:00:39+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵博 现在方便沟通吗？"}, {"seq": 1747724452000, "time": "2025-05-20T15:00:52+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "方便"}, {"seq": 1747724699000, "time": "2025-05-20T15:04:59+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "Real-World Evidence "}, {"seq": 1747724791000, "time": "2025-05-20T15:06:31+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "解析数论"}, {"seq": 1747725721000, "time": "2025-05-20T15:22:01+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": ""}, {"seq": 1747725801000, "time": "2025-05-20T15:23:21+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": "你撤回了一条消息"}, {"seq": 1747725823000, "time": "2025-05-20T15:23:43+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 10000, "subType": 0, "content": "你撤回了一条消息"}, {"seq": 1747725868000, "time": "2025-05-20T15:24:28+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "", "contents": {"md5": "a8ddbe1e6ee1ac3d05aa8f20555e5ae3"}}, {"seq": 1747725868000, "time": "2025-05-20T15:24:28+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 49, "subType": 6, "content": "", "contents": {"md5": "31733ec6d8a558289d7c69112e3df388", "title": "收集附件专用文件夹模板(1).rar"}}, {"seq": 1747725896000, "time": "2025-05-20T15:24:56+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": ".zip我可以打开"}, {"seq": 1747725898000, "time": "2025-05-20T15:24:58+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵博 这个是现在申报项目需要准备材料"}, {"seq": 1747725913000, "time": "2025-05-20T15:25:13+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "里面都是空的文件夹"}, {"seq": 1747725987000, "time": "2025-05-20T15:26:27+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "heiz<PERSON><PERSON>", "senderName": "8465  赵静", "isSelf": false, "type": 1, "subType": 0, "content": "哦 好的"}, {"seq": 1748393773000, "time": "2025-05-28T08:56:13+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵博 您的材料什么时候可以准备好啊？"}, {"seq": 1750898547000, "time": "2025-06-26T08:42:27+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵博 您的材料准备好了吗？"}, {"seq": 1751606493000, "time": "2025-07-04T13:21:33+08:00", "talker": "heiz<PERSON><PERSON>", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "赵博 最近是不是很忙啊？"}]}