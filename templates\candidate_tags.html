<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>候选人标签分析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .tag-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        
        .tag-section h3 {
            color: #2196F3;
            margin-top: 0;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 10px;
        }
        
        .tag-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .tag-item {
            background: white;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .tag-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }
        
        .tag-value {
            color: #333;
            font-size: 14px;
        }
        
        .tag-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .tag-summary h3 {
            margin-top: 0;
            color: white;
            border-bottom: 1px solid rgba(255,255,255,0.3);
        }
        
        .summary-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .summary-tag {
            background: rgba(255,255,255,0.2);
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .recommendation-score {
            text-align: center;
            margin: 20px 0;
        }
        
        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        .score-high { background: linear-gradient(45deg, #4CAF50, #45a049); }
        .score-medium { background: linear-gradient(45deg, #FF9800, #F57C00); }
        .score-low { background: linear-gradient(45deg, #f44336, #d32f2f); }
        
        .test-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background-color: #45a049;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-links a {
            color: #4CAF50;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-links">
            <a href="/">← 返回首页</a>
            <a href="/chatlog">聊天记录</a>
            <a href="/send_message">发送消息</a>
        </div>
        
        <h1>🏷️ 候选人标签分析系统</h1>
        
        <!-- 测试表单 -->
        <div class="test-form">
            <h3>📝 测试标签生成</h3>
            <form id="tagForm">
                <div class="form-group">
                    <label for="contact_id">联系人ID:</label>
                    <input type="text" id="contact_id" name="contact_id" value="test123" placeholder="输入联系人ID">
                </div>
                
                <div class="form-group">
                    <label for="contact_name">联系人姓名:</label>
                    <input type="text" id="contact_name" name="contact_name" value="张三" placeholder="输入联系人姓名">
                </div>
                
                <div class="form-group">
                    <label for="sample_messages">模拟聊天内容:</label>
                    <textarea id="sample_messages" name="sample_messages" placeholder="输入一些模拟的聊天内容，用于测试标签生成...">我是一名软件工程师，有5年的Java开发经验。目前在一家互联网公司工作，主要负责后端开发。最近在考虑换工作，希望能找到一个有更好发展前景的职位。我比较看重团队氛围和学习机会，薪资方面希望能有所提升。英语还可以，能够进行基本的工作交流。</textarea>
                </div>
                
                <button type="submit" class="btn">🚀 生成标签</button>
            </form>
        </div>
        
        <div class="loading" id="loading">
            正在分析候选人信息，生成标签...
        </div>
        
        <!-- 标签结果显示区域 -->
        <div id="tagResults" class="hidden">
            <!-- 标签摘要 -->
            <div class="tag-summary">
                <h3>📊 候选人概览</h3>
                <div class="recommendation-score">
                    <div id="scoreCircle" class="score-circle">
                        <span id="scoreValue">0</span>
                    </div>
                    <div>推荐指数</div>
                </div>
                <div class="summary-tags" id="summaryTags">
                    <!-- 动态生成标签摘要 -->
                </div>
            </div>
            
            <!-- 详细标签 -->
            <div id="detailedTags">
                <!-- 动态生成详细标签 -->
            </div>
        </div>
    </div>

    <script>
        document.getElementById('tagForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const contactId = formData.get('contact_id');
            const contactName = formData.get('contact_name');
            const sampleMessages = formData.get('sample_messages');
            
            // 构建模拟聊天数据
            const chatData = {
                contact_info: {
                    id: contactId,
                    remark: contactName
                },
                total_count: 10,
                text_count: 8,
                messages: [
                    {
                        content: sampleMessages,
                        type: 'text',
                        time: '2024-01-15 10:00',
                        isSelf: false
                    },
                    {
                        content: '好的，我了解了',
                        type: 'text',
                        time: '2024-01-15 10:01',
                        isSelf: true
                    }
                ]
            };
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('tagResults').classList.add('hidden');
            
            try {
                const response = await fetch(`/api/generate_tags/${contactId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(chatData)
                });
                
                const result = await response.json();
                
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                
                if (result.status === 'success') {
                    displayTags(result.data);
                } else {
                    alert(`标签生成失败: ${result.message}`);
                }
                
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                alert(`请求失败: ${error.message}`);
            }
        });
        
        function displayTags(tagData) {
            // 显示结果区域
            document.getElementById('tagResults').classList.remove('hidden');
            
            // 显示推荐分数
            const score = tagData.recommendation_score;
            const scoreElement = document.getElementById('scoreValue');
            const scoreCircle = document.getElementById('scoreCircle');
            
            scoreElement.textContent = score;
            
            if (score >= 80) {
                scoreCircle.className = 'score-circle score-high';
            } else if (score >= 60) {
                scoreCircle.className = 'score-circle score-medium';
            } else {
                scoreCircle.className = 'score-circle score-low';
            }
            
            // 显示标签摘要
            const summaryContainer = document.getElementById('summaryTags');
            summaryContainer.innerHTML = '';
            tagData.tag_summary.forEach(tag => {
                const tagElement = document.createElement('div');
                tagElement.className = 'summary-tag';
                tagElement.textContent = tag;
                summaryContainer.appendChild(tagElement);
            });
            
            // 显示详细标签
            const detailedContainer = document.getElementById('detailedTags');
            detailedContainer.innerHTML = '';
            
            const tagSections = [
                { key: 'basic_info', title: '👤 基础信息' },
                { key: 'work_status', title: '💼 工作状态' },
                { key: 'professional_skills', title: '🛠️ 专业技能' },
                { key: 'communication_behavior', title: '💬 沟通行为' },
                { key: 'job_intention', title: '🎯 求职意愿' },
                { key: 'psychological_traits', title: '🧠 心理特征' },
                { key: 'cooperation_potential', title: '🤝 合作潜力' },
                { key: 'risk_assessment', title: '⚠️ 风险评估' },
                { key: 'multimedia_analysis', title: '📱 多媒体分析' },
                { key: 'temporal_tags', title: '⏰ 时间维度' }
            ];
            
            tagSections.forEach(section => {
                const sectionData = tagData.tags[section.key];
                if (sectionData) {
                    const sectionElement = createTagSection(section.title, sectionData);
                    detailedContainer.appendChild(sectionElement);
                }
            });
        }
        
        function createTagSection(title, data) {
            const section = document.createElement('div');
            section.className = 'tag-section';
            
            const titleElement = document.createElement('h3');
            titleElement.textContent = title;
            section.appendChild(titleElement);
            
            const grid = document.createElement('div');
            grid.className = 'tag-grid';
            
            Object.entries(data).forEach(([key, value]) => {
                const item = document.createElement('div');
                item.className = 'tag-item';
                
                const label = document.createElement('div');
                label.className = 'tag-label';
                label.textContent = formatLabel(key);
                
                const valueElement = document.createElement('div');
                valueElement.className = 'tag-value';
                valueElement.textContent = Array.isArray(value) ? value.join(', ') : value;
                
                item.appendChild(label);
                item.appendChild(valueElement);
                grid.appendChild(item);
            });
            
            section.appendChild(grid);
            return section;
        }
        
        function formatLabel(key) {
            const labelMap = {
                'name': '姓名',
                'age_range': '年龄范围',
                'gender': '性别',
                'location': '地点',
                'education': '学历',
                'work_experience': '工作经验',
                'current_status': '当前状态',
                'employment_type': '就业类型',
                'job_seeking': '求职状态',
                'availability': '到岗时间',
                'work_location_preference': '工作地点偏好',
                'salary_expectation': '薪资期望',
                'industry_preference': '行业偏好',
                'primary_field': '主要领域',
                'technical_skills': '技术技能',
                'soft_skills': '软技能',
                'language_skills': '语言技能',
                'certifications': '认证证书',
                'skill_level': '技能水平'
            };
            
            return labelMap[key] || key;
        }
    </script>
</body>
</html>
