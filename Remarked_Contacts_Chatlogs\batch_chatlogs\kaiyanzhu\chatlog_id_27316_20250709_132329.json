{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "27316", "contact_info": {"UserName": "wxid_6586025878312", "Alias": "<PERSON>-<PERSON>", "Remark": "27316 孙业鹏", "NickName": "孙业鹏"}, "fetch_time": "2025-07-09T13:23:29.053987", "message_count": 74, "chatlog_data": [{"seq": 1706580008000, "time": "2024-01-30T10:00:08+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 10000, "subType": 0, "content": "你已添加了孙业鹏，现在可以开始聊天了。"}, {"seq": 1706580016000, "time": "2024-01-30T10:00:16+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "孙博士您好  "}, {"seq": 1706580031000, "time": "2024-01-30T10:00:31+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我是朱筱沁  您叫我小朱就好   "}, {"seq": 1706582404000, "time": "2024-01-30T10:40:04+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "你好"}, {"seq": 1706584985000, "time": "2024-01-30T11:23:05+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "孙博您好  您现在美国加州吗？"}, {"seq": 1706593878000, "time": "2024-01-30T13:51:18+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "是的， 在加州硅谷"}, {"seq": 1706594690000, "time": "2024-01-30T14:04:50+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "孙博  现在加州晚上10点了  会影响您休息吗？"}, {"seq": 1706594939000, "time": "2024-01-30T14:08:59+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "不会"}, {"seq": 1706594993000, "time": "2024-01-30T14:09:53+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的孙博   我和您沟通一下哦   "}, {"seq": 1706595008000, "time": "2024-01-30T14:10:08+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1706596139000, "time": "2024-01-30T14:28:59+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 49, "subType": 0, "content": "<msg>\n    <fromusername>wxid_6586025878312</fromusername>\n    <scene>0</scene>\n    <commenturl></commenturl>\n    <appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n        <title>Resume_YepengSun_Jan_2024.pdf</title>\n        <des></des>\n        <action>view</action>\n        <type>6</type>\n        <showtype>0</showtype>\n        <content></content>\n        <url></url>\n        <dataurl></dataurl>\n        <lowurl></lowurl>\n        <lowdataurl></lowdataurl>\n        <recorditem></recorditem>\n        <thumburl></thumburl>\n        <messageaction></messageaction>\n        <laninfo></laninfo>\n        <md5>95a6ded4b6e2784cc88f9745bb516d12</md5>\n        <extinfo></extinfo>\n        <sourceusername></sourceusername>\n        <sourcedisplayname></sourcedisplayname>\n        <commenturl></commenturl>\n        <appattach>\n            <totallen>92482</totallen>\n            <attachid>@cdn_3052020100044b30490201000204d697914202030f5efb02041fc2822b020465b89734042438313137363835612d623933612d346438662d386461392d3532363566393738616561300204011c00050201000400_250bf3517222aef30316847208d4b9cb_1</attachid>\n            <emoticonmd5></emoticonmd5>\n            <fileext>pdf</fileext>\n            <fileuploadtoken>v1_vh9tkT/D4Tab/l9pXG+Y6GYPZvaht82PeKwZFncggi/9pJ67/QEj0DwZFYUlSZnQsj3RPGswwiuOyvH03lQxao82Q0X/xQP2YOp4ps3moqNFGzu+EYdsE8zSpnEmK0Lmz5UistdGl2mnTxIK0mi6YO3TPXPouP2uFOFVRpARd2ygDuhuxeoUnaVHDNHrj5q9616n2p+RzZShSU98gQ4FRT2VxdYx0CdsMkzGRJZyZmMYR+ERKjn7If7N</fileuploadtoken>\n            <overwrite_newmsgid>1998745353465306428</overwrite_newmsgid>\n            <filekey>2414f51b852b0a4a005705059869d8cc</filekey>\n            <cdnattachurl>3052020100044b30490201000204d697914202030f5efb02041fc2822b020465b89734042438313137363835612d623933612d346438662d386461392d3532363566393738616561300204011c00050201000400</cdnattachurl>\n            <aeskey>250bf3517222aef30316847208d4b9cb</aeskey>\n            <encryver>&#x01;</encryver>\n            <uploadstatus>2</uploadstatus>\n        </appattach>\n        <webviewshared>\n            <publisherId></publisherId>\n            <publisherReqId>0</publisherReqId>\n        </webviewshared>\n        <weappinfo>\n            <pagepath></pagepath>\n            <username></username>\n            <appid></appid>\n            <appservicetype>0</appservicetype>\n        </weappinfo>\n        <websearch />\n    </appmsg>\n    <appinfo>\n        <version>1</version>\n        <appname>微信电脑版</appname>\n    </appinfo>\n</msg>\n\u0000"}, {"seq": 1706596160000, "time": "2024-01-30T14:29:20+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的    孙博   已收到您的CV   "}, {"seq": 1706596185000, "time": "2024-01-30T14:29:45+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您是一个很爽快的人   非常高兴认识您   "}, {"seq": 1706596189000, "time": "2024-01-30T14:29:49+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[呲牙]"}, {"seq": 1706596212000, "time": "2024-01-30T14:30:12+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢！[微笑]"}, {"seq": 1707017444000, "time": "2024-02-04T11:30:44+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 51, "content": "<msg>\n    <fromusername>wxid_6ahbbsar7g8i22</fromusername>\n    <scene>0</scene>\n    <commenturl></commenturl>\n    <appmsg appid=\"\" sdkver=\"0\">\n        <title>美立中国的动态</title>\n        <des></des>\n        <action>view</action>\n        <type>51</type>\n        <showtype>0</showtype>\n        <content></content>\n        <url>https://support.weixin.qq.com/update/</url>\n        <dataurl></dataurl>\n        <lowurl></lowurl>\n        <lowdataurl></lowdataurl>\n        <recorditem></recorditem>\n        <thumburl></thumburl>\n        <messageaction></messageaction>\n        <laninfo></laninfo>\n        <extinfo></extinfo>\n        <sourceusername></sourceusername>\n        <sourcedisplayname></sourcedisplayname>\n        <commenturl></commenturl>\n        <appattach>\n            <totallen>0</totallen>\n            <attachid></attachid>\n            <emoticonmd5></emoticonmd5>\n            <fileext></fileext>\n            <aeskey></aeskey>\n        </appattach>\n        <webviewshared>\n            <publisherId></publisherId>\n            <publisherReqId>0</publisherReqId>\n        </webviewshared>\n        <weappinfo>\n            <pagepath></pagepath>\n            <username></username>\n            <appid></appid>\n            <appservicetype>0</appservicetype>\n        </weappinfo>\n        <websearch />\n        <finderFeed>\n            <objectId>14318941147185744067</objectId>\n            <objectNonceId>16644270569641015916_0_0_0_0_0</objectNonceId>\n            <feedType>4</feedType>\n            <nickname>美立中国</nickname>\n            <username>v2_060000231003b20faec8c5e08f1ac1d7c707ee31b077f1cc5653c7436ba812f124e3ac74d88e@finder</username>\n            <authIconUrl>https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png</authIconUrl>\n            <authIconType>1</authIconType>\n            <avatar>https://wx.qlogo.cn/finderhead/ver_1/hibpZvbkqhZBhDw0g6vvH9FGIb6WvV8RYkPBT34EnrIOmtq2vS8lfZyFuzGsoDpQI8YrP3F7TgNVCWWyUqbGiaNYoAibsSTWBicYI6qa8hcMpibA/0</avatar>\n            <desc>立春，一年中的第一个节气。立是开始，春是希望，万物复苏，好事正酿，人随春好，春与人安。#立春#节气#新年倒计时</desc>\n            <mediaCount>1</mediaCount>\n            <localId>0</localId>\n            <bizUsername></bizUsername>\n            <bizNickname></bizNickname>\n            <bizAvatar></bizAvatar>\n            <bizUsernameV2></bizUsernameV2>\n            <mediaList>\n                <media>\n                    <thumbUrl>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzmj2zngWM1SaY6Kohqx5ZoAHBLyHBLQRXQy2gPgBRUDOLGz6JNYtKpyFuNwMRBKia92ALjKkRBfLlIh4U8Hiay4hg&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=KkOFht0mCXmbbb24JV62hCqpynhxH8IoV6twYm3phnzyY8mGiaIkhUMnM2rapfMXNnW0qy8VRicL05Jyol6lcNRGKKqeMBrFuknJLuicDFFTiaA&ctsc=2-17]]>\n                    </thumbUrl>\n                    <videoPlayDuration>26</videoPlayDuration>\n                    <url>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDbd5C4rw9biboXT2QZ0ebwQgRAwOA1tKLgydW3ibianromHQ46jjHiaibM36myo1nJicOltiaCv8QdKDoIxWZZwU2fFLe&bizid=1023&dotrans=0&hy=SH&idx=1&m=&upid=0]]>\n                    </url>\n                    <coverUrl>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzmj2zngWM1SaY6Kohqx5ZoAHBLyHBLQRXQy2gPgBRUDOLGz6JNYtKpyFuNwMRBKia92ALjKkRBfLlIh4U8Hiay4hg&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=KkOFht0mCXmbbb24JV62hCqpynhxH8IoV6twYm3phnzyY8mGiaIkhUMnM2rapfMXNnW0qy8VRicL05Jyol6lcNRGKKqeMBrFuknJLuicDFFTiaA&ctsc=2-17]]>\n                    </coverUrl>\n                    <height>\n                        <![CDATA[1920]]>\n                    </height>\n                    <width>\n                        <![CDATA[1080]]>\n                    </width>\n                    <mediaType>\n                        <![CDATA[4]]>\n                    </mediaType>\n                    <fullCoverUrl>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzmj2zngWM1SaY6Kohqx5ZoAHBLyHBLQRXQy2gPgBRUDOLGz6JNYtKpyFuNwMRBKia92ALjKkRBfLlIh4U8Hiay4hg&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=ic1n0xDG6awicH6MdcuPR8yf9V1g5iaerNCsMtQUXm73zmBzO4VYacibuBfOiazsZicQUvXLY3pw0NcPM72JGLdIvM0wib4vffBu1nyiaCnJ6RiaXvSo&ctsc=3-17]]>\n                    </fullCoverUrl>\n                </media>\n            </mediaList>\n        </finderFeed>\n    </appmsg>\n    <appinfo>\n        <version>1</version>\n        <appname>Window wechat</appname>\n    </appinfo>\n</msg>\n\u0000", "contents": {"title": "立春，一年中的第一个节气。立是开始，春是希望，万物复苏，好事正酿，人随春好，春与人安。#立春#节气#新年倒计时", "url": "\n                        http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDbd5C4rw9biboXT2QZ0ebwQgRAwOA1tKLgydW3ibianromHQ46jjHiaibM36myo1nJicOltiaCv8QdKDoIxWZZwU2fFLe&bizid=1023&dotrans=0&hy=SH&idx=1&m=&upid=0\n                    "}}, {"seq": 1707017453000, "time": "2024-02-04T11:30:53+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "人随春好，春与人安。孙博士，立春快乐"}, {"seq": 1707019173000, "time": "2024-02-04T11:59:33+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "立春快乐！ 过个好年！ [呲牙]"}, {"seq": 1707040019000, "time": "2024-02-04T17:46:59+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "还没休假呢"}, {"seq": 1707040022000, "time": "2024-02-04T17:47:02+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[捂脸]"}, {"seq": 1707063422000, "time": "2024-02-05T00:17:02+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "我们这边没有春节假[愉快]"}, {"seq": 1707093907000, "time": "2024-02-05T08:45:07+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "辛苦了   孙博  "}, {"seq": 1707093923000, "time": "2024-02-05T08:45:23+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername=\"wxid_6ahbbsar7g8i22\" tousername=\"wxid_6586025878312\" type=\"2\" androidmd5=\"753191348a9f21a208f5cc0f43edf768\" androidlen=\"23574\" aeskey=\"ff46fcb90046a4b7ff9ff080211cf2e3\" encrypturl=\"http://wxapp.tc.qq.com/262/20304/stodownload?m=d765b638134e534abec84230b682f407&amp;filekey=30350201010421301f02020106040253480410d765b638134e534abec84230b682f4070203008770040d00000004627466730000000132&amp;hy=SH&amp;storeid=26396e1eb00053c90b4590a760000010600004f50534813c128e0b73373e0f&amp;bizid=1023\" externurl=\"http://wxapp.tc.qq.com/262/20304/stodownload?m=88f608f486cd7e5ce9727a9b1d26c6f7&amp;filekey=30340201010420301e020201060402535a041088f608f486cd7e5ce9727a9b1d26c6f702025c20040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26396edd00002fe3f6b8bfa760000010600004f50535a17c8c970b7cf94b8d&amp;bizid=1023\" externmd5=\"6d64b3b42faf18bb526a5cd7679ec625\"/></msg>"}, {"seq": 1707103074000, "time": "2024-02-05T11:17:54+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "过年才辛苦， 又要春运， 又要走亲戚[呲牙]"}, {"seq": 1707103301000, "time": "2024-02-05T11:21:41+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "哈哈哈哈  说到了痛点[捂脸]"}, {"seq": 1707405909000, "time": "2024-02-08T23:25:09+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢！ 春节快乐！ "}, {"seq": 1708222225000, "time": "2024-02-18T10:10:25+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "孙博您好"}, {"seq": 1708222257000, "time": "2024-02-18T10:10:57+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您申报阶段是申报的时候就提交唯一申报视频了吗？"}, {"seq": 1708225683000, "time": "2024-02-18T11:08:03+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "是的"}, {"seq": 1708225714000, "time": "2024-02-18T11:08:34+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "我觉得这个更像一个个人承诺"}, {"seq": 1708225748000, "time": "2024-02-18T11:09:08+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "但是也没有必要重复申报， 一个人的资料是非常有限的"}, {"seq": 1708226238000, "time": "2024-02-18T11:17:18+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "是的   ", "contents": {"refer": {"seq": 0, "time": "2024-02-18T11:09:08+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "但是也没有必要重复申报， 一个人的资料是非常有限的"}}}, {"seq": 1708226268000, "time": "2024-02-18T11:17:48+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "让您录制的时候和您沟通了视频是用于什么吗？"}, {"seq": 1708226305000, "time": "2024-02-18T11:18:25+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我现在不解的是，我们也沟通过让博士录制视频"}, {"seq": 1708226326000, "time": "2024-02-18T11:18:46+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "就是一个个人承诺， 不通过别的渠道申请启明人才计划"}, {"seq": 1708226332000, "time": "2024-02-18T11:18:52+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "有一些会比较抗拒，大部分都接受文字协议的形式  "}, {"seq": 1708226372000, "time": "2024-02-18T11:19:32+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "我个人觉得没啥， 本来规则就是不允许重复申报"}, {"seq": 1708226504000, "time": "2024-02-18T11:21:44+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的，您也是比较直接通透，规则就是这样的[抱拳]"}, {"seq": 1708226551000, "time": "2024-02-18T11:22:31+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "今天按国内的安排   统一复工了   "}, {"seq": 1708226676000, "time": "2024-02-18T11:24:36+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "孙博   那家中介伙伴叫什么啊，我们在宁波也有办公地点，说不定我们还线下聊过呢", "contents": {"refer": {"seq": 0, "time": "2024-02-18T11:19:32+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "我个人觉得没啥， 本来规则就是不允许重复申报"}}}, {"seq": 1708226700000, "time": "2024-02-18T11:25:00+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我去内部了解一下您的申报进度  [呲牙]"}, {"seq": 1708226808000, "time": "2024-02-18T11:26:48+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "中介伙伴和依托单位  方便发送一下吗？我看看我们有没有单位负责人的联系方式"}, {"seq": 1708226852000, "time": "2024-02-18T11:27:32+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "这个不好说吧。 "}, {"seq": 1708226973000, "time": "2024-02-18T11:29:33+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没关系呀   我们和海角、聚齐、红梦等等都有一些沟通的   "}, {"seq": 1708226988000, "time": "2024-02-18T11:29:48+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "有时候也会政府组织会议   "}, {"seq": 1708227011000, "time": "2024-02-18T11:30:11+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "大家也会聚到一起  "}, {"seq": 1708229652000, "time": "2024-02-18T12:14:12+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "去年他们也给我申过， 但没成， 主要是匹配企业不合适。 今年的比较合适。 今年的负责人比较用心。 如果今年不成， 明年可以找你们试试， 也可以找江浙之外的省份试试。 我觉得我的case 不属于国家想搞的“硬科技”, 硬科技的东西诸如材料、硬件半导体、生化制药往往在美国工资不高， 至少在升到一定职位前，是不高的。 我的领域对标：应用、数据科学、软件。 应该是个小case, 成了也拿不到多少钱。 我回去的目标有两个：一， 丰富个人人生体验。 二， 通过一份工作，多认识些社会关系，理解市场 为可能的创业做准备。 软科技创业， 对于一个工作多年的人来说， 技术不是瓶颈， 对接市场才是。 "}, {"seq": 1708229838000, "time": "2024-02-18T12:17:18+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "目前协助我申报的负责人， 比较精明， 明显是想把我“卖”两次， 一次是这个人才计划本身， 另外一个是这家企业。 我觉得， 这个人才计划， 你就别想了。 但是企业匹配， 如果你手头有合适的， 可以把我给卖了。 理论上， 这个人才计划和企业招聘最后可以是分离的"}, {"seq": 1708234322000, "time": "2024-02-18T13:32:02+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "是的   孙博 ，人才计划和企业招聘是可以分离的", "contents": {"refer": {"seq": 0, "time": "2024-02-18T12:17:18+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "目前协助我申报的负责人， 比较精明， 明显是想把我“卖”两次， 一次是这个人才计划本身， 另外一个是这家企业。 我觉得， 这个人才计划， 你就别想了。 但是企业匹配， 如果你手头有合适的， 可以把我给卖了。 理论上， 这个人才计划和企业招聘最后可以是分离的"}}}, {"seq": 1714023716000, "time": "2024-04-25T13:41:56+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "孙博，我们现在有一个杭州创业大赛诚邀您参加，这是大赛链接，您看看有没有兴趣？https://techtalentsuk.com/blog-2/"}, {"seq": 1714024542000, "time": "2024-04-25T13:55:42+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "让我考虑考虑， 目前正在找工作， 准备面试。 如果找不到， 也许就会选择回国旅居， 有时间把过去的想法做一做。 到时就有时间专门搞一搞。 "}, {"seq": 1714024587000, "time": "2024-04-25T13:56:27+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的    孙博，现在回国面试了吗？"}, {"seq": 1714024596000, "time": "2024-04-25T13:56:36+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "还是加州的机会呀？"}, {"seq": 1714024603000, "time": "2024-04-25T13:56:43+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "加州的机会"}, {"seq": 1714024651000, "time": "2024-04-25T13:57:31+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   "}, {"seq": 1714024743000, "time": "2024-04-25T13:59:03+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "祝您成功  "}, {"seq": 1714024749000, "time": "2024-04-25T13:59:09+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "一切顺利   "}, {"seq": 1714024753000, "time": "2024-04-25T13:59:13+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[强]"}, {"seq": 1745908227000, "time": "2025-04-29T14:30:27+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士，您好，您今年还考虑看国内的工作机会吗"}, {"seq": 1745917192000, "time": "2025-04-29T16:59:52+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "考虑呀"}, {"seq": 1745917224000, "time": "2025-04-29T17:00:24+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "但是我年龄大了， 估计打工国内不要了[呲牙]"}, {"seq": 1745917269000, "time": "2025-04-29T17:01:09+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们现在主要是在筹备浙江省的人才项目，年龄这块限制没有很大的哦，60以下都可以的捏"}, {"seq": 1745917311000, "time": "2025-04-29T17:01:51+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "今年的Qm 我已经申请了， 苏州"}, {"seq": 1745917318000, "time": "2025-04-29T17:01:58+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "只是说这个项目需要博士全职回国的，不知道机会合适的话您是否会考虑全职回国嘞"}, {"seq": 1745917333000, "time": "2025-04-29T17:02:13+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "国家级和省级的是各自独立的项目，不冲突的"}, {"seq": 1745917352000, "time": "2025-04-29T17:02:32+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "可以全职回国"}, {"seq": 1745917358000, "time": "2025-04-29T17:02:38+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "到时候哪边入选您选哪边落地就可以的"}, {"seq": 1745917388000, "time": "2025-04-29T17:03:08+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那您对于薪资和企业平台这块有什么要求吗"}, {"seq": 1745917456000, "time": "2025-04-29T17:04:16+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "薪资可谈， 企业平台， 当然要能发挥我专长"}, {"seq": 1745917476000, "time": "2025-04-29T17:04:36+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"\" sdkver=\"0\">\n\t\t<title>孙业鹏简历.docx</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>6</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid>@cdn_010b0006022071d1af8144e47435afbb0d4e92058309935b14572030626f069972099312164103010500c08e010004010503d854000006fbbd3d07abdec2b40208029ec8c0f401029ec8c0fc020209000a030b000ca4acc2c0060d010e030f0610fca5a5eb071104180042b6_68d5a055f9042cedb7e12b67b472156a_1</attachid>\n\t\t\t<cdnattachurl>010b0006022071d1af8144e47435afbb0d4e92058309935b14572030626f069972099312164103010500c08e010004010503d854000006fbbd3d07abdec2b40208029ec8c0f401029ec8c0fc020209000a030b000ca4acc2c0060d010e030f0610fca5a5eb071104180042b6</cdnattachurl>\n\t\t\t<totallen>18233</totallen>\n\t\t\t<aeskey>68d5a055f9042cedb7e12b67b472156a</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext>docx</fileext>\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t\t<overwrite_newmsgid>3827389705059096325</overwrite_newmsgid>\n\t\t\t<fileuploadtoken><![CDATA[v1_PKCsrkgay9wL89uzfY6wKsw1fIIsbhNbQm17Dx6y6+sFY//flxVdWX52It/LFxt4MTm8Pm5ssHF4mtG/Si/xj1KM/CZ4iAakSy7vtBD6W9xjWol83FhDyi5ksc1qOZJapo+okPJmEdeEKallccln54w80dYM/gDFF9uC/lNRYdf9bEaFz+r07M9xzk2C0cVXhRTa7ddA3EUE]]></fileuploadtoken>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl />\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>ae7c701f75471028b0ce763adff20009</md5>\n\t\t<websearch />\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>0</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_6586025878312</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n\u0000", "contents": {"md5": "ae7c701f75471028b0ce763adff20009", "title": "孙业鹏简历.docx"}}, {"seq": 1745917544000, "time": "2025-04-29T17:05:44+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您会说只考虑上市公司吗，初创型的您考虑吗"}, {"seq": 1745917561000, "time": "2025-04-29T17:06:01+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "wxid_6586025878312", "senderName": "27316 孙业鹏", "isSelf": false, "type": 1, "subType": 0, "content": "初创考虑"}, {"seq": 1745917581000, "time": "2025-04-29T17:06:21+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "OKOK"}, {"seq": 1745917595000, "time": "2025-04-29T17:06:35+08:00", "talker": "wxid_6586025878312", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那我这边就为您匹配下单位哦，有合适的我发给您看哦~"}]}