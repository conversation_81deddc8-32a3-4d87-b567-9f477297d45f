# 根据ID获取聊天记录工具

import json
import re
import requests
import urllib.parse
from pathlib import Path
from datetime import datetime
import time

class ChatlogByIdFetcher:
    def __init__(self):
        self.data_dir = Path("../data")
        self.output_dir = Path(".")
        self.excluded_folders = {"test_ip_change_user"}
        self.remark_pattern = re.compile(r'^\d+.*$')
        
    def extract_ip_from_filename(self, filename):
        """从文件名中提取IP地址"""
        parts = filename.replace('.json', '').split('_')
        if len(parts) >= 4:
            ip_parts = parts[:4]
            ip_address = '.'.join(ip_parts)
            return ip_address
        return None
    
    def extract_id_from_remark(self, remark):
        """从备注中提取ID（前面的数字）"""
        if not remark:
            return None
        
        match = re.match(r'^(\d+)', str(remark).strip())
        if match:
            return match.group(1)
        return None
    
    def get_user_contacts_and_ip(self, user_folder):
        """获取用户的联系人数据和IP地址"""
        username = user_folder.name
        
        contact_files = [f for f in user_folder.glob("*.json") 
                        if not f.name.startswith(("new_contacts", "ip_change"))]
        
        if not contact_files:
            return None, None, None
        
        latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
        ip_address = self.extract_ip_from_filename(latest_file.name)
        
        if not ip_address:
            return None, None, None
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                contacts = json.load(f)
            
            if not isinstance(contacts, list):
                return None, None, None
            
            return contacts, ip_address, latest_file.name
            
        except Exception as e:
            print(f"   ❌ {username}: 读取联系人文件失败 - {e}")
            return None, None, None
    
    def find_contact_by_id(self, contacts, target_id):
        """根据ID查找联系人"""
        for contact in contacts:
            remark = contact.get("Remark", "")
            if remark is None:
                remark = ""
            else:
                remark = str(remark).strip()
            
            if remark and self.remark_pattern.match(remark):
                contact_id = self.extract_id_from_remark(remark)
                if contact_id == target_id:
                    return contact
        return None
    
    def get_talker_value(self, contact):
        """获取talker参数值，按优先级：Remark > NickName > Alias > UserName"""
        for field in ['Remark', 'NickName', 'Alias', 'UserName']:
            value = contact.get(field, '')
            if value is None:
                value = ''
            else:
                value = str(value).strip()
            if value:
                return value
        return None
    
    def fetch_chatlog_by_api(self, ip_address, contact, start_date="2023-01-01", end_date=None):
        """通过API获取聊天记录"""
        talker = self.get_talker_value(contact)
        if not talker:
            return None, "无有效talker参数"
        
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        time_range = f"{start_date}%7E{end_date}"
        encoded_talker = urllib.parse.quote(talker)
        
        api_url = f"http://{ip_address}:5030/api/v1/chatlog?time={time_range}&talker={encoded_talker}&format=json"
        
        print(f"   🔗 API URL: {api_url}")
        
        try:
            response = requests.get(api_url, timeout=15)
            response.raise_for_status()
            
            chatlog_data = response.json()
            return chatlog_data, None
            
        except requests.exceptions.Timeout:
            return None, "请求超时"
        except requests.exceptions.ConnectionError:
            return None, "连接失败"
        except requests.exceptions.HTTPError as e:
            return None, f"HTTP错误: {e}"
        except json.JSONDecodeError as e:
            return None, f"JSON解析失败: {e}"
        except Exception as e:
            return None, f"未知错误: {e}"
    
    def format_chatlog_display(self, chatlog_data, max_display=20):
        """格式化聊天记录用于显示"""
        if not chatlog_data or not isinstance(chatlog_data, list):
            return "无聊天记录"
        
        if len(chatlog_data) == 0:
            return "无聊天记录"
        
        print(f"   📊 共找到 {len(chatlog_data)} 条聊天记录")
        
        # 显示最近的消息
        recent_messages = chatlog_data[-max_display:] if len(chatlog_data) > max_display else chatlog_data
        
        formatted_lines = []
        for i, msg in enumerate(recent_messages, 1):
            # 根据实际API返回格式调整字段名
            timestamp = msg.get('timestamp', msg.get('time', ''))
            sender = msg.get('sender', msg.get('from', ''))
            content = msg.get('content', msg.get('message', ''))
            msg_type = msg.get('type', msg.get('msgType', ''))
            
            # 格式化时间
            if timestamp:
                try:
                    if 'T' in timestamp:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    else:
                        dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                    time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                except:
                    time_str = str(timestamp)
            else:
                time_str = "未知时间"
            
            # 处理消息内容
            if msg_type == 'text' or not msg_type:
                msg_content = str(content)
            elif msg_type == 'image':
                msg_content = "[图片消息]"
            elif msg_type == 'voice':
                msg_content = "[语音消息]"
            elif msg_type == 'video':
                msg_content = "[视频消息]"
            elif msg_type == 'file':
                msg_content = "[文件消息]"
            else:
                msg_content = f"[{msg_type}消息]"
            
            # 限制内容长度
            if len(msg_content) > 100:
                msg_content = msg_content[:100] + "..."
            
            formatted_line = f"   {i:2d}. {time_str} | {msg_content}"
            formatted_lines.append(formatted_line)
        
        result = "\n".join(formatted_lines)
        
        if len(chatlog_data) > max_display:
            result = f"   [显示最近{max_display}条，共{len(chatlog_data)}条]\n" + result
        
        return result
    
    def save_chatlog_to_file(self, chatlog_data, contact_info, username, contact_id):
        """保存聊天记录到文件"""
        output_data = {
            "user": username,
            "contact_id": contact_id,
            "contact_info": {
                "UserName": contact_info.get("UserName", ""),
                "Alias": contact_info.get("Alias", ""),
                "Remark": contact_info.get("Remark", ""),
                "NickName": contact_info.get("NickName", "")
            },
            "fetch_time": datetime.now().isoformat(),
            "message_count": len(chatlog_data) if isinstance(chatlog_data, list) else 0,
            "chatlog_data": chatlog_data
        }
        
        # 创建用户文件夹
        user_output_dir = self.output_dir / username
        user_output_dir.mkdir(exist_ok=True)
        
        # 保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"chatlog_id_{contact_id}_{timestamp}.json"
        output_file = user_output_dir / filename
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"   💾 聊天记录已保存到: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"   ❌ 保存文件失败: {e}")
            return None
    
    def get_chatlog_by_id(self, target_id, username=None, save_to_file=False):
        """根据ID获取聊天记录"""
        print(f"🔍 查找ID为 {target_id} 的联系人聊天记录...")
        print("="*60)
        
        if not self.data_dir.exists():
            print("❌ data目录不存在")
            return False
        
        # 确定搜索范围
        if username:
            user_folders = [self.data_dir / username]
            if not user_folders[0].exists():
                print(f"❌ 用户文件夹 {username} 不存在")
                return False
        else:
            user_folders = [d for d in self.data_dir.iterdir() 
                           if d.is_dir() and d.name not in self.excluded_folders]
        
        found_contact = None
        found_username = None
        found_ip = None
        
        # 搜索联系人
        for user_folder in user_folders:
            current_username = user_folder.name
            print(f"\n👤 搜索用户: {current_username}")
            
            contacts, ip_address, filename = self.get_user_contacts_and_ip(user_folder)
            if not contacts:
                continue
            
            print(f"   📄 文件: {filename}")
            print(f"   🌐 IP地址: {ip_address}")
            
            # 查找目标联系人
            contact = self.find_contact_by_id(contacts, target_id)
            if contact:
                found_contact = contact
                found_username = current_username
                found_ip = ip_address
                print(f"   ✅ 找到联系人!")
                print(f"       备注: {contact.get('Remark', '')}")
                print(f"       昵称: {contact.get('NickName', '')}")
                print(f"       用户名: {contact.get('UserName', '')}")
                break
            else:
                print(f"   📝 未找到ID为 {target_id} 的联系人")
        
        if not found_contact:
            print(f"\n❌ 在所有用户中都未找到ID为 {target_id} 的联系人")
            return False
        
        # 获取聊天记录
        print(f"\n📱 获取聊天记录...")
        chatlog_data, error = self.fetch_chatlog_by_api(found_ip, found_contact)
        
        if chatlog_data is not None:
            print(f"   ✅ 成功获取聊天记录")
            
            # 显示聊天记录
            print(f"\n💬 聊天记录预览:")
            print("-" * 50)
            formatted_display = self.format_chatlog_display(chatlog_data)
            print(formatted_display)
            
            # 保存到文件
            if save_to_file:
                self.save_chatlog_to_file(chatlog_data, found_contact, found_username, target_id)
            
            return True
        else:
            print(f"   ❌ 获取聊天记录失败: {error}")
            return False
    
    def batch_get_by_ids(self, id_list, username=None, save_to_file=False):
        """批量根据ID获取聊天记录"""
        print(f"🔍 批量获取 {len(id_list)} 个ID的聊天记录...")
        print("="*60)
        
        success_count = 0
        for i, target_id in enumerate(id_list, 1):
            print(f"\n[{i}/{len(id_list)}] 处理ID: {target_id}")
            if self.get_chatlog_by_id(target_id, username, save_to_file):
                success_count += 1
            
            # 添加延迟避免请求过于频繁
            if i < len(id_list):
                time.sleep(1)
        
        print(f"\n🎉 批量处理完成! 成功: {success_count}/{len(id_list)}")
        return success_count

def main():
    """主函数"""
    print("📱 根据ID获取聊天记录工具")
    print("="*60)
    
    fetcher = ChatlogByIdFetcher()
    
    import sys
    if len(sys.argv) > 1:
        # 命令行模式
        target_id = sys.argv[1]
        username = sys.argv[2] if len(sys.argv) > 2 else None
        save_file = len(sys.argv) > 3 and sys.argv[3].lower() == 'save'
        
        fetcher.get_chatlog_by_id(target_id, username, save_file)
    else:
        # 交互式模式
        while True:
            print("\n" + "="*40)
            print("📱 聊天记录查询选项")
            print("="*40)
            print("1. 根据单个ID查询")
            print("2. 批量ID查询")
            print("3. 退出")
            
            choice = input("\n请选择 (1-3): ").strip()
            
            if choice == '1':
                target_id = input("请输入ID: ").strip()
                if not target_id:
                    print("❌ ID不能为空")
                    continue
                
                username = input("指定用户名(留空搜索所有用户): ").strip()
                username = username if username else None
                
                save_file = input("是否保存到文件? (y/N): ").strip().lower() == 'y'
                
                fetcher.get_chatlog_by_id(target_id, username, save_file)
                
            elif choice == '2':
                ids_input = input("请输入多个ID(用逗号分隔): ").strip()
                if not ids_input:
                    print("❌ ID列表不能为空")
                    continue
                
                id_list = [id.strip() for id in ids_input.split(',') if id.strip()]
                if not id_list:
                    print("❌ 没有有效的ID")
                    continue
                
                username = input("指定用户名(留空搜索所有用户): ").strip()
                username = username if username else None
                
                save_file = input("是否保存到文件? (y/N): ").strip().lower() == 'y'
                
                fetcher.batch_get_by_ids(id_list, username, save_file)
                
            elif choice == '3':
                break
            else:
                print("❌ 无效选择")

if __name__ == "__main__":
    main()
