"""
测试自动创建用户文件夹功能
模拟新用户提交联系人数据
"""

import requests
import json
import time
import os
from pathlib import Path

def test_new_user_auto_folder():
    """测试新用户自动创建文件夹"""
    print("="*60)
    print("测试新用户自动文件夹创建功能")
    print("="*60)
    
    # 生成一个新的测试用户
    timestamp = int(time.time())
    new_username = f"新用户_{timestamp}"
    
    print(f"🧪 测试用户: {new_username}")
    print(f"📋 Client ID: 192_168_1_200_{new_username}")
    
    # 模拟联系人数据
    test_contacts = [
        {
            "UserName": f"contact_{timestamp}_001",
            "NickName": "测试联系人A",
            "Alias": "",
            "Remark": f"这是{new_username}的联系人"
        },
        {
            "UserName": f"contact_{timestamp}_002",
            "NickName": "测试联系人B",
            "Alias": "testB",
            "Remark": "第二个测试联系人"
        },
        {
            "UserName": "filehelper",
            "NickName": "文件传输助手",
            "Alias": "",
            "Remark": ""
        }
    ]
    
    # 准备请求数据
    test_data = {
        "client_id": f"192_168_1_200_{new_username}",
        "contacts": test_contacts
    }
    
    print(f"📊 联系人数量: {len(test_contacts)}")
    
    # 检查文件夹是否已存在（应该不存在）
    user_folder = Path("data") / new_username
    print(f"📁 检查用户文件夹: {user_folder}")
    
    if user_folder.exists():
        print(f"⚠️  用户文件夹已存在（这不应该发生）")
    else:
        print(f"✅ 用户文件夹不存在（符合预期）")
    
    # 发送请求
    server_url = "http://127.0.0.1:8080/upload_contacts"
    
    try:
        print(f"\n📤 发送请求到: {server_url}")
        response = requests.post(server_url, json=test_data, timeout=15)
        response.raise_for_status()
        
        result = response.json()
        print(f"✅ 服务器响应: {result}")
        
        # 等待一下，确保文件系统操作完成
        time.sleep(1)
        
        # 检查是否自动创建了文件夹
        print(f"\n🔍 检查自动创建结果:")
        
        if user_folder.exists():
            print(f"✅ 用户文件夹已自动创建: {user_folder}")
            
            # 检查文件夹内容
            files = list(user_folder.glob("*"))
            print(f"📄 文件夹内容 ({len(files)}个文件):")
            
            for file in files:
                if file.name.endswith('.json'):
                    if file.name.startswith('new_contacts'):
                        print(f"   📝 {file.name} (新增联系人记录)")
                    else:
                        print(f"   📄 {file.name} (联系人文件)")
                        
                        # 验证文件内容
                        try:
                            with open(file, 'r', encoding='utf-8') as f:
                                contacts = json.load(f)
                            print(f"      包含 {len(contacts)} 个联系人")
                        except Exception as e:
                            print(f"      ❌ 读取失败: {str(e)}")
            
            # 检查new_contacts_log.json
            log_file = user_folder / "new_contacts_log.json"
            if log_file.exists():
                print(f"✅ 新增联系人记录文件已创建")
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)
                    print(f"   用户: {log_data.get('user', '未知')}")
                    print(f"   创建时间: {log_data.get('created_time', '未知')}")
                    print(f"   记录数: {len(log_data.get('records', []))}")
                except Exception as e:
                    print(f"   ❌ 读取记录文件失败: {str(e)}")
            else:
                print(f"❌ 新增联系人记录文件未创建")
                
        else:
            print(f"❌ 用户文件夹未自动创建")
            
        return new_username, user_folder
        
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")
        return None, None

def test_multiple_users():
    """测试多个用户的自动创建"""
    print(f"\n" + "="*60)
    print("测试多个用户自动创建")
    print("="*60)
    
    users_to_test = [
        ("张三", "192_168_1_100"),
        ("李四", "10_0_0_25"),
        ("王五", "172_16_1_50")
    ]
    
    created_users = []
    
    for username, ip_prefix in users_to_test:
        print(f"\n🧪 测试用户: {username}")
        
        test_data = {
            "client_id": f"{ip_prefix}_{username}",
            "contacts": [
                {
                    "UserName": f"{username}_contact_001",
                    "NickName": f"{username}的朋友1",
                    "Alias": "",
                    "Remark": f"这是{username}的联系人"
                }
            ]
        }
        
        try:
            response = requests.post("http://127.0.0.1:8080/upload_contacts", json=test_data, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ {username} - 响应: {result}")
            
            # 检查文件夹
            user_folder = Path("data") / username
            if user_folder.exists():
                print(f"✅ {username} - 文件夹已创建")
                created_users.append((username, user_folder))
            else:
                print(f"❌ {username} - 文件夹未创建")
                
        except Exception as e:
            print(f"❌ {username} - 请求失败: {str(e)}")
        
        time.sleep(0.5)  # 避免请求过快
    
    return created_users

def cleanup_test_users(test_users):
    """清理测试用户"""
    print(f"\n🧹 清理测试用户...")
    
    if not test_users:
        print("没有需要清理的用户")
        return
    
    import shutil
    
    cleaned_count = 0
    for username, user_folder in test_users:
        try:
            if user_folder.exists():
                shutil.rmtree(user_folder)
                print(f"✅ 已删除: {username} ({user_folder})")
                cleaned_count += 1
            else:
                print(f"⚠️  文件夹不存在: {username}")
        except Exception as e:
            print(f"❌ 删除失败: {username} - {str(e)}")
    
    print(f"清理完成，删除了 {cleaned_count} 个用户文件夹")

def show_current_structure():
    """显示当前的data目录结构"""
    print(f"\n📁 当前data目录结构:")
    
    data_dir = Path("data")
    if not data_dir.exists():
        print("data目录不存在")
        return
    
    folders = [d for d in data_dir.iterdir() if d.is_dir()]
    files = [f for f in data_dir.iterdir() if f.is_file()]
    
    print(f"   用户文件夹 ({len(folders)}个):")
    for folder in sorted(folders):
        print(f"   📂 {folder.name}/")
    
    if files:
        print(f"   根目录文件 ({len(files)}个):")
        for file in sorted(files):
            print(f"   📄 {file.name}")

def main():
    print("此测试将验证新用户自动文件夹创建功能")
    print("确保Flask服务器正在运行 (python app.py)")
    print()
    
    # 显示当前结构
    show_current_structure()
    
    choice = input(f"\n选择测试类型:\n1. 单个新用户测试\n2. 多个用户测试\n3. 显示当前结构\n请选择 (1-3): ").strip()
    
    test_users = []
    
    if choice == "1":
        print(f"\n开始单个用户测试...")
        username, folder = test_new_user_auto_folder()
        if username and folder:
            test_users.append((username, folder))
            
    elif choice == "2":
        print(f"\n开始多用户测试...")
        test_users = test_multiple_users()
        
    elif choice == "3":
        show_current_structure()
        return
    else:
        print("无效选择")
        return
    
    # 显示最终结构
    show_current_structure()
    
    # 询问是否清理
    if test_users:
        cleanup = input(f"\n是否清理测试用户? (y/N): ").lower().strip()
        if cleanup == 'y':
            cleanup_test_users(test_users)
            show_current_structure()

if __name__ == "__main__":
    main()
