{"user": "小罗", "contact_id": "48598", "contact_info": {"UserName": "xiaoniuniumy", "Alias": "", "Remark": "48598 钟铭宇（猎聘）", "NickName": "小牛牛"}, "fetch_time": "2025-07-09T13:22:57.806343", "message_count": 5, "chatlog_data": [{"seq": 1744938390000, "time": "2025-04-18T09:06:30+08:00", "talker": "xiaoniuniumy", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "下周一北京时间上午十点这个时间您方便吗？"}, {"seq": 1744939932000, "time": "2025-04-18T09:32:12+08:00", "talker": "xiaoniuniumy", "talkerName": "", "isChatRoom": false, "sender": "xiaoniuniumy", "senderName": "48598 钟铭宇（猎聘）", "isSelf": false, "type": 1, "subType": 0, "content": "好"}, {"seq": 1745200821000, "time": "2025-04-21T10:00:21+08:00", "talker": "xiaoniuniumy", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "钟博 您好 现在方便沟通吗？"}, {"seq": 1745200826000, "time": "2025-04-21T10:00:26+08:00", "talker": "xiaoniuniumy", "talkerName": "", "isChatRoom": false, "sender": "xiaoniuniumy", "senderName": "48598 钟铭宇（猎聘）", "isSelf": false, "type": 1, "subType": 0, "content": "好"}, {"seq": 1745202158000, "time": "2025-04-21T10:22:38+08:00", "talker": "xiaoniuniumy", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": ""}]}