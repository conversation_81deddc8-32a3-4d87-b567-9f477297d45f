# 🌐 IP地址监控系统使用指南

本文档介绍如何使用Flask应用中的IP地址监控功能，该功能可以接收和记录来自`SendIp.py`客户端的IP地址变化报告。

## 🚀 功能概述

- **IP地址报告接收**: 接收来自客户端的IP地址变化报告
- **历史记录存储**: 自动保存IP变化历史到JSON文件
- **Web监控界面**: 提供美观的Web界面查看IP变化记录
- **API查询接口**: 支持程序化查询IP变化历史
- **数据导出功能**: 支持将记录导出为CSV格式
- **实时更新**: 监控页面自动刷新显示最新数据

## 📋 系统架构

```
SendIp.py (客户端) → Flask App (服务端) → JSON文件存储
                                    ↓
                              Web监控界面
```

## 🔧 接口说明

### 1. IP地址报告接口

**接口地址**: `POST /report_ip`

**请求格式**:
```json
{
  "username": "用户名",
  "ip": "IP地址"
}
```

**响应格式**:
```json
{
  "status": "success",
  "message": "IP地址变化报告接收成功",
  "data": {
    "username": "用户名",
    "ip_address": "IP地址",
    "timestamp": "2025-07-15 09:48:56"
  }
}
```

### 2. IP历史查询接口

**接口地址**: `GET /api/ip_history`

**查询参数**:
- `username` (可选): 按用户名过滤
- `limit` (可选): 返回记录数量限制，默认50，最大200

**响应格式**:
```json
{
  "status": "success",
  "data": {
    "total_count": 12,
    "username_filter": "test_user",
    "limit": 50,
    "records": [
      {
        "username": "test_user",
        "ip_address": "*************",
        "client_ip": "127.0.0.1",
        "user_agent": "python-requests/2.28.1",
        "timestamp": "2025-07-15 09:48:56",
        "report_time": "2025-07-15T09:48:56.123456"
      }
    ]
  }
}
```

## 🌐 Web监控界面

### 访问地址
```
http://localhost:8080/ip_monitor
```

### 功能特性
- **实时数据**: 每30秒自动刷新
- **用户过滤**: 可按用户名过滤记录
- **数量控制**: 可选择显示20/50/100/200条记录
- **统计信息**: 显示总记录数、用户数量等统计
- **数据导出**: 支持导出为CSV格式
- **响应式设计**: 适配不同屏幕尺寸

### 界面说明
1. **统计卡片**: 显示总记录数、用户数量、最新用户、时间范围
2. **控制面板**: 用户名过滤、显示数量选择、刷新按钮
3. **记录列表**: 详细的IP变化记录，包含时间、IP地址、客户端信息

## 📱 SendIp.py客户端配置

### 修改服务器地址
将`SendIp.py`中的`DEFAULT_SERVER_URL`修改为：
```python
DEFAULT_SERVER_URL = "http://your-server-ip:8080/report_ip"
```

### 示例配置
```python
# 本地测试
DEFAULT_SERVER_URL = "http://127.0.0.1:8080/report_ip"

# 远程服务器
DEFAULT_SERVER_URL = "http://*************:8080/report_ip"

# 域名访问
DEFAULT_SERVER_URL = "http://yourserver.com:8080/report_ip"
```

## 💾 数据存储

### 存储文件
- **文件名**: `ip_changes.json`
- **位置**: Flask应用根目录
- **格式**: JSON数组
- **记录限制**: 最多保存100条记录（自动清理旧记录）

### 记录结构
```json
{
  "username": "用户名",
  "ip_address": "新IP地址",
  "client_ip": "客户端IP",
  "user_agent": "用户代理字符串",
  "timestamp": "2025-07-15 09:48:56",
  "report_time": "2025-07-15T09:48:56.123456"
}
```

## 🔍 使用示例

### Python客户端示例
```python
import requests

# 报告IP变化
data = {
    "username": "my_computer",
    "ip": "*************"
}

response = requests.post(
    "http://127.0.0.1:8080/report_ip",
    json=data
)

if response.status_code == 200:
    print("IP报告成功")
else:
    print("IP报告失败")
```

### 查询历史记录
```python
import requests

# 查询所有记录
response = requests.get("http://127.0.0.1:8080/api/ip_history?limit=10")
data = response.json()

for record in data['data']['records']:
    print(f"{record['username']}: {record['ip_address']} ({record['timestamp']})")
```

## 🚀 部署说明

### 1. 启动Flask应用
```bash
python app.py
```

### 2. 访问监控页面
打开浏览器访问: `http://localhost:8080/ip_monitor`

### 3. 配置客户端
修改`SendIp.py`中的服务器地址并运行

### 4. 验证功能
运行测试脚本验证功能：
```bash
python test_ip_report.py
```

## 🔧 故障排除

### 常见问题

1. **连接失败**
   - 检查Flask应用是否正在运行
   - 确认端口8080未被占用
   - 检查防火墙设置

2. **数据不显示**
   - 确认客户端配置的服务器地址正确
   - 检查JSON文件是否存在且格式正确
   - 查看服务器日志获取错误信息

3. **权限问题**
   - 确保Flask应用有写入文件的权限
   - 检查`ip_changes.json`文件权限

### 日志查看
Flask应用会在控制台输出详细的日志信息：
```
📡 收到IP地址变化报告
👤 用户名: test_user
🌐 新IP地址: *************
📍 客户端IP: 127.0.0.1
🕒 时间: 2025-07-15 09:48:56
✅ IP地址变化记录已保存
```

## 📊 监控建议

1. **定期备份**: 定期备份`ip_changes.json`文件
2. **日志轮转**: 考虑实现日志轮转避免文件过大
3. **告警机制**: 可以扩展添加IP变化告警功能
4. **数据库存储**: 对于大量数据可考虑使用数据库存储
5. **安全考虑**: 生产环境建议添加认证和HTTPS

## 🎯 扩展功能

可以考虑添加的功能：
- IP地址地理位置查询
- 邮件/短信告警通知
- 数据库持久化存储
- 用户认证和权限管理
- API访问频率限制
- 数据可视化图表

## 📞 技术支持

如有问题，请检查：
1. Flask应用日志
2. 客户端网络连接
3. 服务器防火墙设置
4. JSON文件格式和权限
