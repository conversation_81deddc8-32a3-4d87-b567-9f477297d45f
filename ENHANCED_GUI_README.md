# 增强版微信联系人获取工具

这是一个功能完整的微信联系人获取和监控工具，支持首次确认获取、定时自动监控、新增人员记录和IP地址变化处理。

## 🎯 核心功能

### 1. 首次确认获取
- **首次点击"确认"**: 立即获取一次所有联系人信息
- **自动启动定时任务**: 设置每天09:00和13:00自动获取
- **用户文件夹创建**: 按用户名创建专属文件夹

### 2. 定时自动监控
- **每天09:00**: 自动获取联系人信息
- **每天13:00**: 自动获取联系人信息
- **后台运行**: 定时任务在后台持续运行
- **文件替换**: 自动替换之前的文件，保持最新状态

### 3. 新增人员记录
- **智能检测**: 对比历史数据，识别新增联系人
- **时间记录**: 记录每次新增的具体时间
- **详细日志**: 保存新增联系人的完整信息
- **CSV摘要**: 生成便于查看的CSV格式摘要

### 4. IP地址变化处理
- **动态适应**: 用户名相同但IP变化时自动处理
- **历史保留**: 保留不同IP下的所有历史文件
- **变化记录**: 详细记录IP地址变化历史
- **数据连续性**: 基于历史数据准确检测新增

## 📁 文件结构

```
data/
└── 用户名/
    ├── IP1_用户名.json              # 第一个IP的联系人文件
    ├── IP2_用户名.json              # 第二个IP的联系人文件
    ├── IP3_用户名.json              # 第三个IP的联系人文件
    ├── new_contacts_log.json        # 新增联系人详细记录
    ├── new_contacts_summary.csv     # 新增联系人CSV摘要
    └── ip_change_log.json           # IP地址变化记录
```

### 示例文件结构
```
data/
└── zhang_san/
    ├── 192_168_1_100_zhang_san.json
    ├── 192_168_1_200_zhang_san.json
    ├── 10_0_0_50_zhang_san.json
    ├── new_contacts_log.json
    ├── new_contacts_summary.csv
    └── ip_change_log.json
```

## 🚀 使用流程

### 1. 启动程序
```bash
python wechat_gui.py
```

### 2. 配置信息
- **自动检测IP**: 程序自动检测客户端IP地址
- **输入用户名**: 在用户名框中输入您的用户名
- **提交用户名**: 点击"提交用户名"按钮

### 3. 首次确认
- **点击"确认"**: 立即获取所有联系人信息
- **创建文件夹**: 自动创建用户专属文件夹
- **启动定时任务**: 自动设置每天2次的定时获取

### 4. 自动运行
- **定时获取**: 每天09:00和13:00自动运行
- **新增检测**: 自动对比并记录新增联系人
- **文件更新**: 替换旧文件，保持数据最新

## 📊 记录文件详解

### 1. 新增联系人记录 (new_contacts_log.json)
```json
{
  "user": "zhang_san",
  "created_time": "2025-07-08T10:00:00",
  "description": "新增联系人记录文件",
  "records": [
    {
      "timestamp": "2025-07-08T13:00:00",
      "detection_time": "2025-07-08 13:00:00",
      "count": 2,
      "contacts": [
        {
          "username": "new_user_001",
          "nickname": "新朋友1",
          "alias": "",
          "remark": "新增的联系人",
          "full_data": {...}
        }
      ]
    }
  ]
}
```

### 2. IP变化记录 (ip_change_log.json)
```json
{
  "user": "zhang_san",
  "created_time": "2025-07-08T10:00:00",
  "description": "IP地址变化记录",
  "changes": [
    {
      "timestamp": "2025-07-08T14:00:00",
      "change_time": "2025-07-08 14:00:00",
      "old_ip": "*************",
      "new_ip": "*************",
      "old_filename_format": "192_168_1_100_zhang_san.json",
      "new_filename_format": "192_168_1_200_zhang_san.json"
    }
  ]
}
```

### 3. CSV摘要 (new_contacts_summary.csv)
```csv
检测时间,用户名,昵称,别名,备注,记录批次
2025-07-08 13:00:00,new_user_001,新朋友1,,新增的联系人,第1批
2025-07-08 13:00:00,new_user_002,新朋友2,friend2,,第1批
```

## 🔍 功能特点

### 智能新增检测
- **首次排除**: 首次获取的联系人不记录为"新增"
- **历史对比**: 基于用户的历史联系人数据进行对比
- **跨IP检测**: 即使IP变化也能准确检测新增
- **时间戳记录**: 精确记录每次新增的时间

### IP变化处理
- **自动检测**: 自动检测IP地址变化
- **文件管理**: 为每个IP创建独立的联系人文件
- **历史保留**: 保留所有IP下的历史数据
- **连续性保证**: 确保新增检测的连续性

### 定时任务管理
- **后台运行**: 定时任务在后台持续运行
- **自动启动**: 首次确认后自动启动
- **状态监控**: 可查看定时任务运行状态
- **手动触发**: 支持手动立即获取

## 🎛️ 界面功能

### 主要按钮
- **确认**: 首次点击立即获取+启动定时任务，后续为手动获取
- **提交用户名**: 设置用户名并创建文件夹
- **查看状态**: 显示详细的运行状态信息
- **自动检测IP**: 自动检测当前客户端IP地址

### 状态信息
- 当前用户名和IP地址
- 用户文件夹路径
- 定时任务运行状态
- 新增联系人统计
- IP变化记录统计

## 📈 实际应用场景

### 1. 个人用户
- 监控微信联系人变化
- 记录新朋友添加时间
- 备份联系人信息

### 2. 企业用户
- 员工微信联系人监控
- 客户关系管理
- 业务联系人分析

### 3. 移动办公
- 支持IP地址动态变化
- 保持数据连续性
- 自动化数据收集

## 🔧 技术特点

### 数据安全
- 本地文件存储
- 完整数据备份
- 历史记录保留

### 性能优化
- 多线程处理
- 增量检测
- 智能缓存

### 用户体验
- 图形界面操作
- 实时日志显示
- 详细状态反馈

## 📋 注意事项

1. **首次运行**: 首次获取的联系人不会记录为"新增"
2. **定时任务**: 程序需要保持运行以执行定时任务
3. **IP变化**: IP变化时会自动创建新文件，不会覆盖历史数据
4. **文件管理**: 建议定期备份用户文件夹
5. **网络要求**: 需要稳定的网络连接访问MCP服务

## 🎯 总结

这个增强版工具完美实现了您的所有需求：

✅ **首次确认获取**: 点击确认立即获取所有联系人
✅ **定时自动监控**: 每天09:00和13:00自动获取
✅ **新增人员记录**: 记录新增联系人并标注时间
✅ **文件替换更新**: 自动替换旧文件保持最新
✅ **用户文件夹**: 按用户名创建专属文件夹
✅ **IP变化处理**: 支持IP变化时的正确存储

现在您有了一个完全自动化的微信联系人监控系统！
