{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "6830", "contact_info": {"UserName": "F100000099553904", "Alias": "<PERSON><PERSON><PERSON><PERSON>", "Remark": "6830 唐肇蔚", "NickName": "JGTang"}, "fetch_time": "2025-07-09T13:19:31.580387", "message_count": 16, "chatlog_data": [{"seq": 1707382649000, "time": "2024-02-08T16:57:29+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "不辞加一岁，唯喜到三春；[福][福][福]\n正值腊月，岁序更新，华章再续。\n但入新年，在这辞旧迎新之际，家国同乐之时，我谨代表我司全体员工祝您佳节共欢同乐，也向您致以最诚挚的感激，惟愿我司与您的合作在新的一年辰龙而上，逐光而行[烟花][烟花][烟花]"}, {"seq": 1707382649001, "time": "2024-02-08T16:57:29+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"a883e19d5882b41240c8f23755676068\" encryver=\"1\" cdnthumbaeskey=\"a883e19d5882b41240c8f23755676068\" cdnthumburl=\"3057020100044b30490201000204ff23008602032df5a10204a13e2a3a020465c4926a042466663066313761322d363261652d343461362d383532342d3130663136303363623966370204051418020201000405004c55cd00\" cdnthumblength=\"11624\" cdnthumbheight=\"149\" cdnthumbwidth=\"73\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032df5a10204a13e2a3a020465c4926a042466663066313761322d363261652d343461362d383532342d3130663136303363623966370204051418020201000405004c55cd00\" length=\"399798\" md5=\"eff79e129ab05ec945d932c3248cb9ba\" hevc_mid_size=\"0\" originsourcemd5=\"eff79e129ab05ec945d932c3248cb9ba\"/></msg>", "contents": {"md5": "eff79e129ab05ec945d932c3248cb9ba"}}, {"seq": 1707387823000, "time": "2024-02-08T18:23:43+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "F100000099553904", "senderName": "6830 唐肇蔚", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"ed978a71b60efbc13461bad50e2b5996\" encryver=\"1\" cdnthumbaeskey=\"ed978a71b60efbc13461bad50e2b5996\" cdnthumburl=\"3057020100044b3049020100020477f2154e02030f5ef90204482daf2b020465c4abaf042430396239323331342d343436392d343730642d613262622d3436393535613666636339300204011d0a020201000405004c53db00\" cdnthumblength=\"6721\" cdnthumbheight=\"120\" cdnthumbwidth=\"120\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020477f2154e02030f5ef90204482daf2b020465c4abaf042430396239323331342d343436392d343730642d613262622d3436393535613666636339300204011d0a020201000405004c53db00\" length=\"88979\" md5=\"3fb0a6c15e619efdef642e2261af05d9\" hevc_mid_size=\"88979\" originsourcemd5=\"343d71f464a805f81c7bac0340a3f876\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl></ImgSourceUrl>\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n", "contents": {"md5": "3fb0a6c15e619efdef642e2261af05d9"}}, {"seq": 1747899482000, "time": "2025-05-22T15:38:02+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士您好呀！您今年考虑国内的工作机会吗"}, {"seq": 1747899616000, "time": "2025-05-22T15:40:16+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "F100000099553904", "senderName": "6830 唐肇蔚", "isSelf": false, "type": 1, "subType": 0, "content": "有適合我的機會嗎? 有的話會考慮"}, {"seq": 1747899709000, "time": "2025-05-22T15:41:49+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "现在主要是浙江省的人才项目，是先入职后申请的形式"}, {"seq": 1747899739000, "time": "2025-05-22T15:42:19+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您这边看机会的话对平台有什么倾向性吗，初创型的单位会考虑吗"}, {"seq": 1747899833000, "time": "2025-05-22T15:43:53+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "F100000099553904", "senderName": "6830 唐肇蔚", "isSelf": false, "type": 1, "subType": 0, "content": "大企業可能比較好"}, {"seq": 1747899849000, "time": "2025-05-22T15:44:09+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "F100000099553904", "senderName": "6830 唐肇蔚", "isSelf": false, "type": 1, "subType": 0, "content": "想找穩定一點的企業單位"}, {"seq": 1747899866000, "time": "2025-05-22T15:44:26+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好滴好滴，对啦，您这边方便同步一份最新简历吗，我们上次联系时间比较久啦"}, {"seq": 1747899974000, "time": "2025-05-22T15:46:14+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "F100000099553904", "senderName": "6830 唐肇蔚", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n\t\t<title>唐肇蔚履歷表_Jogging <PERSON>'s Resume_2025.05.pdf</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>6</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<md5>69c2f57854b91a8c1bc13a2e50be2c03</md5>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>324570</totallen>\n\t\t\t<attachid>@cdn_3057020100044b3049020100020477f2154e02030f5c3602041b9dcdcb0204682ed647042434393439353539322d656135302d343838622d383863352d6132643965396661323761650204011800050201000405004c505500_7650849479d9584410ab99b29b4d6d81_1</attachid>\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<fileuploadtoken>v1_hJURSA3QOrLMKVWFpLEBCuNQZXH86tvfXw/8yuTouG5N9xBgbRDG7JwPMz6Rmw1jtOC8AQMR8PJ+yRWV+IarmkrMeHzTU4uupsun0b/2bSedONKPfGLAUof5dc6t5XXdrKOHtq3eTupz/TKSYDAPV2EriKDd0gg3l03Xnir/WN8Xa7fqpHfjAW5xbE8gW9614PUln4/PIpU5n6mSF4I7iP6JulcxjFPQOZuwEB6MQVQxIRg4XQZ9KQ6mUcl1+03CQAs0t3ct2ZnrZ4IYJ9rS2iDBXz4g</fileuploadtoken>\n\t\t\t<overwrite_newmsgid>3655871021861073729</overwrite_newmsgid>\n\t\t\t<filekey>b811dc474c33fec739914a6043f5b42c</filekey>\n\t\t\t<cdnattachurl>3057020100044b3049020100020477f2154e02030f5c3602041b9dcdcb0204682ed647042434393439353539322d656135302d343838622d383863352d6132643965396661323761650204011800050201000405004c505500</cdnattachurl>\n\t\t\t<aeskey>7650849479d9584410ab99b29b4d6d81</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>F100000099553904</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>7</version>\n\t\t<appname>微信电脑版</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n\u0000", "contents": {"md5": "69c2f57854b91a8c1bc13a2e50be2c03", "title": "唐肇蔚履歷表_Jogging <PERSON>'s Resume_2025.05.pdf"}}, {"seq": 1747900118000, "time": "2025-05-22T15:48:38+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到，您是在台湾这边哈，方便问下您的年龄吗~"}, {"seq": 1747900170000, "time": "2025-05-22T15:49:30+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "F100000099553904", "senderName": "6830 唐肇蔚", "isSelf": false, "type": 1, "subType": 0, "content": "在台灣，1983,7月滿 42歲"}, {"seq": 1747900397000, "time": "2025-05-22T15:53:17+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好滴好滴"}, {"seq": 1747900415000, "time": "2025-05-22T15:53:35+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那我这边先为您匹配下单位哈"}, {"seq": 1747900557000, "time": "2025-05-22T15:55:57+08:00", "talker": "F100000099553904", "talkerName": "", "isChatRoom": false, "sender": "F100000099553904", "senderName": "6830 唐肇蔚", "isSelf": false, "type": 1, "subType": 0, "content": "[OK]"}]}