# 定时联系人分析工具

这是一个增强版的微信联系人分析工具，支持定时任务，每天自动2次对比所有客户微信联系人的新增情况和重复情况。

## 🎯 新增功能

### 定时分析
- **每天2次自动分析**: 09:00 和 21:00
- **历史状态跟踪**: 自动保存每次分析的状态快照
- **新增联系人检测**: 对比历史状态，识别新增的联系人
- **重复联系人分析**: 继续支持原有的重复联系人检测功能

### 多种运行模式
1. **定时任务模式**: 后台持续运行，按计划执行分析
2. **手动分析模式**: 立即运行一次完整分析
3. **传统模式**: 原有的重复联系人分析功能
4. **Windows服务模式**: 作为系统服务在后台运行

## 📁 目录结构

```
├── find_duplicate_contacts.py          # 主程序文件（已增强）
├── contact_analyzer_service.py         # Windows服务版本
├── run_scheduled_analyzer.bat          # 启动脚本
├── manage_service.bat                  # 服务管理脚本
├── data/                               # 联系人数据目录
├── history/                            # 历史状态存储目录
│   └── contacts_state_YYYYMMDD_HHMMSS.json
├── reports/                            # 分析报告目录
│   ├── contact_analyzer.log            # 程序日志
│   ├── daily_analysis_YYYYMMDD_HHMMSS.json
│   └── new_contacts_YYYYMMDD_HHMMSS.csv
└── logs/                               # 服务日志目录（服务模式）
```

## 🚀 使用方法

### 方法1: 使用启动脚本（推荐）
```bash
# 双击运行，选择运行模式
run_scheduled_analyzer.bat
```

### 方法2: 命令行运行
```bash
# 定时任务模式
python find_duplicate_contacts.py --schedule

# 手动分析模式
python find_duplicate_contacts.py --manual

# 传统分析模式
python find_duplicate_contacts.py
```

### 方法3: Windows服务模式
```bash
# 使用服务管理脚本
manage_service.bat

# 或手动管理
python contact_analyzer_service.py install    # 安装服务
python contact_analyzer_service.py start      # 启动服务
python contact_analyzer_service.py stop       # 停止服务
python contact_analyzer_service.py remove     # 卸载服务
```

## 📊 分析报告

### 1. 日常分析报告 (JSON格式)
文件名: `daily_analysis_YYYYMMDD_HHMMSS.json`

包含内容:
```json
{
  "analysis_time": "2025-07-07T16:04:51.610558",
  "comparison": {
    "current_timestamp": "20250707_160451",
    "last_timestamp": "20250707_160420",
    "new_contacts": {},
    "new_files": [],
    "summary": {
      "total_new_contacts": 0,
      "files_with_new_contacts": 0,
      "new_files_count": 0
    }
  },
  "duplicates": {
    "duplicate_contacts": [...]
  }
}
```

### 2. 新增联系人报告 (CSV格式)
文件名: `new_contacts_YYYYMMDD_HHMMSS.csv`

包含列:
- 文件名
- 用户名
- 昵称
- 别名
- 备注

### 3. 历史状态快照
文件名: `contacts_state_YYYYMMDD_HHMMSS.json`

保存每次分析时的完整联系人状态，用于下次对比。

## 🔍 分析功能详解

### 新增联系人检测
- 对比当前状态与上次保存的历史状态
- 识别每个文件中新增的联系人
- 检测新增的文件
- 统计新增联系人总数

### 重复联系人分析
- 继续支持原有功能
- 识别在多个文件中出现的联系人
- 按出现频次排序
- 生成详细的重复联系人报告

### 历史状态管理
- 自动保存每次分析的状态快照
- 支持状态对比和变化追踪
- 历史文件自动命名和管理

## 📈 示例输出

```
============================================================
定时分析摘要
============================================================
分析时间: 2025-07-07 16:04:51
分析文件数: 3

📈 新增联系人情况:
  总新增联系人: 5
  有新增的文件数: 2
  新增文件数: 1
  新增文件: 192_168_1_100_新用户.json

🔄 重复联系人情况:
  重复联系人总数: 22
  重复次数最多的联系人:
    1. 文件传输助手 - 出现在3个文件中
    2. 漂流瓶 - 出现在3个文件中
    3. 微信支付 - 出现在3个文件中
============================================================
```

## ⚙️ 配置选项

### 修改定时时间
在 `find_duplicate_contacts.py` 中修改:
```python
schedule.every().day.at("09:00").do(self.run_scheduled_analysis)
schedule.every().day.at("21:00").do(self.run_scheduled_analysis)
```

### 修改目录路径
```python
analyzer = ScheduledContactAnalyzer(
    data_dir="data",           # 数据目录
    history_dir="history",     # 历史状态目录
    reports_dir="reports"      # 报告输出目录
)
```

## 🛠️ 依赖安装

```bash
pip install -r requirements.txt
```

新增依赖:
- `schedule`: 定时任务调度
- `pathlib`: 路径处理
- `logging`: 日志记录

可选依赖（Windows服务模式）:
- `pywin32`: Windows服务支持

## 🔧 故障排除

### 常见问题

1. **定时任务不执行**
   - 检查系统时间是否正确
   - 查看日志文件中的错误信息
   - 确保程序持续运行

2. **历史状态文件损坏**
   - 删除 `history` 目录中的损坏文件
   - 程序会自动创建新的状态快照

3. **服务安装失败**
   - 确保以管理员权限运行
   - 安装 `pywin32`: `pip install pywin32`

4. **权限问题**
   - 确保程序有读写相关目录的权限
   - Windows服务需要适当的服务账户权限

### 日志查看

- **程序日志**: `reports/contact_analyzer.log`
- **服务日志**: `logs/service.log`
- **Windows事件日志**: 查看系统事件查看器

## 🚨 注意事项

1. **数据备份**: 建议定期备份 `data` 和 `history` 目录
2. **磁盘空间**: 历史状态文件会占用一定空间，建议定期清理旧文件
3. **系统资源**: 定时任务会消耗一定的系统资源
4. **网络环境**: 如果数据源需要网络访问，确保网络连接稳定

## 📋 更新日志

### v2.0 (2025-07-07)
- ✅ 新增定时任务功能
- ✅ 新增历史状态跟踪
- ✅ 新增新增联系人检测
- ✅ 新增Windows服务支持
- ✅ 新增多种运行模式
- ✅ 新增详细的日志记录
- ✅ 新增批处理管理脚本

### v1.0
- ✅ 基础重复联系人分析功能

这个增强版工具现在可以全自动地监控您的微信联系人变化，每天定时分析并生成详细报告！
