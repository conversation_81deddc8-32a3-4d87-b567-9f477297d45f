# 聊天记录读取器

这是一个独立的Python工具，用于从联系人JSON文件中读取各个用户的聊天记录。

## 🎯 功能特性

### 核心功能
- **自动解析文件名**: 从文件名中提取IP地址和用户名
- **批量获取聊天记录**: 为每个联系人调用聊天记录API
- **智能文件命名**: 生成有意义的聊天记录文件名
- **空记录过滤**: 自动跳过没有聊天记录的联系人
- **多格式支持**: 支持JSON和文本格式的聊天记录

### API调用逻辑
根据您的需求实现：
```
http://IP:5030/api/v1/chatlog?time=2023-01-01~2025-07-07&talker=UserName
```

- **IP地址**: 从文件名中解析（如 `192_168_1_191_小马.json` → `*************`）
- **talker参数**: 使用联系人的`UserName`字段值
- **时间范围**: 默认使用 `2023-01-01~2025-07-07`

## 📁 文件结构

```
├── chatlog_reader.py           # 主程序文件
├── run_chatlog_reader.bat      # Windows启动脚本
├── data/                       # 联系人JSON文件目录
│   ├── 192_168_1_191_小马.json
│   ├── 192_168_1_191_用户_安妮.json
│   └── ...
├── chatlogs/                   # 聊天记录输出目录
│   ├── 192_168_1_191_用户_安妮**************************************
│   ├── 192_168_1_191_用户_安妮_文件传输助手_filehelper.json
│   └── ...
└── chatlog_reader.log          # 程序运行日志
```

## 🚀 使用方法

### 方法1: 使用批处理文件（推荐）
```bash
# 双击运行，选择操作模式
run_chatlog_reader.bat
```

### 方法2: 命令行运行
```bash
# 处理所有文件
python chatlog_reader.py --all

# 处理指定文件
python chatlog_reader.py --file "192_168_1_191_小马.json"

# 生成摘要报告
python chatlog_reader.py --report

# 默认处理所有文件
python chatlog_reader.py
```

## 📊 输出文件

### 1. 聊天记录文件
文件名格式: `IP_用户名_昵称_UserName.json`

示例:
- `192_168_1_191_用户_安妮**************************************`
- `192_168_1_191_用户_安妮_文件传输助手_filehelper.json`

### 2. 摘要报告
文件名: `chatlog_summary_YYYYMMDD_HHMMSS.csv`

包含列:
- 文件名
- IP地址
- 用户名
- 昵称
- UserName
- 文件大小(KB)
- 创建时间

## 🔍 工作流程

1. **扫描数据目录**: 查找所有`.json`文件
2. **解析文件名**: 提取IP地址和用户名信息
3. **加载联系人**: 读取JSON文件中的联系人列表
4. **逐个处理**: 为每个联系人调用聊天记录API
5. **保存记录**: 将有内容的聊天记录保存到文件
6. **生成报告**: 创建处理摘要报告

## 📈 示例运行结果

```
处理文件: 192_168_1_191_用户_安妮.json (IP: *************, 用户: 用户_安妮)
从 data\192_168_1_191_用户_安妮.json 加载了 1168 个联系人

处理联系人 8/1168: Richard (25984983151752900@openim)
请求聊天记录: http://*************:5030/api/v1/chatlog?time=2023-01-01~2025-07-07&talker=25984983151752900%40openim
用户 25984983151752900@openim 返回非JSON格式数据，长度: 9111
聊天记录已保存: chatlogs\192_168_1_191_用户_安妮**************************************

处理联系人 42/1168: 文件传输助手 (filehelper)
请求聊天记录: http://*************:5030/api/v1/chatlog?time=2023-01-01~2025-07-07&talker=filehelper
用户 filehelper 返回非JSON格式数据，长度: 1856
聊天记录已保存: chatlogs\192_168_1_191_用户_安妮_文件传输助手_filehelper.json

文件 192_168_1_191_用户_安妮.json 处理完成: 总联系人 1168, 已处理 1168, 成功获取 2
```

## ⚙️ 配置选项

### 修改时间范围
在 `chatlog_reader.py` 中修改:
```python
self.default_time_range = "2023-01-01~2025-07-07"
```

### 修改目录路径
```python
reader = ChatLogReader(
    data_dir="data",           # 联系人数据目录
    output_dir="chatlogs"      # 聊天记录输出目录
)
```

### 修改请求延迟
```python
time.sleep(0.5)  # 每个请求间隔0.5秒
```

## 🛠️ 技术特点

### 文件名解析
- 支持格式: `IP_用户名.json`
- 自动转换: `192_168_1_191` → `*************`
- 安全处理: 清理特殊字符避免文件名冲突

### API请求
- **URL编码**: 自动处理特殊字符（如@符号）
- **超时控制**: 30秒请求超时
- **错误处理**: 完善的网络异常处理

### 数据处理
- **格式检测**: 自动识别JSON和文本格式
- **空值过滤**: 跳过空的聊天记录
- **安全保存**: 使用UTF-8编码保存文件

## 🔧 故障排除

### 常见问题

1. **无法连接到MCP服务**
   - 检查IP地址是否正确
   - 确认MCP服务运行在5030端口
   - 检查网络连接

2. **文件名解析失败**
   - 确保文件名格式为 `IP_用户名.json`
   - 检查文件名中的特殊字符

3. **聊天记录为空**
   - 这是正常情况，很多联系人可能没有聊天记录
   - 程序会自动跳过空记录

4. **权限问题**
   - 确保程序有读取data目录的权限
   - 确保程序有写入chatlogs目录的权限

### 日志查看
程序会生成详细的日志文件 `chatlog_reader.log`，包含：
- 处理进度信息
- API请求详情
- 错误信息
- 成功保存的文件列表

## 📋 注意事项

1. **处理时间**: 大量联系人可能需要较长时间处理
2. **网络稳定**: 确保网络连接稳定，避免请求失败
3. **存储空间**: 聊天记录文件可能占用较多磁盘空间
4. **API限制**: 添加了请求延迟避免过于频繁的API调用

## 🎯 实际应用

这个工具完美实现了您的需求：
- ✅ 从文件名中提取IP地址
- ✅ 使用UserName字段作为talker参数
- ✅ 调用正确的API格式
- ✅ 自动跳过空的聊天记录
- ✅ 生成有意义的输出文件名

现在您可以轻松地批量获取所有联系人的聊天记录！
