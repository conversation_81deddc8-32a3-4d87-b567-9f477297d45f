# 用户JSON文件更新测试工具

## 📋 功能说明

这个工具用于测试IP地址变化时，系统自动更新用户文件夹下JSON文件名的功能。

## 🚀 使用方法

### 方法1: 直接运行exe文件
双击 `UserJsonUpdateTest.exe` 文件即可运行

### 方法2: 使用批处理文件
双击 `run_test.bat` 文件运行（推荐）

## 📁 文件说明

- `UserJsonUpdateTest.exe` - 主程序文件
- `run_test.bat` - 批处理启动文件
- `README.md` - 本说明文件

## ⚙️ 运行要求

1. **Flask服务器**: 确保Flask应用正在运行在 http://127.0.0.1:8080
2. **数据目录**: 确保存在 `data/` 目录，包含用户文件夹
3. **网络连接**: 能够访问本地Flask服务器

## 🔧 测试流程

1. 程序会自动检查用户数据结构
2. 选择一个测试用户进行IP变化测试
3. 测试不同IP地址的变化场景：
   - 相同IP地址（无需更新）
   - 新的内网IP地址
   - 公网IP地址
4. 验证JSON文件名是否正确更新
5. 检查IP变化日志是否生成

## 📊 测试结果

程序会显示详细的测试结果，包括：
- ✅ 成功的测试项目
- ❌ 失败的测试项目
- 📝 文件变化记录
- 📊 测试统计信息

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查Flask应用是否正在运行
   - 确认端口8080未被占用

2. **数据目录不存在**
   - 确保程序运行目录下有 `data/` 文件夹
   - 检查用户文件夹结构是否正确

3. **权限问题**
   - 确保程序有读写文件的权限
   - 以管理员身份运行（如果需要）

### 错误代码

- 退出代码 0: 所有测试通过
- 退出代码 1: 部分测试失败
- 退出代码 2: 严重错误（无法运行）

## 📞 技术支持

如有问题，请检查：
1. Flask服务器日志
2. 用户文件夹权限
3. 网络连接状态
4. 数据目录结构

---
生成时间: 2025-07-15 10:01:51
版本: 1.0.0
