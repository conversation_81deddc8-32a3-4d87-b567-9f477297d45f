#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
打包脚本 - 将Python脚本打包成exe文件
使用PyInstaller进行打包
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装，版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['test_user_json_update.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['requests', 'json', 'os', 'time'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='UserJsonUpdateTest',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('test_user_json_update.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建PyInstaller规格文件: test_user_json_update.spec")

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")

    try:
        # 使用Python模块方式调用PyInstaller
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',  # 打包成单个exe文件
            '--console',  # 保留控制台窗口
            '--name=UserJsonUpdateTest',  # 指定exe文件名
            '--distpath=dist',  # 输出目录
            '--workpath=build',  # 工作目录
            '--specpath=.',  # spec文件目录
            '--clean',  # 清理缓存
            'test_user_json_update.py'
        ]

        print(f"📝 执行命令: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✅ exe文件构建成功!")
            print(f"📄 构建输出: {result.stdout[-500:]}")  # 显示最后500字符

            # 检查生成的文件
            exe_path = Path('dist/UserJsonUpdateTest.exe')
            if exe_path.exists():
                file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
                print(f"📁 生成的exe文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.2f} MB")
                return True
            else:
                print("❌ exe文件未找到")
                return False
        else:
            print("❌ exe文件构建失败")
            print(f"标准输出: {result.stdout}")
            print(f"错误输出: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ 构建过程中出现错误: {e}")
        return False

def create_batch_file():
    """创建批处理文件用于运行exe"""
    batch_content = '''@echo off
chcp 65001 >nul
echo 启动用户JSON文件更新测试工具
echo ================================
echo.

REM 检查exe文件是否存在
if not exist "UserJsonUpdateTest.exe" (
    echo 错误: UserJsonUpdateTest.exe 文件不存在
    echo 请确保exe文件在当前目录下
    pause
    exit /b 1
)

REM 运行exe文件
echo 正在启动测试程序...
UserJsonUpdateTest.exe

echo.
echo 程序执行完成
pause
'''

    try:
        with open('run_test.bat', 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print("✅ 已创建批处理文件: run_test.bat")
    except Exception as e:
        print(f"⚠️ 创建批处理文件失败: {e}")
        # 创建简化版本
        simple_batch = '''@echo off
echo Starting UserJsonUpdateTest...
UserJsonUpdateTest.exe
pause
'''
        with open('run_test.bat', 'w', encoding='ascii') as f:
            f.write(simple_batch)
        print("✅ 已创建简化批处理文件: run_test.bat")

def create_readme():
    """创建使用说明文件"""
    readme_content = '''# 用户JSON文件更新测试工具

## 📋 功能说明

这个工具用于测试IP地址变化时，系统自动更新用户文件夹下JSON文件名的功能。

## 🚀 使用方法

### 方法1: 直接运行exe文件
双击 `UserJsonUpdateTest.exe` 文件即可运行

### 方法2: 使用批处理文件
双击 `run_test.bat` 文件运行（推荐）

## 📁 文件说明

- `UserJsonUpdateTest.exe` - 主程序文件
- `run_test.bat` - 批处理启动文件
- `README.md` - 本说明文件

## ⚙️ 运行要求

1. **Flask服务器**: 确保Flask应用正在运行在 http://127.0.0.1:8080
2. **数据目录**: 确保存在 `data/` 目录，包含用户文件夹
3. **网络连接**: 能够访问本地Flask服务器

## 🔧 测试流程

1. 程序会自动检查用户数据结构
2. 选择一个测试用户进行IP变化测试
3. 测试不同IP地址的变化场景：
   - 相同IP地址（无需更新）
   - 新的内网IP地址
   - 公网IP地址
4. 验证JSON文件名是否正确更新
5. 检查IP变化日志是否生成

## 📊 测试结果

程序会显示详细的测试结果，包括：
- ✅ 成功的测试项目
- ❌ 失败的测试项目
- 📝 文件变化记录
- 📊 测试统计信息

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查Flask应用是否正在运行
   - 确认端口8080未被占用

2. **数据目录不存在**
   - 确保程序运行目录下有 `data/` 文件夹
   - 检查用户文件夹结构是否正确

3. **权限问题**
   - 确保程序有读写文件的权限
   - 以管理员身份运行（如果需要）

### 错误代码

- 退出代码 0: 所有测试通过
- 退出代码 1: 部分测试失败
- 退出代码 2: 严重错误（无法运行）

## 📞 技术支持

如有问题，请检查：
1. Flask服务器日志
2. 用户文件夹权限
3. 网络连接状态
4. 数据目录结构

---
生成时间: {datetime}
版本: 1.0.0
'''.replace('{datetime}', str(__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已创建说明文件: README.md")

def clean_build_files():
    """清理构建过程中的临时文件"""
    print("🧹 清理临时文件...")
    
    # 要清理的目录和文件
    cleanup_items = [
        'build',
        '__pycache__',
        'test_user_json_update.spec',
        '*.pyc'
    ]
    
    for item in cleanup_items:
        if item.endswith('*'):
            # 处理通配符
            import glob
            for file in glob.glob(item):
                try:
                    os.remove(file)
                    print(f"🗑️ 删除文件: {file}")
                except Exception as e:
                    print(f"⚠️ 删除文件失败 {file}: {e}")
        else:
            # 处理目录和文件
            if os.path.exists(item):
                try:
                    if os.path.isdir(item):
                        shutil.rmtree(item)
                        print(f"🗑️ 删除目录: {item}")
                    else:
                        os.remove(item)
                        print(f"🗑️ 删除文件: {item}")
                except Exception as e:
                    print(f"⚠️ 删除失败 {item}: {e}")

def main():
    """主函数"""
    print("🚀 Python脚本打包工具")
    print("=" * 50)
    
    # 检查源文件是否存在
    if not os.path.exists('test_user_json_update.py'):
        print("❌ 源文件 test_user_json_update.py 不存在")
        return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 创建输出目录
    os.makedirs('dist', exist_ok=True)
    
    # 构建exe文件
    if not build_exe():
        return False
    
    # 创建辅助文件
    create_batch_file()
    create_readme()
    
    # 清理临时文件
    clean_build_files()
    
    print("\n🎉 打包完成!")
    print("=" * 50)
    print("📁 生成的文件:")
    print("   - dist/UserJsonUpdateTest.exe (主程序)")
    print("   - run_test.bat (启动脚本)")
    print("   - README.md (使用说明)")
    
    print("\n💡 使用方法:")
    print("1. 确保Flask服务器正在运行")
    print("2. 双击 run_test.bat 或直接运行 UserJsonUpdateTest.exe")
    print("3. 查看测试结果")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 打包成功完成!")
        else:
            print("\n❌ 打包失败!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 打包过程中出现异常: {e}")
        sys.exit(1)
