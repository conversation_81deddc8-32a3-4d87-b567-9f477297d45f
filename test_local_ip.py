#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试本地IP获取功能
"""

import socket
from datetime import datetime

def log_message(message):
    """记录日志消息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        log_message("🌐 正在获取本机局域网IP地址...")
        
        # 方法1：通过连接外部地址获取本机IP（推荐方法）
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                # 连接到一个外部地址（不会实际发送数据）
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                if local_ip and local_ip != "127.0.0.1":
                    log_message(f"✅ 获取到局域网IP地址: {local_ip}")
                    return local_ip
        except Exception as e:
            log_message(f"⚠️ 方法1获取IP失败: {e}")
        
        # 方法2：通过hostname获取IP
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            if local_ip and local_ip != "127.0.0.1":
                log_message(f"✅ 通过hostname获取到IP地址: {local_ip}")
                return local_ip
        except Exception as e:
            log_message(f"⚠️ 方法2获取IP失败: {e}")
        
        log_message("❌ 无法获取局域网IP地址")
        return None
        
    except Exception as e:
        log_message(f"❌ 获取局域网IP地址失败: {e}")
        return None

def test_ip_detection():
    """测试IP检测功能"""
    print("🧪 测试本地IP获取功能")
    print("=" * 50)
    
    # 测试多次获取，确保稳定性
    for i in range(3):
        print(f"\n第 {i+1} 次测试:")
        ip = get_local_ip()
        if ip:
            print(f"✅ 成功获取IP: {ip}")
        else:
            print("❌ 获取IP失败")
    
    print("\n🔍 系统网络信息:")
    try:
        hostname = socket.gethostname()
        print(f"   主机名: {hostname}")
        
        # 获取所有IP地址
        try:
            all_ips = socket.gethostbyname_ex(hostname)[2]
            print(f"   所有IP地址: {all_ips}")
        except:
            print("   无法获取所有IP地址")
            
    except Exception as e:
        print(f"   获取系统信息失败: {e}")

if __name__ == "__main__":
    test_ip_detection()
