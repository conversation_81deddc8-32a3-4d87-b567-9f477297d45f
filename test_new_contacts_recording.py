"""
测试新增联系人记录功能
"""

import os
import json
import time
from datetime import datetime
from pathlib import Path

def create_test_user_folder():
    """创建测试用户文件夹"""
    test_user = "test_recording_user"
    user_folder = Path("data") / test_user
    user_folder.mkdir(parents=True, exist_ok=True)
    return user_folder, test_user

def create_initial_contacts():
    """创建初始联系人数据"""
    return [
        {
            "UserName": "initial_contact_001",
            "NickName": "初始联系人1",
            "Alias": "",
            "Remark": "这是初始联系人"
        },
        {
            "UserName": "initial_contact_002",
            "NickName": "初始联系人2", 
            "Alias": "initial2",
            "Remark": ""
        },
        {
            "UserName": "filehelper",
            "NickName": "文件传输助手",
            "Alias": "",
            "Remark": ""
        }
    ]

def create_new_contacts_log(user_folder, username):
    """创建新增联系人记录文件"""
    log_file = user_folder / "new_contacts_log.json"
    
    initial_log = {
        "user": username,
        "created_time": datetime.now().isoformat(),
        "description": "新增联系人记录文件",
        "records": []
    }
    
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump(initial_log, f, ensure_ascii=False, indent=2)
    
    return log_file

def simulate_new_contacts_detection():
    """模拟新增联系人检测和记录"""
    print("="*60)
    print("新增联系人记录功能测试")
    print("="*60)
    
    # 1. 创建测试环境
    print("\n1. 创建测试环境...")
    user_folder, username = create_test_user_folder()
    print(f"✅ 测试用户文件夹: {user_folder}")
    
    # 2. 创建初始联系人
    print("\n2. 创建初始联系人...")
    initial_contacts = create_initial_contacts()
    contacts_file = user_folder / "192_168_1_116_test_recording_user.json"
    
    with open(contacts_file, 'w', encoding='utf-8') as f:
        json.dump(initial_contacts, f, ensure_ascii=False, indent=2)
    print(f"✅ 初始联系人文件: {contacts_file}")
    print(f"   包含 {len(initial_contacts)} 个联系人")
    
    # 3. 创建新增联系人记录文件
    print("\n3. 创建新增联系人记录文件...")
    log_file = create_new_contacts_log(user_folder, username)
    print(f"✅ 记录文件: {log_file}")
    
    # 4. 模拟第一次新增检测
    print("\n4. 模拟第一次新增联系人检测...")
    time.sleep(1)  # 确保时间戳不同
    
    new_contacts_batch1 = [
        {
            "UserName": "new_contact_001",
            "NickName": "新增联系人1",
            "Alias": "",
            "Remark": "第一批新增"
        },
        {
            "UserName": "new_contact_002", 
            "NickName": "新增联系人2",
            "Alias": "new2",
            "Remark": "第一批新增"
        }
    ]
    
    record_new_contacts(log_file, new_contacts_batch1, "第一次检测")
    
    # 5. 模拟第二次新增检测
    print("\n5. 模拟第二次新增联系人检测...")
    time.sleep(2)  # 确保时间戳不同
    
    new_contacts_batch2 = [
        {
            "UserName": "new_contact_003",
            "NickName": "新增联系人3",
            "Alias": "",
            "Remark": "第二批新增"
        }
    ]
    
    record_new_contacts(log_file, new_contacts_batch2, "第二次检测")
    
    # 6. 显示记录结果
    print("\n6. 显示记录结果...")
    display_log_summary(log_file)
    
    # 7. 创建CSV摘要
    print("\n7. 创建CSV摘要...")
    create_csv_summary(user_folder, log_file)
    
    # 8. 清理选项
    print(f"\n8. 测试完成")
    cleanup = input("是否清理测试文件? (y/N): ").lower().strip()
    if cleanup == 'y':
        import shutil
        try:
            shutil.rmtree(user_folder)
            print(f"✅ 测试文件已清理")
        except Exception as e:
            print(f"⚠️  清理失败: {str(e)}")
    else:
        print(f"📁 测试文件保留在: {user_folder}")

def record_new_contacts(log_file, new_contacts, description=""):
    """记录新增联系人"""
    try:
        # 读取现有记录
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        # 创建新增记录
        new_record = {
            "timestamp": datetime.now().isoformat(),
            "detection_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "count": len(new_contacts),
            "description": description,
            "contacts": []
        }
        
        # 添加新增联系人详情
        for contact in new_contacts:
            contact_info = {
                "username": contact.get("UserName", ""),
                "nickname": contact.get("NickName", ""),
                "alias": contact.get("Alias", ""),
                "remark": contact.get("Remark", ""),
                "full_data": contact
            }
            new_record["contacts"].append(contact_info)
        
        # 添加到记录列表
        log_data["records"].append(new_record)
        log_data["last_updated"] = datetime.now().isoformat()
        log_data["total_records"] = len(log_data["records"])
        
        # 保存到文件
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
            
        print(f"✅ 已记录 {len(new_contacts)} 个新增联系人 ({description})")
        for contact in new_contacts:
            print(f"   - {contact.get('NickName', '无昵称')} ({contact.get('UserName', '')})")
        
    except Exception as e:
        print(f"❌ 记录失败: {str(e)}")

def display_log_summary(log_file):
    """显示记录摘要"""
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        print(f"📊 新增联系人记录摘要:")
        print(f"   用户: {log_data.get('user', '未知')}")
        print(f"   创建时间: {log_data.get('created_time', '未知')}")
        print(f"   总记录批次: {len(log_data.get('records', []))}")
        
        total_new_contacts = 0
        for i, record in enumerate(log_data.get('records', []), 1):
            count = record.get('count', 0)
            total_new_contacts += count
            detection_time = record.get('detection_time', '未知')
            description = record.get('description', '')
            
            print(f"   第{i}批: {count}个联系人 - {detection_time} ({description})")
        
        print(f"   总新增联系人: {total_new_contacts}")
        
    except Exception as e:
        print(f"❌ 读取记录失败: {str(e)}")

def create_csv_summary(user_folder, log_file):
    """创建CSV摘要文件"""
    try:
        import csv
        
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        csv_file = user_folder / "new_contacts_summary.csv"
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                "检测时间", "用户名", "昵称", "别名", "备注", "记录批次"
            ])
            
            # 写入数据
            for i, record in enumerate(log_data["records"], 1):
                detection_time = record["detection_time"]
                for contact in record["contacts"]:
                    writer.writerow([
                        detection_time,
                        contact["username"],
                        contact["nickname"],
                        contact["alias"],
                        contact["remark"],
                        f"第{i}批"
                    ])
        
        print(f"✅ CSV摘要文件已创建: {csv_file}")
        
    except Exception as e:
        print(f"❌ 创建CSV摘要失败: {str(e)}")

if __name__ == "__main__":
    simulate_new_contacts_detection()
