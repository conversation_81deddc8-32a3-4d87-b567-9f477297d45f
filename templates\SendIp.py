import tkinter as tk
from tkinter import simpledialog, messagebox
import requests
import threading
import time
import os
import sys
import winreg  # Windows 自启动
import json
from datetime import datetime

CONFIG_FILE = "config.txt"
LOG_FILE = "sendip.log"
DEFAULT_SERVER_URL = "http://127.0.0.1:8080/report_ip"
CHECK_INTERVAL = 60  # 秒

def log_message(message):
    """记录日志消息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}\n"

    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    except Exception as e:
        print(f"写入日志失败: {e}")

    # 同时输出到控制台（调试用）
    print(log_entry.strip())

def get_public_ip():
    """获取公网IP地址"""
    try:
        log_message("🌐 正在获取公网IP地址...")
        response = requests.get('https://api.ipify.org', timeout=10)
        ip = response.text.strip()
        log_message(f"✅ 获取到IP地址: {ip}")
        return ip
    except Exception as e:
        log_message(f"❌ 获取IP地址失败: {e}")
        return None

def send_ip_to_server(ip, username, server_url):
    """发送IP地址到服务器"""
    try:
        log_message(f"📡 正在发送IP地址到服务器...")
        log_message(f"   用户名: {username}")
        log_message(f"   IP地址: {ip}")
        log_message(f"   服务器: {server_url}")

        data = {"username": username, "ip": ip}
        response = requests.post(server_url, json=data, timeout=30)

        log_message(f"📊 服务器响应状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                log_message(f"✅ IP地址发送成功: {result.get('message', '成功')}")
                return True
            except:
                log_message("✅ IP地址发送成功 (无JSON响应)")
                return True
        else:
            log_message(f"❌ 服务器返回错误状态码: {response.status_code}")
            log_message(f"   响应内容: {response.text}")
            return False

    except requests.exceptions.ConnectionError as e:
        log_message(f"❌ 连接服务器失败: {e}")
        return False
    except requests.exceptions.Timeout as e:
        log_message(f"❌ 请求超时: {e}")
        return False
    except Exception as e:
        log_message(f"❌ 发送IP地址失败: {e}")
        return False

def add_to_startup():
    if sys.platform.startswith('win'):
        exe_path = sys.executable
        script_path = os.path.abspath(sys.argv[0])
        run_key = r"Software\Microsoft\Windows\CurrentVersion\Run"
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, run_key, 0, winreg.KEY_ALL_ACCESS) as key:
            winreg.SetValueEx(key, "IPMonitor", 0, winreg.REG_SZ, f'"{exe_path}" "{script_path}"')

def load_config():
    """加载配置文件，支持两种格式"""
    config = {"username": "", "server": DEFAULT_SERVER_URL}

    if not os.path.exists(CONFIG_FILE):
        log_message(f"⚠️ 配置文件不存在: {CONFIG_FILE}")
        return config

    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            lines = [line.strip() for line in f.readlines() if line.strip() and not line.strip().startswith('#')]

        log_message(f"📄 读取配置文件: {CONFIG_FILE}")
        log_message(f"   配置行数: {len(lines)}")

        # 支持两种格式
        if len(lines) > 0:
            # 格式1: key=value 格式
            if '=' in lines[0]:
                for line in lines:
                    if line.startswith("username="):
                        config["username"] = line.split("=", 1)[1]
                    elif line.startswith("server="):
                        config["server"] = line.split("=", 1)[1]
            # 格式2: 按行分隔格式
            else:
                if len(lines) >= 1:
                    config["server"] = lines[0]  # 第一行是服务器地址
                if len(lines) >= 3:
                    config["username"] = lines[2]  # 第三行是用户名

        log_message(f"✅ 配置加载成功:")
        log_message(f"   服务器: {config['server']}")
        log_message(f"   用户名: {config['username'] or '(未设置)'}")

    except Exception as e:
        log_message(f"❌ 读取配置文件失败: {e}")

    return config

def save_config(username, server_url):
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        f.write(f"username={username}\n")
        f.write(f"server={server_url}\n")

class IPMonitorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("IP地址上报工具")

        self.config = load_config()
        self.username = tk.StringVar(value=self.config.get("username", ""))
        self.server_url = self.config.get("server", DEFAULT_SERVER_URL)

        tk.Label(root, text="请输入用户名：").pack(pady=10)
        tk.Entry(root, textvariable=self.username).pack(pady=5)

        tk.Button(root, text="更改服务器地址", command=self.change_server).pack(pady=5)
        tk.Button(root, text="开始监测", command=self.start_monitoring).pack(pady=10)

        self.current_ip = None
        self.monitor_thread = None

    def change_server(self):
        new_url = simpledialog.askstring("更改服务器地址", "请输入服务器地址：", initialvalue=self.server_url)
        if new_url:
            self.server_url = new_url
            messagebox.showinfo("提示", f"服务器地址已更改为：\n{self.server_url}")

    def start_monitoring(self):
        """开始监控IP地址"""
        username = self.username.get().strip()
        if not username:
            messagebox.showwarning("提示", "请输入用户名")
            return

        log_message("🚀 开始启动IP地址监控...")
        log_message(f"   用户名: {username}")
        log_message(f"   服务器: {self.server_url}")

        # 保存配置
        save_config(username, self.server_url)

        # 添加到开机启动
        try:
            add_to_startup()
            log_message("✅ 已添加到开机启动")
        except Exception as e:
            log_message(f"⚠️ 添加开机启动失败: {e}")

        # 隐藏窗口
        self.root.withdraw()

        # 获取初始IP并发送
        log_message("🌐 获取初始IP地址...")
        self.current_ip = get_public_ip()
        if self.current_ip:
            log_message("📡 发送初始IP地址到服务器...")
            if send_ip_to_server(self.current_ip, username, self.server_url):
                log_message("✅ 初始IP地址发送成功")
            else:
                log_message("❌ 初始IP地址发送失败")
        else:
            log_message("❌ 无法获取初始IP地址")

        # 启动监控线程
        log_message("🔄 启动IP监控线程...")
        self.monitor_thread = threading.Thread(
            target=self.monitor_ip_changes, args=(username,), daemon=True
        )
        self.monitor_thread.start()
        log_message("✅ IP监控已启动")

    def monitor_ip_changes(self, username):
        """监控IP地址变化"""
        log_message(f"🔄 开始监控IP地址变化 (用户: {username})")
        log_message(f"   检查间隔: {CHECK_INTERVAL} 秒")
        log_message(f"   当前IP: {self.current_ip}")

        while True:
            try:
                new_ip = get_public_ip()

                if new_ip:
                    if new_ip != self.current_ip:
                        log_message(f"🔄 检测到IP地址变化:")
                        log_message(f"   旧IP: {self.current_ip}")
                        log_message(f"   新IP: {new_ip}")

                        if send_ip_to_server(new_ip, username, self.server_url):
                            self.current_ip = new_ip
                            log_message("✅ IP地址更新成功")
                        else:
                            log_message("❌ IP地址更新失败，将在下次检查时重试")
                    else:
                        log_message(f"✅ IP地址未变化: {new_ip}")
                else:
                    log_message("⚠️ 无法获取IP地址，将在下次检查时重试")

            except Exception as e:
                log_message(f"❌ 监控过程中出现错误: {e}")

            log_message(f"⏰ 等待 {CHECK_INTERVAL} 秒后进行下次检查...")
            time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    root = tk.Tk()
    app = IPMonitorApp(root)
    root.mainloop()
