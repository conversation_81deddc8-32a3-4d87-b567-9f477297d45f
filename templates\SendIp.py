import tkinter as tk
from tkinter import simpledialog, messagebox
import requests
import threading
import time
import os
import sys
import winreg  # Windows 自启动
import json
import socket
from datetime import datetime

CONFIG_FILE = "config.txt"
LOG_FILE = "sendip.log"
DEFAULT_SERVER_URL = "http://127.0.0.1:8080/report_ip"
CHECK_INTERVAL = 30  # 秒 - 改为30秒检查一次，更快检测到变化

def log_message(message):
    """记录日志消息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}\n"

    try:
        with open(LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry)
    except Exception as e:
        print(f"写入日志失败: {e}")

    # 同时输出到控制台（调试用）
    print(log_entry.strip())

def get_local_ip():
    """获取当前连接网络的IP地址"""
    try:
        log_message("🌐 正在获取当前网络连接的IP地址...")

        # 方法1：通过连接外部地址获取当前活跃网络的IP（最准确的方法）
        # 这个方法会返回当前用于访问外网的网络接口的IP地址
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                # 连接到一个外部地址（不会实际发送数据）
                # 这会自动选择当前活跃的网络接口
                s.connect(("*******", 80))
                current_ip = s.getsockname()[0]
                if current_ip and current_ip != "127.0.0.1":
                    log_message(f"✅ 获取到当前网络IP地址: {current_ip}")
                    log_message(f"   这是当前连接网络的IP地址")
                    return current_ip
        except Exception as e:
            log_message(f"⚠️ 方法1获取当前网络IP失败: {e}")

        # 方法2：通过hostname获取默认IP（备用方法）
        try:
            hostname = socket.gethostname()
            default_ip = socket.gethostbyname(hostname)
            if default_ip and default_ip != "127.0.0.1":
                log_message(f"✅ 通过hostname获取到IP地址: {default_ip}")
                log_message(f"   这可能不是当前活跃网络的IP")
                return default_ip
        except Exception as e:
            log_message(f"⚠️ 方法2获取IP失败: {e}")

        # 方法3：显示所有可用IP供参考
        try:
            hostname = socket.gethostname()
            all_ips = socket.gethostbyname_ex(hostname)[2]
            log_message(f"📋 系统所有IP地址: {all_ips}")
            log_message("⚠️ 无法自动确定当前网络IP，请检查网络连接")
        except Exception as e:
            log_message(f"⚠️ 获取系统IP列表失败: {e}")

        log_message("❌ 无法获取当前网络IP地址")
        return None

    except Exception as e:
        log_message(f"❌ 获取当前网络IP地址失败: {e}")
        return None

# 重命名函数以保持兼容性
def get_public_ip():
    """获取本机局域网IP地址（为了保持代码兼容性）"""
    return get_local_ip()

def send_ip_to_server(ip, username, server_url):
    """发送IP地址到服务器"""
    try:
        log_message(f"📡 正在发送IP地址到服务器...")
        log_message(f"   用户名: {username}")
        log_message(f"   IP地址: {ip}")
        log_message(f"   服务器: {server_url}")

        data = {"username": username, "ip": ip}
        response = requests.post(server_url, json=data, timeout=30)

        log_message(f"📊 服务器响应状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                log_message(f"✅ IP地址发送成功: {result.get('message', '成功')}")
                return True
            except:
                log_message("✅ IP地址发送成功 (无JSON响应)")
                return True
        else:
            log_message(f"❌ 服务器返回错误状态码: {response.status_code}")
            log_message(f"   响应内容: {response.text}")
            return False

    except requests.exceptions.ConnectionError as e:
        log_message(f"❌ 连接服务器失败: {e}")
        return False
    except requests.exceptions.Timeout as e:
        log_message(f"❌ 请求超时: {e}")
        return False
    except Exception as e:
        log_message(f"❌ 发送IP地址失败: {e}")
        return False

def add_to_startup():
    if sys.platform.startswith('win'):
        exe_path = sys.executable
        script_path = os.path.abspath(sys.argv[0])
        run_key = r"Software\Microsoft\Windows\CurrentVersion\Run"
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, run_key, 0, winreg.KEY_ALL_ACCESS) as key:
            winreg.SetValueEx(key, "IPMonitor", 0, winreg.REG_SZ, f'"{exe_path}" "{script_path}"')

def load_config():
    """加载配置文件，支持两种格式"""
    config = {"username": "", "server": DEFAULT_SERVER_URL}

    if not os.path.exists(CONFIG_FILE):
        log_message(f"⚠️ 配置文件不存在: {CONFIG_FILE}")
        return config

    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            lines = [line.strip() for line in f.readlines() if line.strip() and not line.strip().startswith('#')]

        log_message(f"📄 读取配置文件: {CONFIG_FILE}")
        log_message(f"   配置行数: {len(lines)}")

        # 支持两种格式
        if len(lines) > 0:
            # 格式1: key=value 格式
            if '=' in lines[0]:
                for line in lines:
                    if line.startswith("username="):
                        config["username"] = line.split("=", 1)[1]
                    elif line.startswith("server="):
                        config["server"] = line.split("=", 1)[1]
            # 格式2: 按行分隔格式
            else:
                if len(lines) >= 1:
                    config["server"] = lines[0]  # 第一行是服务器地址
                if len(lines) >= 3:
                    config["username"] = lines[2]  # 第三行是用户名

        log_message(f"✅ 配置加载成功:")
        log_message(f"   服务器: {config['server']}")
        log_message(f"   用户名: {config['username'] or '(未设置)'}")

    except Exception as e:
        log_message(f"❌ 读取配置文件失败: {e}")

    return config

def save_config(username, server_url):
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        f.write(f"username={username}\n")
        f.write(f"server={server_url}\n")

class IPMonitorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("IP地址上报工具")

        self.config = load_config()
        self.username = tk.StringVar(value=self.config.get("username", ""))
        self.server_url = self.config.get("server", DEFAULT_SERVER_URL)

        tk.Label(root, text="请输入用户名：").pack(pady=10)
        tk.Entry(root, textvariable=self.username).pack(pady=5)

        tk.Button(root, text="更改服务器地址", command=self.change_server).pack(pady=5)
        tk.Button(root, text="开始监测", command=self.start_monitoring).pack(pady=10)

        self.current_ip = None
        self.monitor_thread = None

    def change_server(self):
        new_url = simpledialog.askstring("更改服务器地址", "请输入服务器地址：", initialvalue=self.server_url)
        if new_url:
            self.server_url = new_url
            messagebox.showinfo("提示", f"服务器地址已更改为：\n{self.server_url}")

    def start_monitoring(self):
        """开始监控IP地址"""
        username = self.username.get().strip()
        if not username:
            messagebox.showwarning("提示", "请输入用户名")
            return

        log_message("🚀 开始启动IP地址监控...")
        log_message(f"   用户名: {username}")
        log_message(f"   服务器: {self.server_url}")

        # 保存配置
        save_config(username, self.server_url)

        # 添加到开机启动
        try:
            add_to_startup()
            log_message("✅ 已添加到开机启动")
        except Exception as e:
            log_message(f"⚠️ 添加开机启动失败: {e}")

        # 隐藏窗口
        self.root.withdraw()

        # 立即获取并发送当前IP地址（首次发送）
        log_message("🌐 获取当前IP地址并立即发送...")
        current_ip = get_public_ip()

        if current_ip:
            log_message("📡 首次发送IP地址到服务器...")
            success = send_ip_to_server(current_ip, username, self.server_url)
            if success:
                self.current_ip = current_ip
                log_message("✅ 首次IP地址发送成功")
                messagebox.showinfo("成功", f"IP监控已启动\n当前IP: {current_ip}\n程序将在后台运行")
            else:
                log_message("❌ 首次IP地址发送失败")
                messagebox.showerror("错误", "无法连接到服务器，请检查服务器地址和网络连接")
                self.root.deiconify()  # 显示窗口
                return
        else:
            log_message("❌ 无法获取当前IP地址")
            messagebox.showerror("错误", "无法获取IP地址，请检查网络连接")
            self.root.deiconify()  # 显示窗口
            return

        # 启动监控线程
        log_message("🔄 启动IP监控线程...")
        self.monitor_thread = threading.Thread(
            target=self.monitor_ip_changes, args=(username,), daemon=True
        )
        self.monitor_thread.start()
        log_message("✅ IP监控已启动，程序将在后台持续运行")

    def monitor_ip_changes(self, username):
        """监控IP地址变化"""
        log_message(f"🔄 开始监控IP地址变化 (用户: {username})")
        log_message(f"   检查间隔: {CHECK_INTERVAL} 秒")
        log_message(f"   当前IP: {self.current_ip}")

        consecutive_failures = 0
        max_failures = 3

        while True:
            try:
                log_message(f"🔍 正在检查IP地址变化...")
                new_ip = get_public_ip()

                if new_ip:
                    consecutive_failures = 0  # 重置失败计数

                    if new_ip != self.current_ip:
                        log_message(f"🚨 检测到IP地址变化!")
                        log_message(f"   旧IP: {self.current_ip}")
                        log_message(f"   新IP: {new_ip}")

                        # 尝试发送新IP到服务器
                        log_message("📡 正在发送新IP地址到服务器...")
                        if send_ip_to_server(new_ip, username, self.server_url):
                            old_ip = self.current_ip
                            self.current_ip = new_ip
                            log_message("✅ IP地址更新成功!")
                            log_message(f"   IP已从 {old_ip} 更新为 {new_ip}")
                        else:
                            log_message("❌ IP地址更新失败，将在下次检查时重试")
                    else:
                        log_message(f"✅ IP地址未变化: {new_ip}")
                else:
                    consecutive_failures += 1
                    log_message(f"⚠️ 无法获取IP地址 (连续失败 {consecutive_failures}/{max_failures})")

                    if consecutive_failures >= max_failures:
                        log_message("🚨 连续多次无法获取IP地址，可能网络有问题")
                        # 可以在这里添加更多的错误处理逻辑

            except Exception as e:
                log_message(f"❌ 监控过程中出现错误: {e}")
                consecutive_failures += 1

            log_message(f"⏰ 等待 {CHECK_INTERVAL} 秒后进行下次检查...")
            time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    root = tk.Tk()
    app = IPMonitorApp(root)
    root.mainloop()
