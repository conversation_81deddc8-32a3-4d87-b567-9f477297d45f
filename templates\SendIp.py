import tkinter as tk
from tkinter import simpledialog, messagebox
import requests
import threading
import time
import os
import sys
import winreg  # Windows 自启动

CONFIG_FILE = "config.txt"
DEFAULT_SERVER_URL = "http://yourserver.com/report_ip"
CHECK_INTERVAL = 60  # 秒

def get_public_ip():
    try:
        return requests.get('https://api.ipify.org').text
    except:
        return None

def send_ip_to_server(ip, username, server_url):
    try:
        response = requests.post(server_url, json={"username": username, "ip": ip})
        return response.status_code == 200
    except:
        return False

def add_to_startup():
    if sys.platform.startswith('win'):
        exe_path = sys.executable
        script_path = os.path.abspath(sys.argv[0])
        run_key = r"Software\Microsoft\Windows\CurrentVersion\Run"
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, run_key, 0, winreg.KEY_ALL_ACCESS) as key:
            winreg.SetValueEx(key, "IPMonitor", 0, winreg.REG_SZ, f'"{exe_path}" "{script_path}"')

def load_config():
    config = {"username": "", "server": DEFAULT_SERVER_URL}
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            for line in f:
                if line.startswith("username="):
                    config["username"] = line.strip().split("=", 1)[1]
                elif line.startswith("server="):
                    config["server"] = line.strip().split("=", 1)[1]
    return config

def save_config(username, server_url):
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        f.write(f"username={username}\n")
        f.write(f"server={server_url}\n")

class IPMonitorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("IP地址上报工具")

        self.config = load_config()
        self.username = tk.StringVar(value=self.config.get("username", ""))
        self.server_url = self.config.get("server", DEFAULT_SERVER_URL)

        tk.Label(root, text="请输入用户名：").pack(pady=10)
        tk.Entry(root, textvariable=self.username).pack(pady=5)

        tk.Button(root, text="更改服务器地址", command=self.change_server).pack(pady=5)
        tk.Button(root, text="开始监测", command=self.start_monitoring).pack(pady=10)

        self.current_ip = None
        self.monitor_thread = None

    def change_server(self):
        new_url = simpledialog.askstring("更改服务器地址", "请输入服务器地址：", initialvalue=self.server_url)
        if new_url:
            self.server_url = new_url
            messagebox.showinfo("提示", f"服务器地址已更改为：\n{self.server_url}")

    def start_monitoring(self):
        username = self.username.get().strip()
        if not username:
            messagebox.showwarning("提示", "请输入用户名")
            return

        save_config(username, self.server_url)
        add_to_startup()
        self.root.withdraw()  # 隐藏窗口

        self.current_ip = get_public_ip()
        if self.current_ip:
            send_ip_to_server(self.current_ip, username, self.server_url)

        self.monitor_thread = threading.Thread(
            target=self.monitor_ip_changes, args=(username,), daemon=True
        )
        self.monitor_thread.start()

    def monitor_ip_changes(self, username):
        while True:
            new_ip = get_public_ip()
            if new_ip and new_ip != self.current_ip:
                send_ip_to_server(new_ip, username, self.server_url)
                self.current_ip = new_ip
            time.sleep(CHECK_INTERVAL)

if __name__ == "__main__":
    root = tk.Tk()
    app = IPMonitorApp(root)
    root.mainloop()
