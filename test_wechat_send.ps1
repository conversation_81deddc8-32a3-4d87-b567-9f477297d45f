# 微信消息发送功能测试脚本 (PowerShell)

Write-Host "🚀 微信消息发送功能测试 (PowerShell)" -ForegroundColor Green
Write-Host "=" * 60

# 测试1: 通过Flask应用发送消息
Write-Host "`n📋 测试1: 通过Flask应用发送消息" -ForegroundColor Yellow
Write-Host "📡 API地址: http://localhost:5000/api/send_wechat_message"

$flaskBody = @{
    target_host = "localhost:8000"
    receiver = "小马"
    msg = "Hello from PowerShell! 这是通过Flask应用发送的测试消息"
} | ConvertTo-Json -Depth 3

Write-Host "📤 发送数据:" -ForegroundColor Cyan
Write-Host $flaskBody

try {
    $flaskResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/send_wechat_message" -Method Post -Body $flaskBody -ContentType "application/json" -TimeoutSec 30
    
    Write-Host "✅ Flask API调用成功" -ForegroundColor Green
    Write-Host "📄 响应内容:" -ForegroundColor Cyan
    $flaskResponse | ConvertTo-Json -Depth 5 | Write-Host
    
} catch {
    Write-Host "❌ Flask API调用失败" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Message -like "*连接*" -or $_.Exception.Message -like "*Connection*") {
        Write-Host "💡 请确保Flask应用正在运行在 http://localhost:5000" -ForegroundColor Yellow
    }
}

Write-Host "`n" + "-" * 40

# 测试2: 直接调用微信API
Write-Host "`n📋 测试2: 直接调用微信API" -ForegroundColor Yellow
Write-Host "📡 API地址: http://localhost:8000/api/sendMsg"

$directBody = @{
    receiver = "小马"
    msg = "Hello from PowerShell! 这是直接调用微信API的测试消息"
} | ConvertTo-Json -Depth 3

Write-Host "📤 发送数据:" -ForegroundColor Cyan
Write-Host $directBody

try {
    $directResponse = Invoke-RestMethod -Uri "http://localhost:8000/api/sendMsg" -Method Post -Body $directBody -ContentType "application/json" -TimeoutSec 30
    
    Write-Host "✅ 微信API调用成功" -ForegroundColor Green
    Write-Host "📄 响应内容:" -ForegroundColor Cyan
    $directResponse | ConvertTo-Json -Depth 5 | Write-Host
    
} catch {
    Write-Host "❌ 微信API调用失败" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Message -like "*连接*" -or $_.Exception.Message -like "*Connection*") {
        Write-Host "💡 请确保微信API服务正在运行在 http://localhost:8000" -ForegroundColor Yellow
    }
}

Write-Host "`n" + "-" * 40

# 测试3: 批量消息发送测试
Write-Host "`n📋 测试3: 批量消息发送测试" -ForegroundColor Yellow

$batchMessages = @(
    @{
        target_host = "localhost:8000"
        receiver = "小马"
        msg = "批量消息 1/3: 测试消息发送功能"
    },
    @{
        target_host = "localhost:8000"
        receiver = "小马"
        msg = "批量消息 2/3: 包含emoji的消息 😊🚀📱"
    },
    @{
        target_host = "localhost:8000"
        receiver = "小马"
        msg = "批量消息 3/3: 多行消息测试`n第二行内容`n第三行内容"
    }
)

$successCount = 0
$totalCount = $batchMessages.Count

foreach ($i in 0..($batchMessages.Count - 1)) {
    $message = $batchMessages[$i]
    $messageBody = $message | ConvertTo-Json -Depth 3
    
    Write-Host "📤 发送消息 $($i + 1)/$totalCount" -ForegroundColor Cyan
    Write-Host "👤 接收人: $($message.receiver)"
    Write-Host "💬 内容: $($message.msg.Substring(0, [Math]::Min(50, $message.msg.Length)))..."
    
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:5000/api/send_wechat_message" -Method Post -Body $messageBody -ContentType "application/json" -TimeoutSec 30
        
        if ($response.status -eq "success") {
            Write-Host "✅ 消息 $($i + 1) 发送成功" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "❌ 消息 $($i + 1) 发送失败: $($response.message)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "❌ 消息 $($i + 1) 发送失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 添加延迟避免发送过快
    if ($i -lt $batchMessages.Count - 1) {
        Start-Sleep -Seconds 1
    }
}

Write-Host "`n📊 批量发送统计:" -ForegroundColor Yellow
Write-Host "✅ 成功: $successCount/$totalCount" -ForegroundColor Green
Write-Host "❌ 失败: $($totalCount - $successCount)/$totalCount" -ForegroundColor Red

Write-Host "`n" + "-" * 40

# 显示使用示例
Write-Host "`n📖 PowerShell使用示例" -ForegroundColor Yellow

$exampleCode = @"
# 基本用法
`$body = @{
    target_host = "localhost:8000"
    receiver = "联系人昵称"
    msg = "消息内容"
} | ConvertTo-Json

`$response = Invoke-RestMethod -Uri "http://localhost:5000/api/send_wechat_message" -Method Post -Body `$body -ContentType "application/json"

# 检查结果
if (`$response.status -eq "success") {
    Write-Host "消息发送成功" -ForegroundColor Green
} else {
    Write-Host "消息发送失败: `$(`$response.message)" -ForegroundColor Red
}
"@

Write-Host $exampleCode -ForegroundColor Gray

Write-Host "`n🏁 测试完成" -ForegroundColor Green
Write-Host "💡 如需查看详细日志，请检查Flask应用的控制台输出"
