#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标签提取提示词设计
"""

def create_tag_extraction_prompt(analysis_report):
    """创建标签提取的提示词"""
    
    prompt = f"""
请从以下分析报告中提取候选人的关键标签。

## 分析报告内容：
{analysis_report}

## 标签提取任务：
请仔细阅读上述分析报告，从中提取出最能代表候选人特征的关键标签。

## 标签类型要求：
1. **专业技能**：如Java、Python、前端、后端、AI、产品经理等
2. **工作状态**：如全职、兼职、主动求职、被动求职等  
3. **薪资水平**：如10-20万、20-30万、30-50万、50万+等
4. **地理位置**：如北京、上海、深圳、海外等
5. **学术背景**：如教授、博导、研究员、博士等
6. **人才项目**：如千人计划、青千、优青、杰青、长江学者等
7. **工作意愿**：如意愿强烈、意愿中性、意愿一般等
8. **个人特质**：如沟通良好、技术扎实、有潜力、活跃等

## 输出格式要求：
- 只输出标签，不要解释
- 用逗号分隔多个标签
- 每个标签2-6个字
- 总共提取5-8个最重要的标签
- 只提取确实能从报告中得出的标签

## 输出示例：
Java,后端开发,全职,主动求职,30-50万,北京,意愿强烈,技术扎实

请提取标签：
"""
    
    return prompt

def create_simple_tag_extraction_prompt(analysis_report):
    """创建简化版标签提取提示词"""
    
    prompt = f"""
请从这个分析报告中找出候选人的关键特征标签：

报告内容：
{analysis_report[:1000]}

请提取：技能、工作状态、地点、薪资、意愿等关键标签，用逗号分隔。
"""
    
    return prompt

def create_ultra_simple_prompt(analysis_report):
    """创建超简化版提示词"""
    
    prompt = f"""
从这个文本中提取关键词：

{analysis_report[:800]}

提取关键词，用逗号分隔：
"""
    
    return prompt

# 测试用的分析报告示例
SAMPLE_ANALYSIS_REPORT = """
### 1. 候选人心理状态分析
通过分析与候选人 张工程师 的 45 条聊天记录，发现以下职业心理特征：
- **求职心态**: 候选人表现出积极但谨慎的求职态度，对新机会有兴趣但不急躁
- **职业焦虑**: 对当前工作环境有一定不满，希望寻求更好的发展机会
- **沟通风格**: 表达清晰，回复及时，展现出良好的职业素养

### 2. 求职意愿解读
- **跳槽动机**: 主要出于职业发展考虑，希望获得更大的平台和挑战
- **薪资期望**: 期望薪资水平在30-40万之间，更注重长期发展前景
- **时间安排**: 具有一定的紧迫性，但愿意等待合适的机会

### 3. 专业能力评估
- **技术水平**: 具备扎实的Java后端开发基础，有5年工作经验
- **沟通能力**: 表达清晰，逻辑性强，适合团队协作
- **学习态度**: 对新技术和新领域保持开放态度

### 4. 合作建议
- **沟通策略**: 建议采用专业但友好的沟通方式
- **职位匹配**: 适合中高级Java后端开发或技术管理岗位
- **跟进方式**: 保持定期联系，及时反馈职位信息

*本分析基于聊天记录内容，仅供参考。*
"""

def test_prompts():
    """测试不同的提示词"""
    print("🧪 标签提取提示词测试")
    print("=" * 60)
    
    print("\n📝 测试报告内容:")
    print(SAMPLE_ANALYSIS_REPORT[:200] + "...")
    
    print("\n🎯 提示词1: 详细版")
    print("-" * 40)
    prompt1 = create_tag_extraction_prompt(SAMPLE_ANALYSIS_REPORT)
    print(prompt1)
    
    print("\n🎯 提示词2: 简化版")
    print("-" * 40)
    prompt2 = create_simple_tag_extraction_prompt(SAMPLE_ANALYSIS_REPORT)
    print(prompt2)
    
    print("\n🎯 提示词3: 超简化版")
    print("-" * 40)
    prompt3 = create_ultra_simple_prompt(SAMPLE_ANALYSIS_REPORT)
    print(prompt3)

if __name__ == "__main__":
    test_prompts()
