#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信消息发送功能测试脚本
"""

import requests
import json
import sys

def test_send_message_api():
    """测试微信消息发送API"""
    
    # 测试数据
    test_cases = [
        {
            "name": "基本消息发送测试",
            "data": {
                "target_host": "localhost:8000",
                "receiver": "小马",
                "msg": "Hello, 这是来自Flask应用的测试消息！"
            }
        },
        {
            "name": "长消息发送测试", 
            "data": {
                "target_host": "localhost:8000",
                "receiver": "小马",
                "msg": "这是一条较长的测试消息，用来验证系统是否能够正确处理包含多行文本和特殊字符的消息内容。\n\n包含换行符、emoji 😊 和中英文混合内容。\n\nTesting English content as well."
            }
        },
        {
            "name": "不同目标主机测试",
            "data": {
                "target_host": "*************:8000",
                "receiver": "测试用户",
                "msg": "测试不同目标主机的消息发送"
            }
        }
    ]
    
    # Flask应用的API地址
    api_url = "http://localhost:8080/api/send_wechat_message"
    
    print("🧪 开始测试微信消息发送API")
    print(f"📡 API地址: {api_url}")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"🎯 目标主机: {test_case['data']['target_host']}")
        print(f"👤 接收人: {test_case['data']['receiver']}")
        print(f"💬 消息: {test_case['data']['msg'][:50]}{'...' if len(test_case['data']['msg']) > 50 else ''}")
        
        try:
            # 发送请求
            response = requests.post(
                api_url,
                json=test_case['data'],
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            # 解析响应
            try:
                result = response.json()
                print(f"📄 响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                if result.get('status') == 'success':
                    print("✅ 测试通过")
                else:
                    print("❌ 测试失败")
                    
            except ValueError as e:
                print(f"⚠️ 响应不是有效的JSON格式: {e}")
                print(f"📄 原始响应: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败 - Flask应用可能未运行")
            print("💡 请确保Flask应用正在运行在 http://localhost:8080")
            
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        print("-" * 40)

def test_direct_wechat_api():
    """直接测试微信API（不通过Flask）"""
    print("\n🔗 直接测试微信API")
    print("=" * 60)
    
    # 直接调用微信API的测试数据
    direct_test_data = {
        "receiver": "小马",
        "msg": "这是直接调用微信API的测试消息"
    }
    
    # 微信API地址
    wechat_api_url = "http://localhost:8000/api/sendMsg"
    
    print(f"📡 微信API地址: {wechat_api_url}")
    print(f"👤 接收人: {direct_test_data['receiver']}")
    print(f"💬 消息: {direct_test_data['msg']}")
    
    try:
        response = requests.post(
            wechat_api_url,
            json=direct_test_data,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        try:
            result = response.json()
            print(f"📄 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        except ValueError:
            print(f"📄 响应内容: {response.text}")
            
        if response.status_code == 200:
            print("✅ 直接API调用成功")
        else:
            print("❌ 直接API调用失败")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 微信API服务可能未运行")
        print("💡 请确保微信API服务正在运行在 http://localhost:8000")
        
    except requests.exceptions.Timeout:
        print("⏰ 请求超时")
        
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def test_curl_examples():
    """显示curl命令示例"""
    print("\n📋 curl命令示例")
    print("=" * 60)
    
    # Flask API curl示例
    flask_curl = '''
# 通过Flask应用发送消息
curl -X POST http://localhost:5000/api/send_wechat_message \\
  -H "Content-Type: application/json" \\
  -d '{
    "target_host": "localhost:8000",
    "receiver": "小马",
    "msg": "Hello from curl!"
  }'
'''
    
    # 直接微信API curl示例
    direct_curl = '''
# 直接调用微信API
curl -X POST http://localhost:8000/api/sendMsg \\
  -H "Content-Type: application/json" \\
  -d '{
    "receiver": "小马",
    "msg": "Hello from curl!"
  }'
'''
    
    print("🔧 Flask API调用:")
    print(flask_curl)
    
    print("🔧 直接微信API调用:")
    print(direct_curl)

def main():
    """主函数"""
    print("🚀 微信消息发送功能测试")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "flask":
            test_send_message_api()
        elif test_type == "direct":
            test_direct_wechat_api()
        elif test_type == "curl":
            test_curl_examples()
        else:
            print("❌ 未知的测试类型")
            print("💡 使用方法:")
            print("  python test_wechat_send.py flask   # 测试Flask API")
            print("  python test_wechat_send.py direct  # 直接测试微信API")
            print("  python test_wechat_send.py curl    # 显示curl示例")
    else:
        # 运行所有测试
        test_send_message_api()
        test_direct_wechat_api()
        test_curl_examples()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
