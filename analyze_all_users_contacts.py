"""
使用最精确的规则分析所有用户的直接联系人数量
"""

import json
from pathlib import Path
from collections import defaultdict
import re

class AllUsersContactAnalyzer:
    def __init__(self):
        self.user_results = {}
        
    def is_real_personal_contact(self, contact):
        """最精确的个人联系人判断规则"""
        username = contact.get("UserName", "")
        nickname = contact.get("NickName", "") or ""
        alias = contact.get("<PERSON><PERSON>", "") or ""
        remark = contact.get("Remark", "") or ""
        
        # 1. 排除群聊（@chatroom结尾）
        if username.endswith("@chatroom"):
            return False, "群聊(@chatroom)"
        
        # 2. 排除公众号（gh_开头）
        if username.startswith("gh_"):
            return False, "公众号(gh_)"
        
        # 3. 排除企业微信（@openim）
        if "@openim" in username:
            return False, "企业微信(@openim)"
        
        # 4. 排除系统账号
        system_accounts = {
            "filehelper", "weixin", "fmessage", "floatbottle", "medianote", 
            "notifymessage", "tmessage", "qmessage", "qqmail", "wxitil",
            "brandcontact", "helper_entry", "pc_share", "newsapp", "facebookapp",
            "tmessage", "qqsync", "shakeapp", "medianote", "qqfriend", "readerapp"
        }
        if username in system_accounts:
            return False, f"系统账号({username})"
        
        # 5. 基于备注的工作相关过滤
        if remark:
            work_related_patterns = [
                # 政府机构
                r".*[县市区]委.*", r".*[县市区]政府.*", r".*[县市区]局.*", 
                r".*[县市区]办.*", r".*[县市区]厅.*", r".*[县市区]部.*",
                r".*街道.*办.*", r".*社区.*", r".*村委.*", r".*镇政府.*",
                r".*组织部.*", r".*宣传部.*", r".*统战部.*",
                
                # 企业标识
                r".*有限公司.*", r".*股份.*公司.*", r".*集团.*公司.*",
                r".*企业.*", r".*工厂.*", r".*制造.*",
                
                # 教育机构工作人员
                r".*学校.*老师.*", r".*大学.*老师.*", r".*学院.*老师.*",
                r".*教授.*", r".*博导.*", r".*导师.*",
                
                # 工作职位
                r".*经理.*", r".*主管.*", r".*总监.*", r".*主任.*",
                r".*处长.*", r".*科长.*", r".*局长.*", r".*部长.*",
                
                # 服务行业工作人员
                r".*银行.*", r".*保险.*", r".*中介.*", r".*代理.*",
                r".*销售.*", r".*客服.*", r".*服务.*中心.*"
            ]
            
            for pattern in work_related_patterns:
                if re.search(pattern, remark):
                    return False, f"工作备注"
        
        # 6. 排除长数字ID
        if username.isdigit() and len(username) > 12:
            return False, f"长数字ID"
        
        # 7. 排除特殊企业标识
        if any(pattern in username for pattern in ["@stranger", "v3_020b", "v1_"]):
            return False, f"特殊标识"
        
        # 8. 排除企业/服务账号昵称
        enterprise_keywords = [
            "有限公司", "股份有限", "集团", "工作室", "工作号",
            "有限责任公司", "股份公司", "责任有限公司",
            "行政人事", "管理部", "人事部", "财务部", "市场部", "销售部",
            "客服部", "运营部", "产品部", "设计部",
            "客服", "售后", "支持", "帮助", "咨询", "顾问",
            "service", "support", "help", "customer service",
            "系统", "平台", "中心", "智能", "自动", "机器人", "bot", "AI助手",
            "小助手", "智能助手", "自动回复",
            "生产商", "制造商", "厂家", "供应商", "经销商", "代理商",
            "登报服务", "广告公司", "推广", "营销", "宣传",
            "培训机构", "咨询公司", "法律事务所",
            "官方", "认证", "验证", "审核", "监管", "管理员",
            "通知", "公告", "消息中心", "提醒"
        ]
        
        for keyword in enterprise_keywords:
            if keyword in nickname:
                return False, f"企业昵称"
        
        # 9. 检查别名中的企业标识
        alias_keywords = [
            "corp", "company", "ltd", "inc", "admin", "service", "support",
            "business", "official", "manager", "assistant",
            "工作", "公司", "企业", "管理", "客服", "服务", "助手"
        ]
        
        alias_lower = alias.lower()
        for keyword in alias_keywords:
            if keyword in alias_lower:
                return False, f"企业别名"
        
        # 10. 排除全大写英文
        if nickname:
            if nickname.isupper() and len(nickname) > 3 and nickname.isalpha():
                return False, f"全大写英文"
            
            if any(symbol in nickname for symbol in ["®", "™", "©", "℠"]):
                return False, f"企业符号"
        
        # 11. 排除测试账号
        test_patterns = ["test", "测试", "demo", "sample", "example", "temp"]
        for pattern in test_patterns:
            if pattern in nickname.lower() or pattern in username.lower():
                return False, f"测试账号"
        
        # 12. 排除商业联系人
        if nickname:
            business_patterns = [
                r".*代理.*", r".*经销.*", r".*批发.*", r".*零售.*",
                r".*招商.*", r".*加盟.*", r".*投资.*", r".*融资.*",
                r".*房产.*", r".*地产.*", r".*装修.*", r".*建材.*",
                r".*保险.*", r".*理财.*", r".*贷款.*", r".*信贷.*"
            ]
            
            for pattern in business_patterns:
                if re.search(pattern, nickname):
                    return False, f"商业昵称"
        
        # 13. 排除编号备注（可能的工作联系人）
        if remark:
            number_patterns = [
                r"^\d{4,6}\s+.*",  # 以4-6位数字开头
                r".*\d{4,6}$",     # 以4-6位数字结尾
            ]
            
            for pattern in number_patterns:
                if re.search(pattern, remark):
                    # 如果包含个人信息则保留
                    personal_indicators = ["朋友", "同学", "同事", "老乡", "室友", "邻居"]
                    if not any(indicator in remark for indicator in personal_indicators):
                        return False, f"编号备注"
        
        return True, "真实个人联系人"
    
    def analyze_user_contacts(self, user_folder):
        """分析单个用户的联系人"""
        username = user_folder.name
        
        # 查找联系人文件
        contact_files = [f for f in user_folder.glob("*.json") 
                        if not f.name.startswith(("new_contacts", "ip_change"))]
        
        if not contact_files:
            return {
                "username": username,
                "total_contacts": 0,
                "personal_contacts": 0,
                "filtered_contacts": 0,
                "files": [],
                "error": "没有找到联系人文件"
            }
        
        total_contacts = 0
        personal_contacts = []
        filtered_categories = defaultdict(int)
        files_info = []
        
        for contact_file in contact_files:
            try:
                with open(contact_file, 'r', encoding='utf-8') as f:
                    contacts = json.load(f)
                
                if not isinstance(contacts, list):
                    continue
                
                file_total = len(contacts)
                file_personal = 0
                
                for contact in contacts:
                    total_contacts += 1
                    is_personal, reason = self.is_real_personal_contact(contact)
                    
                    if is_personal:
                        personal_contacts.append(contact)
                        file_personal += 1
                    else:
                        category = reason.split('(')[0] if '(' in reason else reason
                        filtered_categories[category] += 1
                
                files_info.append({
                    "filename": contact_file.name,
                    "total": file_total,
                    "personal": file_personal,
                    "filtered": file_total - file_personal
                })
                
            except Exception as e:
                files_info.append({
                    "filename": contact_file.name,
                    "error": str(e)
                })
        
        return {
            "username": username,
            "total_contacts": total_contacts,
            "personal_contacts": len(personal_contacts),
            "filtered_contacts": total_contacts - len(personal_contacts),
            "personal_rate": f"{(len(personal_contacts)/total_contacts*100):.1f}%" if total_contacts > 0 else "0%",
            "files": files_info,
            "filtered_categories": dict(filtered_categories)
        }
    
    def analyze_all_users(self):
        """分析所有用户的联系人"""
        print("🔍 开始分析所有用户的直接联系人数量...")
        print("=" * 80)
        
        data_dir = Path("data")
        
        if not data_dir.exists():
            print("❌ data目录不存在")
            return
        
        # 排除测试文件夹
        excluded_folders = {"test_ip_change_user"}
        user_folders = [d for d in data_dir.iterdir() 
                       if d.is_dir() and d.name not in excluded_folders]
        
        if not user_folders:
            print("❌ 没有找到用户文件夹")
            return
        
        print(f"📊 找到 {len(user_folders)} 个用户文件夹")
        
        # 分析每个用户
        for i, user_folder in enumerate(sorted(user_folders), 1):
            print(f"\n{i}. 分析用户: {user_folder.name}")
            result = self.analyze_user_contacts(user_folder)
            self.user_results[user_folder.name] = result
            
            if "error" in result:
                print(f"   ❌ {result['error']}")
            else:
                print(f"   📊 总联系人: {result['total_contacts']} 个")
                print(f"   👥 真实个人联系人: {result['personal_contacts']} 个 ({result['personal_rate']})")
                print(f"   🚫 过滤掉: {result['filtered_contacts']} 个")
                
                # 显示文件信息
                for file_info in result['files']:
                    if "error" in file_info:
                        print(f"      ❌ {file_info['filename']}: {file_info['error']}")
                    else:
                        print(f"      📄 {file_info['filename']}: {file_info['personal']}/{file_info['total']} 个人联系人")
        
        # 生成汇总报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print(f"\n" + "=" * 80)
        print("📊 所有用户直接联系人数量汇总")
        print("=" * 80)
        
        # 统计总数
        total_users = len(self.user_results)
        total_all_contacts = sum(r['total_contacts'] for r in self.user_results.values() if 'total_contacts' in r)
        total_personal_contacts = sum(r['personal_contacts'] for r in self.user_results.values() if 'personal_contacts' in r)
        total_filtered = total_all_contacts - total_personal_contacts
        
        print(f"\n📈 总体统计:")
        print(f"   用户数量: {total_users}")
        print(f"   总联系人: {total_all_contacts:,}")
        print(f"   真实个人联系人: {total_personal_contacts:,}")
        print(f"   过滤掉的联系人: {total_filtered:,}")
        print(f"   个人联系人占比: {(total_personal_contacts/total_all_contacts*100):.1f}%")
        
        print(f"\n👥 各用户详细统计:")
        print(f"{'用户名':<15} {'总联系人':<10} {'个人联系人':<12} {'占比':<8} {'过滤数':<8}")
        print("-" * 60)
        
        # 按个人联系人数量排序
        sorted_users = sorted(self.user_results.items(), 
                            key=lambda x: x[1].get('personal_contacts', 0), 
                            reverse=True)
        
        for username, result in sorted_users:
            if 'total_contacts' in result:
                print(f"{username:<15} {result['total_contacts']:<10} "
                      f"{result['personal_contacts']:<12} {result['personal_rate']:<8} "
                      f"{result['filtered_contacts']:<8}")
            else:
                print(f"{username:<15} {'错误':<10} {'错误':<12} {'错误':<8} {'错误':<8}")
        
        # 生成详细文件报告
        self.generate_detailed_file_report()
        
        print(f"\n📄 详细报告已保存到: all_users_contacts_analysis.txt")
    
    def generate_detailed_file_report(self):
        """生成详细文件报告"""
        report_file = "all_users_contacts_analysis.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("所有用户微信直接联系人分析报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 总体统计
            total_users = len(self.user_results)
            total_all_contacts = sum(r['total_contacts'] for r in self.user_results.values() if 'total_contacts' in r)
            total_personal_contacts = sum(r['personal_contacts'] for r in self.user_results.values() if 'personal_contacts' in r)
            total_filtered = total_all_contacts - total_personal_contacts
            
            f.write(f"总体统计:\n")
            f.write(f"  用户数量: {total_users}\n")
            f.write(f"  总联系人: {total_all_contacts:,}\n")
            f.write(f"  真实个人联系人: {total_personal_contacts:,}\n")
            f.write(f"  过滤掉的联系人: {total_filtered:,}\n")
            f.write(f"  个人联系人占比: {(total_personal_contacts/total_all_contacts*100):.1f}%\n\n")
            
            # 各用户详细信息
            f.write("各用户详细统计:\n\n")
            
            sorted_users = sorted(self.user_results.items(), 
                                key=lambda x: x[1].get('personal_contacts', 0), 
                                reverse=True)
            
            for username, result in sorted_users:
                f.write(f"{username}:\n")
                
                if 'error' in result:
                    f.write(f"  错误: {result['error']}\n\n")
                    continue
                
                f.write(f"  总联系人: {result['total_contacts']}\n")
                f.write(f"  真实个人联系人: {result['personal_contacts']} ({result['personal_rate']})\n")
                f.write(f"  过滤掉: {result['filtered_contacts']}\n")
                
                # 文件详情
                f.write(f"  文件详情:\n")
                for file_info in result['files']:
                    if "error" in file_info:
                        f.write(f"    {file_info['filename']}: 错误 - {file_info['error']}\n")
                    else:
                        f.write(f"    {file_info['filename']}: {file_info['personal']}/{file_info['total']} 个人联系人\n")
                
                # 过滤分类
                if result['filtered_categories']:
                    f.write(f"  过滤分类:\n")
                    for category, count in sorted(result['filtered_categories'].items()):
                        f.write(f"    {category}: {count}个\n")
                
                f.write("\n")

def main():
    analyzer = AllUsersContactAnalyzer()
    
    try:
        analyzer.analyze_all_users()
        print(f"\n🎉 分析完成！")
        
    except Exception as e:
        print(f"\n❌ 分析失败: {str(e)}")

if __name__ == "__main__":
    main()
