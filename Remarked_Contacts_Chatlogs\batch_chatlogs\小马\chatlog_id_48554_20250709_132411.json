{"user": "小马", "contact_id": "48554", "contact_info": {"UserName": "wxid_7990479906412", "Alias": "stzhang94", "Remark": "48554张博", "NickName": "Shuting"}, "fetch_time": "2025-07-09T13:24:11.927091", "message_count": 15, "chatlog_data": [{"seq": 1741237380000, "time": "2025-03-06T13:03:00+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_7990479906412", "senderName": "48554张博", "isSelf": false, "type": 1, "subType": 0, "content": "我通过了你的朋友验证请求，现在我们可以开始聊天了"}, {"seq": 1741237490000, "time": "2025-03-06T13:04:50+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士您好"}, {"seq": 1741237497000, "time": "2025-03-06T13:04:57+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们是一家专业的猎头公司，搭建高层次人才与国内企业高校就业创业全职兼职合作的桥梁；可以柔性合作或者全职回国就业创业。"}, {"seq": 1741237513000, "time": "2025-03-06T13:05:13+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "如果机会合适的话您会考虑全职回国发展吗"}, {"seq": 1741239007000, "time": "2025-03-06T13:30:07+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_7990479906412", "senderName": "48554张博", "isSelf": false, "type": 1, "subType": 0, "content": "您好"}, {"seq": 1741239069000, "time": "2025-03-06T13:31:09+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_7990479906412", "senderName": "48554张博", "isSelf": false, "type": 1, "subType": 0, "content": "如果有合适的职位我愿意回国全职发展"}, {"seq": 1741239102000, "time": "2025-03-06T13:31:42+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们主要是江浙和山东这一带，您有意向城市吗"}, {"seq": 1741243040000, "time": "2025-03-06T14:37:20+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_7990479906412", "senderName": "48554张博", "isSelf": false, "type": 1, "subType": 0, "content": "没有具体的意向城市，主要还是看职位是否合适"}, {"seq": 1741243313000, "time": "2025-03-06T14:41:53+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "明白，我们还有一种模式是配合申报，会有5000-10000的报酬，这个的话就是不要求您落地，如果是这方面的话您会考虑吗"}, {"seq": 1741266684000, "time": "2025-03-06T21:11:24+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_7990479906412", "senderName": "48554张博", "isSelf": false, "type": 1, "subType": 0, "content": "这方面的话我不是很了解，可以详细介绍一下吗"}, {"seq": 1741308075000, "time": "2025-03-07T08:41:15+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您有时间吗，我跟您电话聊一下呀"}, {"seq": 1741309480000, "time": "2025-03-07T09:04:40+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我先简单跟您介绍一下，配合申报的话就是不需要跟企业对接，而且不论是否入选(有效申报即可）都会有报酬的，入选了更好呀，奖金也会更多，不入选也会有报酬嘛，这样会更好一些"}, {"seq": 1749786330000, "time": "2025-06-13T11:45:30+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "张博您好呀，您今年是否有参与申报qm呀"}, {"seq": 1749786374000, "time": "2025-06-13T11:46:14+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "因为现在qm开始了第二批报名，我们也为您匹配到了单位，我发您看看"}, {"seq": 1749786386000, "time": "2025-06-13T11:46:26+08:00", "talker": "wxid_7990479906412", "talkerName": "", "isChatRoom": false, "sender": "wxid_jkufhd91scgp22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "https://www.nbmksw.com/  这是公司官网，您可以看下"}]}