{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "28221", "contact_info": {"UserName": "daming108792", "Alias": "", "Remark": "28221 罗明伟", "NickName": "<PERSON><PERSON>伟Chris"}, "fetch_time": "2025-07-09T13:20:57.420443", "message_count": 24, "chatlog_data": [{"seq": 1683695686000, "time": "2023-05-10T13:14:46+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "我通过了你的朋友验证请求，现在我们可以开始聊天了"}, {"seq": 1683695716000, "time": "2023-05-10T13:15:16+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士，您好！我是猎头顾问小朱"}, {"seq": 1683695757000, "time": "2023-05-10T13:15:57+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "浙江省的省级人才引进计划已经开始了"}, {"seq": 1683695776000, "time": "2023-05-10T13:16:16+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "请问您之前是报过深圳的吗？"}, {"seq": 1683695847000, "time": "2023-05-10T13:17:27+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "您好"}, {"seq": 1683695850000, "time": "2023-05-10T13:17:30+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "是"}, {"seq": 1683695882000, "time": "2023-05-10T13:18:02+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "深圳三四月截止了"}, {"seq": 1683695993000, "time": "2023-05-10T13:19:53+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的，博士。我们主要是做浙江省的。并且这次浙江省的人才引进政策已经开始了。"}, {"seq": 1683696009000, "time": "2023-05-10T13:20:09+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您对于工作地点有要求吗？"}, {"seq": 1683696478000, "time": "2023-05-10T13:27:58+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "政策可发我看下？"}, {"seq": 1683696505000, "time": "2023-05-10T13:28:25+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "材料有"}, {"seq": 1683696515000, "time": "2023-05-10T13:28:35+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士，不好意思，这个文件是红头文件，不可外发的"}, {"seq": 1683696611000, "time": "2023-05-10T13:30:11+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "并且今年省级政策，每个区域的都有区别，如果为您成功匹配企业，会由政府方为您介绍相关政策。"}, {"seq": 1683696828000, "time": "2023-05-10T13:33:48+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "喔"}, {"seq": 1683696907000, "time": "2023-05-10T13:35:07+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "先看看吧，我可以把简历发你，有合适的再议，我这边很晚了，先下了"}, {"seq": 1683696949000, "time": "2023-05-10T13:35:49+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1683696964000, "time": "2023-05-10T13:36:04+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士为了后续做好人才服务，我们需要跟您确认一些信息，烦请您方便时候回复下以下信息：\n之前是否申报过类似项目？\n申报后是意向兼职还是全职还是两者均可？\n意向高校还是企业还是两者均可？\n如果全职期望年薪是？"}, {"seq": 1683697058000, "time": "2023-05-10T13:37:38+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "前一阵在深圳报过一次，看岗位吧[合十]"}, {"seq": 1683698125000, "time": "2023-05-10T13:55:25+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的，博士，您有空发一份简历，我们会根据简历为您匹配"}, {"seq": 1684255003000, "time": "2023-05-17T00:36:43+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 1, "subType": 0, "content": "我发下简历"}, {"seq": 1684255037000, "time": "2023-05-17T00:37:17+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "daming108792", "senderName": "28221 罗明伟", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"wx10ea681453646930\" sdkver=\"0\">\n\t\t<title>罗明伟的简历.pdf.pdf</title>\n\t\t<des>笔记</des>\n\t\t<type>6</type>\n\t\t<messageext>pdf</messageext>\n\t\t<appattach>\n\t\t\t<totallen>162296</totallen>\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<attachid>@cdn_3057020100044b304902010002048432242302030f5efb02041fc2822b02046463b13e042463656139636538642d386235322d343461612d623935662d3733636665383666323537660204011c00050201000405004c4f2900_4f7fa41874174a15acbc3d7f8fa05f8b_1</attachid>\n\t\t\t<cdnattachurl>3057020100044b304902010002048432242302030f5efb02041fc2822b02046463b13e042463656139636538642d386235322d343461612d623935662d3733636665383666323537660204011c00050201000405004c4f2900</cdnattachurl>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey>4f7fa41874174a15acbc3d7f8fa05f8b</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t\t<filekey>wxid_6ahbbsar7g8i22_24_1684255036</filekey>\n\t\t\t<overwrite_newmsgid>8040463794319609333</overwrite_newmsgid>\n\t\t\t<fileuploadtoken>v1_hiYioN3KXnQwW2GbAiKgX5tQgnLcPxnrbOSQ+4zZnLGCnfJgM3O/e6i7Sx4UOBO5DqQ8/5ln6B0iWHsQj0Cgembxm8AJQ4I4JpkpLzLzabxTMP/BY58lqqNrrb62pNKBeXq0glQGTLG7ogy+KvUSKYPlFa2xNtoyUzyPfYrv1JgYoN8qwWTTSITPAUI5mK9hxzsNytWO8biMPE+gOD/Qmf3oZYCC3kNZ</fileuploadtoken>\n\t\t</appattach>\n\t\t<md5>8003199790abca14b7987fcfd6c5b576</md5>\n\t\t<statextstr>GhQKEnd4MTBlYTY4MTQ1MzY0NjkzMA==</statextstr>\n\t\t<recorditem><![CDATA[(null)]]></recorditem>\n\t\t<uploadpercent>95</uploadpercent>\n\t</appmsg>\n\t<fromusername>daming108792</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>11</version>\n\t\t<appname>新浪邮箱</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n\u0000", "contents": {"md5": "8003199790abca14b7987fcfd6c5b576", "title": "罗明伟的简历.pdf.pdf"}}, {"seq": 1684284668000, "time": "2023-05-17T08:51:08+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到"}, {"seq": 1684284680000, "time": "2023-05-17T08:51:20+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们会尽快给您推荐合适的企业"}, {"seq": 1684284688000, "time": "2023-05-17T08:51:28+08:00", "talker": "daming108792", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[握手][握手][握手]"}]}