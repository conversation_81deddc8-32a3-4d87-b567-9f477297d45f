#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试昵称标签提取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import extract_basic_nickname_tags

def test_nickname_tags():
    """测试联系人备注标签提取"""
    print("🧪 测试联系人备注标签提取功能")
    print("=" * 60)

    # 重点测试您提到的案例
    key_test_case = "21胡艺华英国正教授省青千"
    print(f"\n🎯 重点测试备注: {key_test_case}")
    tags = extract_basic_nickname_tags(key_test_case)
    print(f"✅ 提取标签: {tags}")

    # 验证期望的标签
    expected_tags = ["英国", "教授", "省青千"]
    found_tags = [tag for tag in expected_tags if tag in tags]
    missing_tags = [tag for tag in expected_tags if tag not in tags]

    print(f"\n📊 标签分析:")
    if found_tags:
        print(f"   ✅ 成功识别: {found_tags}")
    if missing_tags:
        print(f"   ❌ 未识别: {missing_tags}")

    # 详细分析每个期望标签
    print(f"\n🔍 详细分析:")
    for expected_tag in expected_tags:
        if expected_tag in tags:
            print(f"   ✅ '{expected_tag}' - 已识别")
        else:
            print(f"   ❌ '{expected_tag}' - 未识别")
            # 检查可能的匹配问题
            if expected_tag == "教授":
                if any("教授" in tag for tag in tags):
                    matching_tags = [tag for tag in tags if "教授" in tag]
                    print(f"      💡 找到相关标签: {matching_tags}")

    print(f"\n" + "=" * 60)

    # 其他测试案例
    other_test_cases = [
        "20彭义杰，哈佛医学院",
        "张教授 清华 长江学者",
        "李博导 省青千 上海交大",
        "王总监 腾讯 深圳",
        "陈研究员 中科院 优青",
        "3236 Java后端开发 北京 5年经验",
        "美国斯坦福博士后",
        "英国剑桥大学教授",
        "澳洲悉尼大学研究员"
    ]

    for remark in other_test_cases:
        print(f"\n📝 测试备注: {remark}")
        tags = extract_basic_nickname_tags(remark)
        print(f"✅ 提取标签: {tags}")

def main():
    """主函数"""
    test_nickname_tags()

if __name__ == "__main__":
    main()
