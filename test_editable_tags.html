<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可编辑标签功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }

        .demo-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .demo-title {
            color: #333;
            margin-bottom: 20px;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
        }

        .demo-section {
            margin-bottom: 30px;
        }

        .demo-section h3 {
            color: #555;
            margin-bottom: 15px;
        }

        .demo-button {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
        }

        /* 复制标签相关样式 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            transition: all 0.2s ease;
        }

        .tag-applied {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .tags-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .tags-header h4 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }

        .tags-controls {
            display: flex;
            gap: 8px;
        }

        .add-tag-btn, .edit-mode-btn {
            background: #2196F3;
            color: white;
            border: none;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .add-tag-btn:hover, .edit-mode-btn:hover {
            background: #1976D2;
            transform: scale(1.1);
        }

        .editable-tag {
            position: relative;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .tag-delete {
            display: none;
            background: rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            text-align: center;
            line-height: 16px;
            transition: all 0.2s ease;
        }

        .tag-delete:hover {
            background: rgba(255, 255, 255, 0.5);
            transform: scale(1.2);
        }

        .edit-mode .tag-delete {
            display: inline-block;
        }

        .edit-mode .editable-tag {
            padding-right: 8px;
        }

        .add-tag-input {
            display: none;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
            padding: 8px;
            background: white;
            border: 2px solid #2196F3;
            border-radius: 6px;
        }

        .add-tag-input input {
            flex: 1;
            border: none;
            outline: none;
            padding: 4px 8px;
            font-size: 14px;
            border-radius: 4px;
            background: #f5f5f5;
        }

        .add-tag-input button {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .add-tag-input button:first-of-type {
            color: #4CAF50;
        }

        .add-tag-input button:first-of-type:hover {
            background: #E8F5E8;
        }

        .add-tag-input button:last-of-type {
            color: #f44336;
        }

        .add-tag-input button:last-of-type:hover {
            background: #FFEBEE;
        }

        .contact-tags {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .feature-list {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .feature-list ul {
            margin: 0;
            padding-left: 20px;
        }

        .feature-list li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🏷️ 可编辑标签功能演示</h1>
        
        <div class="feature-list">
            <h3>✨ 新功能特点：</h3>
            <ul>
                <li>✅ <strong>标签不覆盖</strong> - 新提取的标签会追加到现有标签，不会覆盖</li>
                <li>✅ <strong>手动添加标签</strong> - 点击 ➕ 按钮可以手动添加新标签</li>
                <li>✅ <strong>删除标签</strong> - 在编辑模式下点击 × 可以删除标签</li>
                <li>✅ <strong>编辑模式切换</strong> - 点击 ✏️ 按钮切换编辑/查看模式</li>
                <li>✅ <strong>去重功能</strong> - 自动去除重复的标签</li>
                <li>✅ <strong>输入验证</strong> - 标签长度限制、空值检查</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>🎯 操作演示</h3>
            <button class="demo-button" onclick="simulateExtractedTags()">模拟提取标签</button>
            <button class="demo-button" onclick="simulateMoreTags()">再次提取标签</button>
            <button class="demo-button" onclick="clearAllTags()">清空所有标签</button>
        </div>

        <!-- 模拟联系人标签区域 -->
        <div id="demoContactTags" class="contact-tags" style="display: none;">
            <!-- 标签内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 复制主应用中的标签管理函数
        function updateContactTagsDisplay(newTags) {
            let contactTagsContainer = document.getElementById('demoContactTags');
            contactTagsContainer.style.display = 'block';
            
            // 获取现有标签
            const existingTagsWrapper = contactTagsContainer.querySelector('.tags-container');
            let existingTags = [];
            
            if (existingTagsWrapper) {
                existingTags = Array.from(existingTagsWrapper.children)
                    .map(tag => tag.textContent.trim().replace('×', ''))
                    .filter(tag => tag.length > 0);
            }
            
            // 合并标签，去重
            const allTags = [...new Set([...existingTags, ...newTags])];
            
            // 重新构建标签显示
            contactTagsContainer.innerHTML = `
                <div class="tags-header">
                    <h4>🏷️ 联系人标签</h4>
                    <div class="tags-controls">
                        <button onclick="addNewTag()" class="add-tag-btn" title="添加标签">➕</button>
                        <button onclick="toggleEditMode()" class="edit-mode-btn" title="编辑模式">✏️</button>
                    </div>
                </div>
            `;
            
            // 添加标签容器
            const tagsWrapper = document.createElement('div');
            tagsWrapper.className = 'tags-container';
            tagsWrapper.id = 'contactTagsWrapper';
            
            allTags.forEach(tag => {
                const tagElement = createEditableTag(tag);
                tagsWrapper.appendChild(tagElement);
            });
            
            contactTagsContainer.appendChild(tagsWrapper);
            
            // 添加新标签输入框（默认隐藏）
            const addTagInput = document.createElement('div');
            addTagInput.className = 'add-tag-input';
            addTagInput.id = 'addTagInput';
            addTagInput.style.display = 'none';
            addTagInput.innerHTML = `
                <input type="text" placeholder="输入新标签" maxlength="20" />
                <button onclick="confirmAddTag()">✅</button>
                <button onclick="cancelAddTag()">❌</button>
            `;
            contactTagsContainer.appendChild(addTagInput);
        }

        function createEditableTag(tagText) {
            const tagElement = document.createElement('span');
            tagElement.className = 'tag tag-applied editable-tag';
            tagElement.innerHTML = `
                <span class="tag-text">${tagText}</span>
                <span class="tag-delete" onclick="deleteTag(this)" title="删除标签">×</span>
            `;
            return tagElement;
        }

        function addNewTag() {
            const addTagInput = document.getElementById('addTagInput');
            if (addTagInput) {
                addTagInput.style.display = 'flex';
                const input = addTagInput.querySelector('input');
                input.focus();
                input.value = '';
                
                input.onkeypress = function(e) {
                    if (e.key === 'Enter') {
                        confirmAddTag();
                    } else if (e.key === 'Escape') {
                        cancelAddTag();
                    }
                };
            }
        }

        function confirmAddTag() {
            const addTagInput = document.getElementById('addTagInput');
            const input = addTagInput.querySelector('input');
            const newTagText = input.value.trim();
            
            if (newTagText.length === 0) {
                alert('❌ 标签内容不能为空');
                return;
            }
            
            if (newTagText.length > 20) {
                alert('❌ 标签长度不能超过20个字符');
                return;
            }
            
            // 检查是否已存在
            const existingTags = Array.from(document.querySelectorAll('#contactTagsWrapper .tag-text'))
                .map(tag => tag.textContent.trim());
            
            if (existingTags.includes(newTagText)) {
                alert('❌ 标签已存在');
                return;
            }
            
            // 添加新标签
            const tagsWrapper = document.getElementById('contactTagsWrapper');
            const newTag = createEditableTag(newTagText);
            tagsWrapper.appendChild(newTag);
            
            // 隐藏输入框
            addTagInput.style.display = 'none';
            
            alert(`✅ 已添加标签: ${newTagText}`);
        }

        function cancelAddTag() {
            const addTagInput = document.getElementById('addTagInput');
            addTagInput.style.display = 'none';
        }

        function deleteTag(deleteBtn) {
            const tagElement = deleteBtn.closest('.tag');
            const tagText = tagElement.querySelector('.tag-text').textContent;
            
            if (confirm(`确定要删除标签 "${tagText}" 吗？`)) {
                tagElement.remove();
                alert(`✅ 已删除标签: ${tagText}`);
            }
        }

        function toggleEditMode() {
            const tagsContainer = document.getElementById('contactTagsWrapper');
            const editBtn = document.querySelector('.edit-mode-btn');
            
            if (tagsContainer.classList.contains('edit-mode')) {
                // 退出编辑模式
                tagsContainer.classList.remove('edit-mode');
                editBtn.textContent = '✏️';
                editBtn.title = '编辑模式';
                alert('✅ 已退出编辑模式');
            } else {
                // 进入编辑模式
                tagsContainer.classList.add('edit-mode');
                editBtn.textContent = '👁️';
                editBtn.title = '查看模式';
                alert('✏️ 已进入编辑模式，点击×删除标签');
            }
        }

        // 演示函数
        function simulateExtractedTags() {
            const tags = ['Java', '后端开发', '主动求职', '30-50万', '技术扎实'];
            updateContactTagsDisplay(tags);
        }

        function simulateMoreTags() {
            const moreTags = ['沟通良好', '有潜力', '北京', '5年+经验'];
            updateContactTagsDisplay(moreTags);
        }

        function clearAllTags() {
            const contactTagsContainer = document.getElementById('demoContactTags');
            contactTagsContainer.style.display = 'none';
            contactTagsContainer.innerHTML = '';
        }
    </script>
</body>
</html>
