#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SendIp.exe功能的脚本
"""

import subprocess
import time
import requests
import os
from pathlib import Path

def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get("http://127.0.0.1:8080", timeout=5)
        print("✅ Flask服务器正在运行")
        return True
    except:
        print("❌ Flask服务器未运行，请先启动服务器")
        return False

def test_ip_report_endpoint():
    """测试IP报告端点"""
    try:
        data = {"username": "test_user", "ip": "*************"}
        response = requests.post("http://127.0.0.1:8080/report_ip", json=data, timeout=10)
        
        print(f"📊 IP报告端点测试:")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应: {result}")
                print("✅ IP报告端点正常工作")
                return True
            except:
                print("✅ IP报告端点正常工作 (无JSON响应)")
                return True
        else:
            print(f"❌ IP报告端点返回错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ IP报告端点测试失败: {e}")
        return False

def check_sendip_files():
    """检查SendIp相关文件"""
    files_to_check = [
        "dist/SendIp.exe",
        "dist/config.txt",
        "dist/run_sendip.bat"
    ]
    
    print("📁 检查SendIp文件:")
    all_exist = True
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            print(f"   ✅ {file_path} ({file_size:,} bytes)")
        else:
            print(f"   ❌ {file_path} (不存在)")
            all_exist = False
    
    return all_exist

def update_config_file():
    """更新配置文件"""
    config_path = "dist/config.txt"
    
    config_content = """http://127.0.0.1:8080/report_ip
30
test_computer
"""
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ 配置文件已更新")
        print("   服务器: http://127.0.0.1:8080/report_ip")
        print("   检查间隔: 30秒")
        print("   用户名: test_computer")
        return True
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False

def run_sendip_test():
    """运行SendIp.exe测试"""
    print("\n🚀 启动SendIp.exe测试")
    print("=" * 50)
    
    # 检查exe文件
    exe_path = "dist/SendIp.exe"
    if not Path(exe_path).exists():
        print(f"❌ {exe_path} 不存在")
        return False
    
    try:
        print("📋 启动SendIp.exe...")
        print("注意: 程序将显示GUI窗口，请:")
        print("1. 确认用户名为 'test_computer'")
        print("2. 点击'开始监测'按钮")
        print("3. 观察是否立即发送IP地址")
        print("4. 程序会隐藏到系统托盘")
        
        # 启动SendIp.exe
        process = subprocess.Popen([exe_path], cwd="dist")
        
        print(f"✅ SendIp.exe已启动 (PID: {process.pid})")
        print("💡 请在GUI中点击'开始监测'，然后观察服务器日志")
        print("💡 程序将在后台运行，检查sendip.log文件查看详细日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动SendIp.exe失败: {e}")
        return False

def monitor_logs():
    """监控日志文件"""
    log_file = "dist/sendip.log"
    
    print(f"\n📝 监控日志文件: {log_file}")
    print("=" * 50)
    
    if not Path(log_file).exists():
        print("⚠️ 日志文件尚未创建，等待程序启动...")
        
        # 等待日志文件创建
        for i in range(30):
            if Path(log_file).exists():
                break
            time.sleep(1)
            print(f"   等待中... ({i+1}/30)")
        
        if not Path(log_file).exists():
            print("❌ 日志文件未创建，程序可能未正常启动")
            return False
    
    print("✅ 开始监控日志...")
    
    try:
        # 读取并显示日志内容
        last_size = 0
        
        for i in range(60):  # 监控60秒
            if Path(log_file).exists():
                current_size = Path(log_file).stat().st_size
                
                if current_size > last_size:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        f.seek(last_size)
                        new_content = f.read()
                        if new_content.strip():
                            print(new_content.strip())
                    last_size = current_size
            
            time.sleep(1)
        
        print("\n📊 日志监控完成")
        return True
        
    except Exception as e:
        print(f"❌ 监控日志失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 SendIp.exe 功能测试")
    print("=" * 60)
    
    # 1. 检查服务器状态
    print("\n1️⃣ 检查服务器状态")
    if not check_server_status():
        print("请先启动Flask服务器: python app.py")
        return False
    
    # 2. 测试IP报告端点
    print("\n2️⃣ 测试IP报告端点")
    if not test_ip_report_endpoint():
        print("IP报告端点有问题，请检查服务器代码")
        return False
    
    # 3. 检查SendIp文件
    print("\n3️⃣ 检查SendIp文件")
    if not check_sendip_files():
        print("请先运行: python build_sendip_exe.py")
        return False
    
    # 4. 更新配置文件
    print("\n4️⃣ 更新配置文件")
    if not update_config_file():
        return False
    
    # 5. 运行SendIp测试
    print("\n5️⃣ 运行SendIp测试")
    if not run_sendip_test():
        return False
    
    # 6. 监控日志
    print("\n6️⃣ 监控日志输出")
    monitor_logs()
    
    print("\n🎉 测试完成!")
    print("💡 如果看到IP地址发送成功的日志，说明功能正常")
    print("💡 可以尝试切换网络来测试IP变化检测功能")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
