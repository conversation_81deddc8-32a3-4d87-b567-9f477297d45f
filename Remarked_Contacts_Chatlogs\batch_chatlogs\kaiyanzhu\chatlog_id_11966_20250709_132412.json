{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "11966", "contact_info": {"UserName": "wxid_ak5g6ruqf9k612", "Alias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remark": "11966 聂书芳", "NickName": "shufang "}, "fetch_time": "2025-07-09T13:24:12.998841", "message_count": 297, "chatlog_data": [{"seq": 1678417787940, "time": "2023-03-10T11:09:47+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好！"}, {"seq": 1678417865713, "time": "2023-03-10T11:11:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "政府老师非常关心您，为您匹配了与您专业领域相适的企业，我把企业介绍发给您，您抽空看一下，是否感兴趣[让我看看]"}, {"seq": 1678417904109, "time": "2023-03-10T11:11:44+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "江苏诚信药业有限公司"}, {"seq": 1678417915539, "time": "2023-03-10T11:11:55+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "公司主要从事丙氨酰谷氨酰胺、阿托伐他汀钙、奥拉西坦、吡喹酮、瑞舒伐他汀钙、替比夫定原料药生产、销售；酮还原酶生产、销售；维生素C葡萄糖苷的生产、销售；氨基酸系列产品销售；化工原料中间体、日用化妆品、生物医药产品的研发、销售、技术转让；（除危险化学品、有毒品等国家专项规定的产品）；自营和代理一般经营项目商品和技术的进出口业务。我公司当前各类学士、硕士、博士占全员比重的30%。公司非常注重科技创新和研发投入，与中国医科院药物研究所、中国药科大学建立了长期的产学研合作关系，公司拥有江苏省级生物酶工程技术研究中心，主要进行生物酶及绿色生物产品的开发、应用研究，先后申请专利101件，已授权专利42件，其中授权发明专利8件，获认定省高新技术产品7个。公司自创办以来，始终实施以“人才为本，科技为先，发展为重，共享共赢”的可持续发展战略，并一直以“追求卓越，福及众生”为使命，全力打造一流的诚信品牌，打造受人尊敬的企业。"}, {"seq": 1678417934341, "time": "2023-03-10T11:12:14+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "期待您的回复[让我看看][让我看看][让我看看]"}, {"seq": 1687945875000, "time": "2023-06-28T17:51:15+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "刘博您好，近况如何，根据您的专业领域为您匹配了浙江诚意药业股份有限公司，我把企业信息发您，您看下是否有意向"}, {"seq": 1687945924000, "time": "2023-06-28T17:52:04+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "https://www.chengyipharma.com/"}, {"seq": 1687945968000, "time": "2023-06-28T17:52:48+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您在加入浙江诚意药业股份有限公司后，可以在以下几个研究领域展开工作：\n新型给药系统研究： 您在新型给药系统方面有丰富的经验，可以与企业合作进行新型给药系统的研究。例如，探索基于纳米颗粒的药物传递系统，以提高药物的生物利用度和治疗效果。同时，可以研究开发具有独特药物释放机制的控释系统，以实现药物的持续释放和稳定性。\n产品研发： 您可以参与固体剂型和软胶囊的产品研发工作。通过优化制备工艺、改进配方以及提高产品的生产效率，可以提升公司现有产品的质量和市场竞争力。此外，根据市场需求和趋势，可以共同研发新的药物剂型，以满足患者的需求。\n分析科学研究： 您在分析科学方面有专业知识和经验，可以与企业的质量控制部门合作，开展分析方法的研究和优化。通过引入先进的分析技术和仪器，可以提高产品的质量标准和质量管控体系，并确保产品符合药品监管部门的法规要求。\n创新战略研究： 您在创新战略方面有丰富经验，可以为企业提供战略规划和创新思路。通过深入了解市场需求和竞争动态，可以帮助企业制定创新战略，并加强与学术界和研究机构的合作，以推动企业的科技创新和技术转化。这有助于提升企业的研发能力和技术水平，进一步巩固和扩大市场地位。\n综上所述，您在加入浙江诚意药业股份有限公司后，可以在新型给药系统研究、产品研发、分析科学研究和创新战略研究等领域与企业展开合作。这些合作将有助于提升企业的研发能力和技术水平，推动企业的科技创新和市场竞争力的发展。"}, {"seq": 1687996755000, "time": "2023-06-29T07:59:15+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "你阐述的未来工作计划和我的研究背景很符合 我有兴趣进一步了解 "}, {"seq": 1687996795000, "time": "2023-06-29T07:59:55+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我是聂博士 不是刘博 更正一下"}, {"seq": 1687999418000, "time": "2023-06-29T08:43:38+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "对不起聂博[捂脸]打错了打错了", "contents": {"refer": {"seq": 0, "time": "1970-01-01T08:00:00+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "shufang ", "isSelf": false, "type": 1, "subType": 0, "content": "我是聂博士 不是刘博 更正一下"}}}, {"seq": 1688350670000, "time": "2023-07-03T10:17:50+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好，诚意药业想了解一下您的期望薪资是多少呢？"}, {"seq": 1688430293000, "time": "2023-07-04T08:24:53+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我的想法是薪资期望取决于引进后的岗位职责内容和政府补贴标准，能知道诚意药业的预算范围吗？"}, {"seq": 1688430596000, "time": "2023-07-04T08:29:56+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我更看重我的背景能给企业发展带来什么实质价值和发挥空间，如果双方匹配满意的话，我相信可以配合企业在薪资上达成一致"}, {"seq": 1688432397000, "time": "2023-07-04T08:59:57+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   聂博  "}, {"seq": 1688432480000, "time": "2023-07-04T09:01:20+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博企业正在确认会议时间，您有任何疑问都可以在会议上和企业沟通的"}, {"seq": 1688433617000, "time": "2023-07-04T09:20:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "嗯 第一次会议你有什么建议可以分享吗？"}, {"seq": 1688434196000, "time": "2023-07-04T09:29:56+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "第一次会议的话，主要就是您的一个自我介绍，之后是企业和您的一个自由交流"}, {"seq": 1688434300000, "time": "2023-07-04T09:31:40+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "会议大概多久？30min?"}, {"seq": 1688434322000, "time": "2023-07-04T09:32:02+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "当然聂博也可以和企业沟通   您可以问您想了解的问题  "}, {"seq": 1688434352000, "time": "2023-07-04T09:32:32+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "例如：薪资，技术，未来发展等等"}, {"seq": 1688434381000, "time": "2023-07-04T09:33:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "时间大概是30分钟到一个小时左右", "contents": {"refer": {"seq": 0, "time": "1970-01-01T08:00:00+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "shufang ", "isSelf": false, "type": 1, "subType": 0, "content": "会议大概多久？30min?"}}}, {"seq": 1688434419000, "time": "2023-07-04T09:33:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢"}, {"seq": 1688434530000, "time": "2023-07-04T09:35:30+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   具体什么时间方便会议呢？"}, {"seq": 1688434716000, "time": "2023-07-04T09:38:36+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "有12小时时差 你看企业那边早上9-10点（我这边晚上9-10点）可以吗？ "}, {"seq": 1688434724000, "time": "2023-07-04T09:38:44+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的 "}, {"seq": 1688458363000, "time": "2023-07-04T16:12:43+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好   会议时间定为北京时间7月5日早上9:30可以吗？"}, {"seq": 1688458952000, "time": "2023-07-04T16:22:32+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "小张@海归就业创业(小张@海归就业创业) 邀请你参加会议\n会议主题：11966聂博—诚信药业会议对接\n会议时间：2023/07/05 09:30-10:30\n\n会议链接：https://work.weixin.qq.com/webapp/tm/vqN881UGBM2"}, {"seq": 1688458966000, "time": "2023-07-04T16:22:46+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  这是会议链接   请您查收！"}, {"seq": 1688463924000, "time": "2023-07-04T17:45:24+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1688464183000, "time": "2023-07-04T17:49:43+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "小张@海归就业创业 邀请您参加企业微信会议\n会议主题：11966聂博—诚意药业会议对接\n会议时间：2023/07/05 12:00-13:00 (GMT+08:00) 中国标准时间 - 北京\n\n点击链接入会，或添加至会议列表：\nhttps://work.weixin.qq.com/webapp/tm/vqN881UGBM2\n\n#企业微信会议：977-111-732"}, {"seq": 1688464191000, "time": "2023-07-04T17:49:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  这是会议链接   请您查收！"}, {"seq": 1688474538000, "time": "2023-07-04T20:42:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "你发了两个链接 时间好像不同 请确定哪个链接是对的"}, {"seq": 1688474662000, "time": "2023-07-04T20:44:22+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "另外 这是视频链接吗？是用手机微信吗？还是可以在电脑上操作？我不太熟悉"}, {"seq": 1688475010000, "time": "2023-07-04T20:50:10+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "聂博  以第二个为准", "contents": {"refer": {"seq": 0, "time": "1970-01-01T08:00:00+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "shufang ", "isSelf": false, "type": 1, "subType": 0, "content": "你发了两个链接 时间好像不同 请确定哪个链接是对的"}}}, {"seq": 1688475027000, "time": "2023-07-04T20:50:27+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "腾讯会议   手机可以操作  电脑也可以操作", "contents": {"refer": {"seq": 0, "time": "1970-01-01T08:00:00+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "shufang ", "isSelf": false, "type": 1, "subType": 0, "content": "另外 这是视频链接吗？是用手机微信吗？还是可以在电脑上操作？我不太熟悉"}}}, {"seq": 1688475049000, "time": "2023-07-04T20:50:49+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您在电脑上下载一个腾讯会议就好"}, {"seq": 1688475058000, "time": "2023-07-04T20:50:58+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "手机下载一个腾讯会议也可以使用"}, {"seq": 1688475081000, "time": "2023-07-04T20:51:21+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢 我下载试试 "}, {"seq": 1688475102000, "time": "2023-07-04T20:51:42+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "行 那就确定了"}, {"seq": 1688475122000, "time": "2023-07-04T20:52:02+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没关系   聂博   会议开始前我会提醒您的"}, {"seq": 1688475328000, "time": "2023-07-04T20:55:28+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "今天是国庆假期 我不一定有时间好好准备会议 只能就初步聊聊 那是几个小时后视频见"}, {"seq": 1688475365000, "time": "2023-07-04T20:56:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "十几个小时后见"}, {"seq": 1688517231000, "time": "2023-07-05T08:33:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   聂博  早上好"}, {"seq": 1688517258000, "time": "2023-07-05T08:34:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "你好"}, {"seq": 1688519186000, "time": "2023-07-05T09:06:26+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   您好   我已经在会议中   您可以随时进入会议测试设备"}, {"seq": 1688519465000, "time": "2023-07-05T09:11:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "👌"}, {"seq": 1688519763000, "time": "2023-07-05T09:16:03+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   您有不太了解的  可以电话致电我   "}, {"seq": 1688519768000, "time": "2023-07-05T09:16:08+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1688520101000, "time": "2023-07-05T09:21:41+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "嗯 "}, {"seq": 1688520124000, "time": "2023-07-05T09:22:04+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您现在入会顺利吗？"}, {"seq": 1688523162000, "time": "2023-07-05T10:12:42+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  方便沟通吗？"}, {"seq": 1688525939000, "time": "2023-07-05T10:58:59+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "11"}, {"seq": 1688526154000, "time": "2023-07-05T11:02:34+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   我刚刚和政府老师核实了    您不需要去找对方中介公司  "}, {"seq": 1688559360000, "time": "2023-07-05T20:16:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "好的 我希望和诚意合作申报 那就辛苦你准备材料了 "}, {"seq": 1688559958000, "time": "2023-07-05T20:25:58+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  聂博   "}, {"seq": 1688604294000, "time": "2023-07-06T08:44:54+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<msg><img length=\"29467\" hdlength=\"0\" /><commenturl></commenturl></msg>", "contents": {"md5": ""}}, {"seq": 1688604317000, "time": "2023-07-06T08:45:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，您好  方便电话沟通吗？"}, {"seq": 1688605071000, "time": "2023-07-06T08:57:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，省级项目只能依托一个浙江省的单位申请，后续如果有其他猎头给您推荐单位 您可以直接回复 “我已经有委托的机构帮我在处理相关的申报事宜了“"}, {"seq": 1688605278000, "time": "2023-07-06T09:01:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "<PERSON><PERSON>"}, {"seq": 1688605288000, "time": "2023-07-06T09:01:28+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "现在能通话吗？"}, {"seq": 1688605712000, "time": "2023-07-06T09:08:32+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "11"}, {"seq": 1688605885000, "time": "2023-07-06T09:11:25+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博刚刚接到通知，诚意的任总想和您深入交谈一下，您看明天方便吗？"}, {"seq": 1688605981000, "time": "2023-07-06T09:13:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我这边晚上8点之后都可以 看任总哪个时间方便"}, {"seq": 1688606018000, "time": "2023-07-06T09:13:38+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   "}, {"seq": 1688606046000, "time": "2023-07-06T09:14:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "那就是北京时间的8点", "contents": {"refer": {"seq": 0, "time": "1970-01-01T08:00:00+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "shufang ", "isSelf": false, "type": 1, "subType": 0, "content": "我这边晚上8点之后都可以 看任总哪个时间方便"}}}, {"seq": 1688606151000, "time": "2023-07-06T09:15:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "可以 再确定一下是任总那边7/7号北京时间早上8点 "}, {"seq": 1688606202000, "time": "2023-07-06T09:16:42+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的  "}, {"seq": 1688606202001, "time": "2023-07-06T09:16:40+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "还是视频交流吗？"}, {"seq": 1688606232000, "time": "2023-07-06T09:17:12+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的  会议的形式   但是只有任总沟通  "}, {"seq": 1688606246000, "time": "2023-07-06T09:17:26+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这次的人员不会很多"}, {"seq": 1688606275000, "time": "2023-07-06T09:17:55+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "视频的话 需要麻烦你发个链接"}, {"seq": 1688606297000, "time": "2023-07-06T09:18:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   聂博  稍后给您发送"}, {"seq": 1688606440000, "time": "2023-07-06T09:20:40+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "如果这次深入交流的话 我考虑能否将我的材料提前给他过目一下 会有助于更了解我的研发领域和经验"}, {"seq": 1688606468000, "time": "2023-07-06T09:21:08+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我一会将材料修改发给你"}, {"seq": 1688611941000, "time": "2023-07-06T10:52:21+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的    聂博   "}, {"seq": 1688611982000, "time": "2023-07-06T10:53:02+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "材料给到我们就好，您可以直接和任总交流", "contents": {"refer": {"seq": 0, "time": "1970-01-01T08:00:00+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "shufang ", "isSelf": false, "type": 1, "subType": 0, "content": "如果这次深入交流的话 我考虑能否将我的材料提前给他过目一下 会有助于更了解我的研发领域和经验"}}}, {"seq": 1688613768000, "time": "2023-07-06T11:22:48+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "你的邮箱？"}, {"seq": 1688613841000, "time": "2023-07-06T11:24:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "ka<PERSON><PERSON><PERSON>@email.techtalentsuk.com"}, {"seq": 1688613911000, "time": "2023-07-06T11:25:11+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这个聂博"}, {"seq": 1688614315000, "time": "2023-07-06T11:31:55+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "发了"}, {"seq": 1688614471000, "time": "2023-07-06T11:34:31+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  聂博  "}, {"seq": 1688614474000, "time": "2023-07-06T11:34:34+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "辛苦了  "}, {"seq": 1688614479000, "time": "2023-07-06T11:34:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername = \"wxid_6ahbbsar7g8i22\" tousername = \"wxid_ak5g6ruqf9k612\" type=\"2\" androidmd5=\"763CBA39FF031818E1B269B001E7A462\" androidlen=\"25111\" productid=\"com.tencent.xin.emoticon.person.stiker_1612168687535900a092e32523\"></emoji></msg>"}, {"seq": 1688614781000, "time": "2023-07-06T11:39:41+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "材料很杂 还要辛苦你整理归纳 有问题再联系！"}, {"seq": 1688615019000, "time": "2023-07-06T11:43:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  聂博  "}, {"seq": 1688615029000, "time": "2023-07-06T11:43:49+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您早点休息   晚安"}, {"seq": 1688621945000, "time": "2023-07-06T13:39:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "小张@海归就业创业(小张@海归就业创业) 邀请你参加会议\n会议主题：11966聂博—洞头诚意药业对接会\n会议时间：2023/07/07 09:00-10:00\n会议链接：https://work.weixin.qq.com/webapp/tm/XtqgpuXOOmh\n#会议号：461-216-317\n手机一键拨号入会\n+8675536550000,,461216317 (中国大陆)\n+85230018898,,,2,461216317 (中国香港)\n根据您的位置拨号\n+8675536550000 (中国大陆)\n+85230018898 (中国香港)"}, {"seq": 1688621969000, "time": "2023-07-06T13:39:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   会议链接请查收   感谢~"}, {"seq": 1688629548000, "time": "2023-07-06T15:45:48+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，材料我们已经研读了，您还能找到在沈阳药科大学担任副教授的一些证明吗？二是项目方面，您能不能提供几项项目的文字类证明呢？"}, {"seq": 1688685566000, "time": "2023-07-07T07:19:26+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "副教授证明发给你了 但项目证明无法提供 "}, {"seq": 1688689946000, "time": "2023-07-07T08:32:26+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   聂博"}, {"seq": 1688695980000, "time": "2023-07-07T10:13:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  您好   您现在累吗？"}, {"seq": 1688695990000, "time": "2023-07-07T10:13:10+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "方便电话沟通吗？"}, {"seq": 1688696019000, "time": "2023-07-07T10:13:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "可以"}, {"seq": 1688696815000, "time": "2023-07-07T10:26:55+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "11"}, {"seq": 1688696898000, "time": "2023-07-07T10:28:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "https://rcgj.zj.gov.cn/foreignLogin"}, {"seq": 1688696910000, "time": "2023-07-07T10:28:30+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<msg><img length=\"876039\" hdlength=\"0\" /><commenturl></commenturl></msg>", "contents": {"md5": ""}}, {"seq": 1688696915000, "time": "2023-07-07T10:28:35+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<msg><img length=\"738656\" hdlength=\"0\" /><commenturl></commenturl></msg>", "contents": {"md5": ""}}, {"seq": 1688696931000, "time": "2023-07-07T10:28:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这样的"}, {"seq": 1688696941000, "time": "2023-07-07T10:29:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您看链接 您可以进入吗？"}, {"seq": 1688697078000, "time": "2023-07-07T10:31:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "链接可以打开 明天注册好了通知你"}, {"seq": 1688697770000, "time": "2023-07-07T10:42:50+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  聂博  "}, {"seq": 1688737690000, "time": "2023-07-07T21:48:10+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "系统显示我的邮箱**********************不存在 ，没法重设密码"}, {"seq": 1688760037000, "time": "2023-07-08T04:00:37+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  聂博   我这边询问一下   是否需要重新注册账号  "}, {"seq": 1688950189000, "time": "2023-07-10T08:49:49+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  您好   您这边重新注册一下账户  "}, {"seq": 1688950200000, "time": "2023-07-10T08:50:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您现在方便电话沟通吗？"}, {"seq": 1688959452000, "time": "2023-07-10T11:24:12+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好，您需要帮助吗？"}, {"seq": 1688987145000, "time": "2023-07-10T19:05:45+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我今天注册一下"}, {"seq": 1688988006000, "time": "2023-07-10T19:20:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  聂博"}, {"seq": 1689019686000, "time": "2023-07-11T04:08:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"e53c958b496158f7deeadf8e23c0a1bb\" encryver=\"1\" cdnthumbaeskey=\"e53c958b496158f7deeadf8e23c0a1bb\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464ac6526042463323232613135392d363061652d343663652d616536302d6236666233666338616238320204011c18020201000405004c537500\" cdnthumblength=\"2811\" cdnthumbheight=\"120\" cdnthumbwidth=\"71\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464ac6526042463323232613135392d363061652d343663652d616536302d6236666233666338616238320204011c18020201000405004c537500\" length=\"423949\" md5=\"4196f46fdd5a8d83be11669a99b8f403\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "4196f46fdd5a8d83be11669a99b8f403"}}, {"seq": 1689019704000, "time": "2023-07-11T04:08:24+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"8f32a2bb9d3bbd057e6f254aef433d77\" encryver=\"1\" cdnthumbaeskey=\"8f32a2bb9d3bbd057e6f254aef433d77\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464ac6537042430633838323662652d633733302d343365372d386233612d6230616630633131373734300204011c18020201000405004c4f2900\" cdnthumblength=\"3003\" cdnthumbheight=\"120\" cdnthumbwidth=\"72\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464ac6537042430633838323662652d633733302d343365372d386233612d6230616630633131373734300204011c18020201000405004c4f2900\" length=\"390297\" md5=\"ecdb448cbc057c0bb40f42d7e7be7d0e\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "ecdb448cbc057c0bb40f42d7e7be7d0e"}}, {"seq": 1689019745000, "time": "2023-07-11T04:09:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "好像不能重新注册 我的护照号已被注册"}, {"seq": 1689019804000, "time": "2023-07-11T04:10:04+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "又重新试着改密码 还是不行 提示邮箱不存在"}, {"seq": 1689019878000, "time": "2023-07-11T04:11:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我猜有可能是 去年中介用我的护照号和另一个邮箱替我注册过了 但用的不是我的个人邮箱"}, {"seq": 1689036141000, "time": "2023-07-11T08:42:21+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "11"}, {"seq": 1689036240000, "time": "2023-07-11T08:44:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"df0f2dc8e01dffd6e0ceac551495ebd0\" encryver=\"1\" cdnthumbaeskey=\"df0f2dc8e01dffd6e0ceac551495ebd0\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020414c2822b020464aca5d0042435313131613066662d316539652d346433322d616666312d3139373464316434653737630204011c18020201000405004c54a100\" cdnthumblength=\"2503\" cdnthumbheight=\"120\" cdnthumbwidth=\"80\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020414c2822b020464aca5d0042435313131613066662d316539652d346433322d616666312d3139373464316434653737630204011c18020201000405004c54a100\" length=\"378672\" md5=\"883ca40cdaa18054013399ac9f60c45c\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "883ca40cdaa18054013399ac9f60c45c"}}, {"seq": 1689036271000, "time": "2023-07-11T08:44:31+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我没看到有身份证注册的选项"}, {"seq": 1689036511000, "time": "2023-07-11T08:48:31+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  我们解决一下   "}, {"seq": 1689036544000, "time": "2023-07-11T08:49:04+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢"}, {"seq": 1689036553000, "time": "2023-07-11T08:49:13+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没关系   聂博 "}, {"seq": 1689571691000, "time": "2023-07-17T13:28:11+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 6, "content": "<msg>\n    <fromusername>wxid_6ahbbsar7g8i22</fromusername>\n    <scene>0</scene>\n    <commenturl></commenturl>\n    <appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n        <title>个人承诺书.docx</title>\n        <des></des>\n        <action>view</action>\n        <type>6</type>\n        <showtype>0</showtype>\n        <content></content>\n        <url></url>\n        <dataurl></dataurl>\n        <lowurl></lowurl>\n        <lowdataurl></lowdataurl>\n        <recorditem></recorditem>\n        <thumburl></thumburl>\n        <messageaction></messageaction>\n        <laninfo></laninfo>\n        <md5>61aa4064c90fe85dcb0ea4129da907e1</md5>\n        <extinfo></extinfo>\n        <sourceusername></sourceusername>\n        <sourcedisplayname></sourcedisplayname>\n        <commenturl></commenturl>\n        <appattach>\n            <totallen>12642</totallen>\n            <attachid>@cdn_3057020100044b30490201000204ff23008602032df5a10204923e2a3a020464b4d16b042434303863656232352d623534642d343062392d613136642d3139343865373738373930310204011400050201000405004c56f900_bcf6635c97fc2a7d9371db92bb97d2e2_1</attachid>\n            <emoticonmd5></emoticonmd5>\n            <fileext>docx</fileext>\n            <fileuploadtoken>v1_qCvDHmnW5YabqtR3olzP5Jx17rlC7t1wb1j2GSzGBmauiapi3QfB/n4nHr4+xJEURybBlC8UaaaOAM67UXUSYOOq6HOrEN/3CKLsjklI1aPZEORx1jKtWgSfLI26DzMpOaH64sylPY/lldS62TGvrVrE25kvgEK5N7k0xGPhRTEPgRo2HX3pet81pkA9wKd5gjlEvjqXDB2/gojiTq1pLuFQuQrRYxdlhW3/a9CzVa6JllE=</fileuploadtoken>\n            <overwrite_newmsgid>6980315186989158214</overwrite_newmsgid>\n            <filekey>913a8a66b512ea8af92c31acc2ba397c</filekey>\n            <cdnattachurl>3057020100044b30490201000204ff23008602032df5a10204923e2a3a020464b4d16b042434303863656232352d623534642d343062392d613136642d3139343865373738373930310204011400050201000405004c56f900</cdnattachurl>\n            <aeskey>bcf6635c97fc2a7d9371db92bb97d2e2</aeskey>\n            <encryver>1</encryver>\n        </appattach>\n        <weappinfo>\n            <pagepath></pagepath>\n            <username></username>\n            <appid></appid>\n            <appservicetype>0</appservicetype>\n        </weappinfo>\n        <websearch />\n    </appmsg>\n    <appinfo>\n        <version>1</version>\n        <appname>Window wechat</appname>\n    </appinfo>\n</msg>\n\u0000", "contents": {"md5": "61aa4064c90fe85dcb0ea4129da907e1", "title": "个人承诺书.docx"}}, {"seq": 1689571780000, "time": "2023-07-17T13:29:40+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  您好   以上文件是您的个人承诺书，辛苦您签字回传一下，非常感谢"}, {"seq": 1689642737000, "time": "2023-07-18T09:12:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"5aaec05c8be1a6a54e9040a2af95c01e\" encryver=\"1\" cdnthumbaeskey=\"5aaec05c8be1a6a54e9040a2af95c01e\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464b5e6f0042461663330353437372d333935652d343034332d623432302d3438666135316466373730350204011c18020201000405004c543d00\" cdnthumblength=\"3526\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464b5e6f0042461663330353437372d333935652d343034332d623432302d3438666135316466373730350204011c18020201000405004c543d00\" length=\"254990\" md5=\"7842df589a221a7b90560fbc0d165896\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "7842df589a221a7b90560fbc0d165896"}}, {"seq": 1689642766000, "time": "2023-07-18T09:12:46+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  聂博   收到了   "}, {"seq": 1689642814000, "time": "2023-07-18T09:13:34+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "辛苦您了  "}, {"seq": 1689642865000, "time": "2023-07-18T09:14:25+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "这只是个意向书吧 "}, {"seq": 1689642909000, "time": "2023-07-18T09:15:09+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的  "}, {"seq": 1689642917000, "time": "2023-07-18T09:15:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "嗯"}, {"seq": 1689642930000, "time": "2023-07-18T09:15:30+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   这个只是一个意向协议，仅限于申报系统需要，具体的职位什么的是以最终申报之后拟定的合同为准"}, {"seq": 1689642951000, "time": "2023-07-18T09:15:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢"}, {"seq": 1689642976000, "time": "2023-07-18T09:16:16+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没关系  "}, {"seq": 1689645836000, "time": "2023-07-18T10:03:56+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好！您的申报进度已成功注册申报系统账号和密码，因系统需要做到信息安全保密所以登录有限制的，这是由相关负责的工作人员专人处理，请知悉省级有系统注册账号和密码事宜[玫瑰][玫瑰]"}, {"seq": 1689645907000, "time": "2023-07-18T10:05:07+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "好的👌"}, {"seq": 1689645925000, "time": "2023-07-18T10:05:25+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "辛苦您了"}, {"seq": 1689645929000, "time": "2023-07-18T10:05:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername = \"wxid_6ahbbsar7g8i22\" tousername = \"wxid_ak5g6ruqf9k612\" type=\"2\" androidmd5=\"46051a3e7760d5ff2dcf261ff357458e\" androidlen=\"16880\" aeskey=\"0b313942a67b551d4a94c732048e471f\" encrypturl=\"http://wxapp.tc.qq.com/262/20304/stodownload?m=3fe55b8fdb889af0d3649c53292e29e3&filekey=30340201010420301e020201060402535a04103fe55b8fdb889af0d3649c53292e29e302027760040d00000004627466730000000131&hy=SZ&storeid=32303232303430343039313934313030303531356537336131343232643130653331383830393030303030313036&bizid=1023\" externurl=\"http://wxapp.tc.qq.com/262/20304/stodownload?m=159a0f8b5085772dc37447632831901e&filekey=30340201010420301e020201060402535a0410159a0f8b5085772dc37447632831901e02024200040d00000004627466730000000131&hy=SZ&storeid=32303232303430343039313934313030303763646462336131343232643161353536383830393030303030313036&bizid=1023\" externmd5=\"0a5fb904366de742a1fe72f3d690904d\"></emoji></msg>"}, {"seq": 1691378856000, "time": "2023-08-07T11:27:36+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 6, "content": "<msg>\n    <fromusername>wxid_6ahbbsar7g8i22</fromusername>\n    <scene>0</scene>\n    <commenturl></commenturl>\n    <appmsg appid=\"\" sdkver=\"0\">\n        <title>意向协议（中英文）(2).docx</title>\n        <des></des>\n        <action>view</action>\n        <type>6</type>\n        <showtype>0</showtype>\n        <content></content>\n        <url></url>\n        <dataurl></dataurl>\n        <lowurl></lowurl>\n        <lowdataurl></lowdataurl>\n        <recorditem></recorditem>\n        <thumburl></thumburl>\n        <messageaction></messageaction>\n        <laninfo></laninfo>\n        <md5>3d2600e1510eef012eb1589d1b1d30c1</md5>\n        <extinfo></extinfo>\n        <sourceusername></sourceusername>\n        <sourcedisplayname></sourcedisplayname>\n        <commenturl></commenturl>\n        <appattach>\n            <totallen>19802</totallen>\n            <attachid>@cdn_3057020100044b30490201000204ff23008602032df7950204785d06af020464d064a8042465616365376562652d356562302d343664622d626536302d6339326636326463386437360204011400050201000405004c4dfd00_346a6c4d4113908bee4058757c3649b6_1</attachid>\n            <emoticonmd5></emoticonmd5>\n            <fileext>docx</fileext>\n            <fileuploadtoken>v1_g+/G/8NtRLAxrNByK6eL2Qv7n/8ffSr9Gt8zXYf7TegqwQdY+EeTMqf3FHn6suruY2ETxcsvsk6C3aQgjjtViUHd+N69csJxLIU4y1z0iDKfqI8kyZG5SOQbXE6z/E6b/pRMwW082lY96tPM+vdvJdx0R96wCPvddU1rWAEJqWlU6izwBlWqNHdU2mwF/+8b4lGQmRJwrfUrX6EHFCsNliGLuHG1RJkW0Fapwr1C8LEW8qnxJlyntjWF9CDxvwuDrM2Kjn4=</fileuploadtoken>\n            <overwrite_newmsgid>7813218510338209828</overwrite_newmsgid>\n            <filekey>27c978b5ecac005319387682e8390d41</filekey>\n            <cdnattachurl>3057020100044b30490201000204ff23008602032df7950204785d06af020464d064a8042465616365376562652d356562302d343664622d626536302d6339326636326463386437360204011400050201000405004c4dfd00</cdnattachurl>\n            <aeskey>346a6c4d4113908bee4058757c3649b6</aeskey>\n            <encryver>1</encryver>\n        </appattach>\n        <weappinfo>\n            <pagepath></pagepath>\n            <username></username>\n            <appid></appid>\n            <appservicetype>0</appservicetype>\n        </weappinfo>\n        <websearch />\n    </appmsg>\n    <appinfo>\n        <version>1</version>\n        <appname>Window wechat</appname>\n    </appinfo>\n</msg>\n\u0000", "contents": {"md5": "3d2600e1510eef012eb1589d1b1d30c1", "title": "意向协议（中英文）(2).docx"}}, {"seq": 1691378908000, "time": "2023-08-07T11:28:28+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好，这份文件需要麻烦您签字回传一下，仅为意向协议，仅限于申报系统需要，最终的合同以最终申报成功之后拟定的合同为准"}, {"seq": 1691445908000, "time": "2023-08-08T06:05:08+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "收到 我明天发给你"}, {"seq": 1691454785000, "time": "2023-08-08T08:33:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的  谢谢单博"}, {"seq": 1691454792000, "time": "2023-08-08T08:33:12+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[抱拳]"}, {"seq": 1691479932000, "time": "2023-08-08T15:32:12+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "抱歉   聂博   打错字了   ", "contents": {"refer": {"seq": 0, "time": "1970-01-01T08:00:00+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "<PERSON><PERSON> Zhu@海归就业创业", "isSelf": false, "type": 1, "subType": 0, "content": "好的  谢谢单博"}}}, {"seq": 1691541759000, "time": "2023-08-09T08:42:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"221a6c231601edceb3d99fbe6895bb8b\" encryver=\"1\" cdnthumbaeskey=\"221a6c231601edceb3d99fbe6895bb8b\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020414c2822b020464d2e0ff042431656338323134652d383465332d343266302d626233632d3736626561393137333134630204011c18020201000405004c54a100\" cdnthumblength=\"2530\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020414c2822b020464d2e0ff042431656338323134652d383465332d343266302d626233632d3736626561393137333134630204011c18020201000405004c54a100\" length=\"228478\" md5=\"f0c2adf43cea52b99ee81f0868718790\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "f0c2adf43cea52b99ee81f0868718790"}}, {"seq": 1691541778000, "time": "2023-08-09T08:42:58+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   收到   谢谢聂博    "}, {"seq": 1691541786000, "time": "2023-08-09T08:43:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername = \"wxid_6ahbbsar7g8i22\" tousername = \"wxid_ak5g6ruqf9k612\" type=\"2\" androidmd5=\"753191348a9f21a208f5cc0f43edf768\" androidlen=\"23574\" aeskey=\"ff46fcb90046a4b7ff9ff080211cf2e3\" encrypturl=\"http://wxapp.tc.qq.com/262/20304/stodownload?m=d765b638134e534abec84230b682f407&filekey=30350201010421301f02020106040253480410d765b638134e534abec84230b682f4070203008770040d00000004627466730000000132&hy=SH&storeid=26396e1eb00053c90b4590a760000010600004f50534813c128e0b73373e0f&bizid=1023\" externurl=\"http://wxapp.tc.qq.com/262/20304/stodownload?m=88f608f486cd7e5ce9727a9b1d26c6f7&filekey=30340201010420301e020201060402535a041088f608f486cd7e5ce9727a9b1d26c6f702025c20040d00000004627466730000000132&hy=SZ&storeid=26396edd00002fe3f6b8bfa760000010600004f50535a17c8c970b7cf94b8d&bizid=1023\" externmd5=\"6d64b3b42faf18bb526a5cd7679ec625\"></emoji></msg>"}, {"seq": 1691541889000, "time": "2023-08-09T08:44:49+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我就把签字的一页发给你 好的"}, {"seq": 1691541941000, "time": "2023-08-09T08:45:41+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "嗯  好的  聂博  [抱拳]"}, {"seq": 1691552631000, "time": "2023-08-09T11:43:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   其它页也需要您拍一下回传   辛苦您了   "}, {"seq": 1691552645000, "time": "2023-08-09T11:44:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[抱拳]"}, {"seq": 1691626560000, "time": "2023-08-10T08:16:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"1e82b22d8aafe52eb2c1d92656afd253\" encryver=\"1\" cdnthumbaeskey=\"1e82b22d8aafe52eb2c1d92656afd253\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c3e042432666233663736362d653266342d346230652d383034342d3130353162643234306538620204011c18020201000405004c4dfd00\" cdnthumblength=\"2765\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c3e042432666233663736362d653266342d346230652d383034342d3130353162643234306538620204011c18020201000405004c4dfd00\" length=\"211892\" md5=\"067ef5ea020876b258ff8150f122360d\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "067ef5ea020876b258ff8150f122360d"}}, {"seq": 1691626562000, "time": "2023-08-10T08:16:02+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"d68614b08165c6e6876c9529a72883b6\" encryver=\"1\" cdnthumbaeskey=\"d68614b08165c6e6876c9529a72883b6\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c41042465643366636564302d346139652d346537372d393835382d6637323137386434663236370204011c18020201000405004c4ec500\" cdnthumblength=\"2694\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c41042465643366636564302d346139652d346537372d393835382d6637323137386434663236370204011c18020201000405004c4ec500\" length=\"230777\" md5=\"48ecc6dc2ad96dac9d3d8f80774e80a1\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "48ecc6dc2ad96dac9d3d8f80774e80a1"}}, {"seq": 1691626564000, "time": "2023-08-10T08:16:04+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"defabee714be65406f6c27194b4aad4f\" encryver=\"1\" cdnthumbaeskey=\"defabee714be65406f6c27194b4aad4f\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c43042466643830643164622d623933322d346662662d613530302d6236646333326136383465650204011c18020201000405004c4c6d00\" cdnthumblength=\"3006\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c43042466643830643164622d623933322d346662662d613530302d6236646333326136383465650204011c18020201000405004c4c6d00\" length=\"257318\" md5=\"768b2f3168e6657b54174e1d4bdb089c\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "768b2f3168e6657b54174e1d4bdb089c"}}, {"seq": 1691626566000, "time": "2023-08-10T08:16:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"90f303d2f6d914610323eea316f57536\" encryver=\"1\" cdnthumbaeskey=\"90f303d2f6d914610323eea316f57536\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c44042432333539613536632d376662612d346664662d386330642d3461313235353163343933320204011c18020201000405004c54a100\" cdnthumblength=\"2793\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020424c2822b020464d42c44042432333539613536632d376662612d346664662d386330642d3461313235353163343933320204011c18020201000405004c54a100\" length=\"222177\" md5=\"bb26547e2a62bf0c2d2576bd1eeb3a0c\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "bb26547e2a62bf0c2d2576bd1eeb3a0c"}}, {"seq": 1691627369000, "time": "2023-08-10T08:29:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到    谢谢聂博"}, {"seq": 1691745092000, "time": "2023-08-11T17:11:32+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博 您好，您在高乐氏的离职时间是什么时候呢？是否方便提供离职证明呢？"}, {"seq": 1691745120000, "time": "2023-08-11T17:12:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "如果没有最后月份的工资单也是可以的  "}, {"seq": 1692058152000, "time": "2023-08-15T08:09:12+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"d603d193eadb7dd07df82c1957a12327\" encryver=\"1\" cdnthumbaeskey=\"d603d193eadb7dd07df82c1957a12327\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464dac227042437373663666535392d386666612d343735652d626461622d6131656465326565336235340204011c18020201000405004c4ec500\" cdnthumblength=\"3535\" cdnthumbheight=\"120\" cdnthumbwidth=\"91\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020427c2822b020464dac227042437373663666535392d386666612d343735652d626461622d6131656465326565336235340204011c18020201000405004c4ec500\" length=\"340821\" md5=\"0358ab9a8a582572eea5677968c80500\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "0358ab9a8a582572eea5677968c80500"}}, {"seq": 1692058159000, "time": "2023-08-15T08:09:19+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "只能找到工资单 希望证明有用"}, {"seq": 1692060035000, "time": "2023-08-15T08:40:35+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的    谢谢聂博   "}, {"seq": 1692795016000, "time": "2023-08-23T20:50:16+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好，申报的话还需要您的身份证，您看方便提供一下吗？"}, {"seq": 1692795033000, "time": "2023-08-23T20:50:33+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "还有就是聂博您博士读了5年，是硕博连读吗还是直博呢？"}, {"seq": 1692812747000, "time": "2023-08-24T01:45:47+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"6c78672778c61adff0e7d63607ad0d14\" encryver=\"1\" cdnthumbaeskey=\"6c78672778c61adff0e7d63607ad0d14\" cdnthumburl=\"3057020100044b3049020100020464ab305002030f5efb020414c2822b020464e645ca042435353464386164612d383563642d343631652d623734372d6362386239383937393134340204011c18020201000405004c4c6d00\" cdnthumblength=\"3061\" cdnthumbheight=\"120\" cdnthumbwidth=\"67\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002030f5efb020414c2822b020464e645ca042435353464386164612d383563642d343631652d623734372d6362386239383937393134340204011c18020201000405004c4c6d00\" length=\"685061\" md5=\"23c7d74a9619b4faadedab273cfaa83c\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "23c7d74a9619b4faadedab273cfaa83c"}}, {"seq": 1692812766000, "time": "2023-08-24T01:46:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "硕博连读"}, {"seq": 1692812833000, "time": "2023-08-24T01:47:13+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "但我没有硕士学位 那应该算直博吧"}, {"seq": 1692835300000, "time": "2023-08-24T08:01:40+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   谢谢聂博[抱拳][爱心]"}, {"seq": 1697530093000, "time": "2023-10-17T16:08:13+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好   现在需要您补充一下材料    需要一份您的层级材料  "}, {"seq": 1697530180000, "time": "2023-10-17T16:09:40+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"eeb920608c54641bdcb473aa66967c98\" encryver=\"1\" cdnthumbaeskey=\"eeb920608c54641bdcb473aa66967c98\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f5bd502049f621f740204652e4145042465393032393139392d353663392d343034352d613637322d3233393666386138623362630204011818020201000405004c543d00\" cdnthumblength=\"12769\" cdnthumbheight=\"113\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f5bd502049f621f740204652e4145042465393032393139392d353663392d343034352d613637322d3233393666386138623362630204011818020201000405004c543d00\" length=\"343557\" md5=\"3d568bef04c5ba99126f8664c4e14da2\" hevc_mid_size=\"0\" originsourcemd5=\"3d568bef04c5ba99126f8664c4e14da2\"/></msg>", "contents": {"md5": "3d568bef04c5ba99126f8664c4e14da2"}}, {"seq": 1697530201000, "time": "2023-10-17T16:10:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "类似于这样的一个层级证明材料  "}, {"seq": 1697544489000, "time": "2023-10-17T20:08:09+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "是现在工作单位的吗？"}, {"seq": 1697553964000, "time": "2023-10-17T22:46:04+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的  聂博"}, {"seq": 1697583901000, "time": "2023-10-18T07:05:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "好的 这两天发给你 目前申请有何进展吗？"}, {"seq": 1697590370000, "time": "2023-10-18T08:52:50+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   现在就需要您补充一下  佐证材料   "}, {"seq": 1697619005000, "time": "2023-10-18T16:50:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "聂博，材料已经通过市里的审核了，现在在等省里的审核", "contents": {"refer": {"seq": 0, "time": "2023-10-18T07:05:01+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "shufang ", "isSelf": false, "type": 1, "subType": 0, "content": "好的 这两天发给你 目前申请有何进展吗？"}}}, {"seq": 1697620433000, "time": "2023-10-18T17:13:53+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  层级证明的话  麻烦您尽快给一下哦   因为市里面在催材料[苦涩]"}, {"seq": 1697620441000, "time": "2023-10-18T17:14:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername=\"wxid_6ahbbsar7g8i22\" tousername=\"wxid_ak5g6ruqf9k612\" type=\"2\" androidmd5=\"bb9ac3c457835d1ce0dd6c7af84bd1b9\" androidlen=\"13391\" aeskey=\"0ee85387b08343bd8080159d5978d0a2\" encrypturl=\"http://vweixinf.tc.qq.com/110/20402/stodownload?m=4c8c46348cdfb189764f0eff6de94089&amp;filekey=3043020101042f302d02016e040253480420346338633436333438636466623138393736346630656666366465393430383902025ad0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130303431343237343230303063353535323036366165353364303863313631303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022\" externurl=\"http://vweixinf.tc.qq.com/110/20403/stodownload?m=16df567e697e7d80a0309f58e1520f21&amp;filekey=3043020101042f302d02016e040253480420313664663536376536393765376438306130333039663538653135323066323102023450040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130303431343237343230303064316566613036366165353364303863313631303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022\" externmd5=\"e5c4cb07317f1542380fe2037799fc87\"/></msg>"}, {"seq": 1697689651000, "time": "2023-10-19T12:27:31+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"2b373c4509da161e67d2ef6dc1fb9e7d\" encryver=\"1\" cdnthumbaeskey=\"2b373c4509da161e67d2ef6dc1fb9e7d\" cdnthumburl=\"3052020100044b3049020100020464ab305002033d09680204728b206502046530b032042435663765303463312d653962342d346366362d626434612d3738366138323635356134390204011c18020201000400\" cdnthumblength=\"2651\" cdnthumbheight=\"67\" cdnthumbwidth=\"120\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3052020100044b3049020100020464ab305002033d09680204728b206502046530b032042435663765303463312d653962342d346366362d626434612d3738366138323635356134390204011c18020201000400\" length=\"127351\" md5=\"ec107b0634afcb5713c7ce7064ed98ce\" originsourcemd5=\"bc8b255ebafb6516e53702a825fa2889\" />\n\t<platform_signature></platform_signature>\n\t<imgdatahash></imgdatahash>\n</msg>\n", "contents": {"md5": "ec107b0634afcb5713c7ce7064ed98ce"}}, {"seq": 1697691847000, "time": "2023-10-19T13:04:07+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   聂博  已收到  "}, {"seq": 1697691852000, "time": "2023-10-19T13:04:12+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[社会社会]"}, {"seq": 1697762314000, "time": "2023-10-20T08:38:34+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博    您方便提供一下高乐氏的层级证明吗？您作为诚意药业的重点人才   政府全力完善您的材料   "}, {"seq": 1697775562000, "time": "2023-10-20T12:19:22+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我已离职 无法进入原单位系统调出结构图"}, {"seq": 1697778519000, "time": "2023-10-20T13:08:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   您可以自己绘制一份吗？"}, {"seq": 1697778526000, "time": "2023-10-20T13:08:46+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们来完善   "}, {"seq": 1697808187000, "time": "2023-10-20T21:23:07+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "之前公司很大 人员也变动 记不住了"}, {"seq": 1697808297000, "time": "2023-10-20T21:24:57+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "一般国外公司的结构图对外是不公开的 员工只会在公司内部系统查询 "}, {"seq": 1698023107000, "time": "2023-10-23T09:05:07+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   聂博   我清楚了"}, {"seq": 1698024073000, "time": "2023-10-23T09:21:13+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好，洞头政府的郑老师和您沟通一下，您看可以拉一个群聊吗？"}, {"seq": 1698029166000, "time": "2023-10-23T10:46:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   需要您录制一个唯一申报视频作为佐证，内容：本人聂淑芳，在此明确，在2023年的浙江省级引才计划中，唯一依托申报单位为位于温州市洞头区的浙江诚意药业股份有限公司，未在任何其他地方进行申报。任何除洞头区以外的其他地方的申报，均属不实申报。2023年10月23日。"}, {"seq": 1698029226000, "time": "2023-10-23T10:47:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "视频中还需要出现您的护照   "}, {"seq": 1698029319000, "time": "2023-10-23T10:48:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "目前我在出差 护照不在身边 最早下周三发给你"}, {"seq": 1698029581000, "time": "2023-10-23T10:53:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博  我们和郑老师  沟通好了   您现在在外出差  情况特殊   您直接录视频就好   不需要出现护照了   "}, {"seq": 1698030416001, "time": "2023-10-23T11:06:56+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[已在其它设备接听]]></msg>\n<room_type>0</room_type>\n<red_dot>false</red_dot>\n<roomid>613454230</roomid>\n<roomkey>6275762063579301680</roomkey>\n<inviteid>0</inviteid>\n<msg_type>101</msg_type>\n<timestamp>1698030416525</timestamp>\n<identity><![CDATA[7433461682116179087]]></identity>\n<duration>0</duration>\n<inviteid64>0</inviteid64>\n<business>0</business>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1698030416003, "time": "2023-10-23T11:06:56+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[对方已取消]]></msg>\n<room_type>0</room_type>\n<red_dot>true</red_dot>\n<roomid>613454230</roomid>\n<roomkey>6275762063579301680</roomkey>\n<inviteid>1698030366</inviteid>\n<msg_type>100</msg_type>\n<timestamp>1698030416753</timestamp>\n<identity><![CDATA[5363730288340529359]]></identity>\n<duration>0</duration>\n<inviteid64>1698030366</inviteid64>\n<business>0</business>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1698030459001, "time": "2023-10-23T11:07:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[对方已拒绝]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>211102301</roomid>\n<roomkey>8320545325704961803</roomkey>\n<inviteid>0</inviteid>\n<msg_type>100</msg_type>\n<timestamp>1698030459780</timestamp>\n<identity><![CDATA[5321232514842527206]]></identity>\n<duration>0</duration>\n<inviteid64>0</inviteid64>\n<business>0</business>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1698030496001, "time": "2023-10-23T11:08:16+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[已在其它设备接听]]></msg>\n<room_type>0</room_type>\n<red_dot>false</red_dot>\n<roomid>613767788</roomid>\n<roomkey>8323583978912803162</roomkey>\n<inviteid>0</inviteid>\n<msg_type>101</msg_type>\n<timestamp>1698030496388</timestamp>\n<identity><![CDATA[5232819697151623886]]></identity>\n<duration>0</duration>\n<inviteid64>0</inviteid64>\n<business>0</business>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1698714188000, "time": "2023-10-31T09:03:08+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好    洞头政府郑老师要和您沟通一下   您现在方便吗？"}, {"seq": 1698714937000, "time": "2023-10-31T09:15:37+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "语音通话吗？"}, {"seq": 1698715105000, "time": "2023-10-31T09:18:25+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的  "}, {"seq": 1698715115000, "time": "2023-10-31T09:18:35+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "可以"}, {"seq": 1698715130000, "time": "2023-10-31T09:18:50+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   谢谢聂博  "}, {"seq": 1698717955000, "time": "2023-10-31T10:05:55+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博    郑老师是通过微信群聊给您发的语音通话  "}, {"seq": 1698717977000, "time": "2023-10-31T10:06:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您现在方便聊一下吗？"}, {"seq": 1698983309000, "time": "2023-11-03T11:48:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博士   辛苦您通过一下郑老师微信    老师正在协调处理您被其他机构盗用材料的问题   "}, {"seq": 1704422593000, "time": "2024-01-05T10:43:13+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好   2024年国家级人才项目已开始 我们可以继续给您服务 为您匹配单位 请问您这边有意向吗  这次申请对您来说是简单的哦  因为我们有您的申报资料 到时候政府老师直接给您提交就好哦  "}, {"seq": 1704422778000, "time": "2024-01-05T10:46:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您这边也可以继续依托诚意药业申报国家的哦  不会浪费您很多时间的哦 到时候直接提交系统就可以啦[愉快]"}, {"seq": 1704422931000, "time": "2024-01-05T10:48:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "去年没成功 今年就算了"}, {"seq": 1704423177000, "time": "2024-01-05T10:52:57+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好！2023年国家级有个博士连续申报三年才成功的，今年申报不会耽误您的时间，系统直接提交就可以了"}, {"seq": 1704423205000, "time": "2024-01-05T10:53:25+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "国家级人才项目不会限制次数申报的"}, {"seq": 1704423293000, "time": "2024-01-05T10:54:53+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您的材料的保密的，是在国家申报系统里，我们现在也看不了，只有政府老师在系统里可以点提交"}, {"seq": 1704423349000, "time": "2024-01-05T10:55:49+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "同样的材料 去年没中 今年也没理由能中啊 为何重复申报呢？"}, {"seq": 1704423454000, "time": "2024-01-05T10:57:34+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "接老师通知，这个企业有入选的名单，老师已开专项会议，已评估博士情侣，老师预估聂博您入选率很高"}, {"seq": 1704423468000, "time": "2024-01-05T10:57:48+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "所以接老师通知邀请您继续申报"}, {"seq": 1704423601000, "time": "2024-01-05T11:00:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "能知道去年没中的主要原因是什么吗？是我背景不吻合，还是资质不够？"}, {"seq": 1704423787000, "time": "2024-01-05T11:03:07+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好！2023年省级还没出结果哦"}, {"seq": 1704423909000, "time": "2024-01-05T11:05:09+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我们和诚意报了2023省级吗？"}, {"seq": 1704423929000, "time": "2024-01-05T11:05:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的，聂博"}, {"seq": 1704424397000, "time": "2024-01-05T11:13:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "我怎么记得 由于我是和另一个企业报的省级 由于不能多家同时报 最后和诚意取消了申报 你确定吗？"}, {"seq": 1704424517000, "time": "2024-01-05T11:15:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "之前的微信记录我都删除 所以记得不太清了"}, {"seq": 1704425354000, "time": "2024-01-05T11:29:14+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1704425408000, "time": "2024-01-05T11:30:08+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博士和诚意药业没有取消申报的哦，我之后提交了您的唯一申报视频，所以和诚意药业的申报是唯一申报"}, {"seq": 1704432241000, "time": "2024-01-05T13:24:01+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"6bcf4435b7c962fa3ea7ad9e9bcfdabf\" encryver=\"1\" cdnthumbaeskey=\"6bcf4435b7c962fa3ea7ad9e9bcfdabf\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f7e990204e46bf071020465979273042434653466396635302d656533652d346161312d393831352d3062383232373162303039380204051818020201000405004c550500\" cdnthumblength=\"5180\" cdnthumbheight=\"61\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f7e990204e46bf071020465979273042434653466396635302d656533652d346161312d393831352d3062383232373162303039380204051818020201000405004c550500\" length=\"48675\" md5=\"35b1d65b815d9958fa28aeef77945f72\" hevc_mid_size=\"0\" originsourcemd5=\"35b1d65b815d9958fa28aeef77945f72\"/></msg>", "contents": {"md5": "35b1d65b815d9958fa28aeef77945f72"}}, {"seq": 1704432261000, "time": "2024-01-05T13:24:21+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博士   您的材料是正常提交了的    "}, {"seq": 1704497034000, "time": "2024-01-06T07:23:54+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "行 那就试试吧"}, {"seq": 1704501251000, "time": "2024-01-06T08:34:11+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的    聂博  "}, {"seq": 1704761969000, "time": "2024-01-09T08:59:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"3562de75960483f3779618bf646038d5\" encryver=\"1\" cdnthumbaeskey=\"3562de75960483f3779618bf646038d5\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f7e990204256bf0710204659c9a48042461636364343135382d633062372d343930372d616261332d3036323234386538343135310204011418020201000405004c53d900\" cdnthumblength=\"4007\" cdnthumbheight=\"50\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f7e990204256bf0710204659c9a48042461636364343135382d633062372d343930372d616261332d3036323234386538343135310204011418020201000405004c53d900\" length=\"22276\" md5=\"484702d453f8bacd221155daee65a46d\" hevc_mid_size=\"0\" originsourcemd5=\"484702d453f8bacd221155daee65a46d\"/></msg>", "contents": {"md5": "484702d453f8bacd221155daee65a46d"}}, {"seq": 1704761969001, "time": "2024-01-09T08:59:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1704761978000, "time": "2024-01-09T08:59:38+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博士   24年国家级政府已确认您的申报名额[强]"}, {"seq": 1704762369000, "time": "2024-01-09T09:06:09+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "每年继续申报的名额都是优中选优，是您的履历足够优异，和企业匹配度高，才能争取这个名额"}, {"seq": 1704767621000, "time": "2024-01-09T10:33:41+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": " 前两年都申报过 后来都没下文 挺失望的 希望这次不同吧"}, {"seq": 1704768190000, "time": "2024-01-09T10:43:10+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博   这个我们继续主推您   会有一个好的结果的"}, {"seq": 1704768194000, "time": "2024-01-09T10:43:14+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[爱心]"}, {"seq": 1707016679000, "time": "2024-02-04T11:17:59+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 51, "content": "<msg>\n    <fromusername>wxid_6ahbbsar7g8i22</fromusername>\n    <scene>0</scene>\n    <commenturl></commenturl>\n    <appmsg appid=\"\" sdkver=\"0\">\n        <title>美立中国的动态</title>\n        <des></des>\n        <action>view</action>\n        <type>51</type>\n        <showtype>0</showtype>\n        <content></content>\n        <url>https://support.weixin.qq.com/update/</url>\n        <dataurl></dataurl>\n        <lowurl></lowurl>\n        <lowdataurl></lowdataurl>\n        <recorditem></recorditem>\n        <thumburl></thumburl>\n        <messageaction></messageaction>\n        <laninfo></laninfo>\n        <extinfo></extinfo>\n        <sourceusername></sourceusername>\n        <sourcedisplayname></sourcedisplayname>\n        <commenturl></commenturl>\n        <appattach>\n            <totallen>0</totallen>\n            <attachid></attachid>\n            <emoticonmd5></emoticonmd5>\n            <fileext></fileext>\n            <aeskey></aeskey>\n        </appattach>\n        <webviewshared>\n            <publisherId></publisherId>\n            <publisherReqId>0</publisherReqId>\n        </webviewshared>\n        <weappinfo>\n            <pagepath></pagepath>\n            <username></username>\n            <appid></appid>\n            <appservicetype>0</appservicetype>\n        </weappinfo>\n        <websearch />\n        <finderFeed>\n            <objectId>14318941147185744067</objectId>\n            <objectNonceId>16644270569641015916_0_0_0_0_0</objectNonceId>\n            <feedType>4</feedType>\n            <nickname>美立中国</nickname>\n            <username>v2_060000231003b20faec8c5e08f1ac1d7c707ee31b077f1cc5653c7436ba812f124e3ac74d88e@finder</username>\n            <authIconUrl>https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png</authIconUrl>\n            <authIconType>1</authIconType>\n            <avatar>https://wx.qlogo.cn/finderhead/ver_1/hibpZvbkqhZBhDw0g6vvH9FGIb6WvV8RYkPBT34EnrIOmtq2vS8lfZyFuzGsoDpQI8YrP3F7TgNVCWWyUqbGiaNYoAibsSTWBicYI6qa8hcMpibA/0</avatar>\n            <desc>立春，一年中的第一个节气。立是开始，春是希望，万物复苏，好事正酿，人随春好，春与人安。#立春#节气#新年倒计时</desc>\n            <mediaCount>1</mediaCount>\n            <localId>0</localId>\n            <bizUsername></bizUsername>\n            <bizNickname></bizNickname>\n            <bizAvatar></bizAvatar>\n            <bizUsernameV2></bizUsernameV2>\n            <mediaList>\n                <media>\n                    <thumbUrl>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzmj2zngWM1SaY6Kohqx5ZoAHBLyHBLQRXQy2gPgBRUDOLGz6JNYtKpyFuNwMRBKia92ALjKkRBfLlIh4U8Hiay4hg&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=KkOFht0mCXmbbb24JV62hCqpynhxH8IoV6twYm3phnzyY8mGiaIkhUMnM2rapfMXNnW0qy8VRicL05Jyol6lcNRGKKqeMBrFuknJLuicDFFTiaA&ctsc=2-17]]>\n                    </thumbUrl>\n                    <videoPlayDuration>26</videoPlayDuration>\n                    <url>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDbd5C4rw9biboXT2QZ0ebwQgRAwOA1tKLgydW3ibianromHQ46jjHiaibM36myo1nJicOltiaCv8QdKDoIxWZZwU2fFLe&bizid=1023&dotrans=0&hy=SH&idx=1&m=&upid=0]]>\n                    </url>\n                    <coverUrl>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzmj2zngWM1SaY6Kohqx5ZoAHBLyHBLQRXQy2gPgBRUDOLGz6JNYtKpyFuNwMRBKia92ALjKkRBfLlIh4U8Hiay4hg&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=KkOFht0mCXmbbb24JV62hCqpynhxH8IoV6twYm3phnzyY8mGiaIkhUMnM2rapfMXNnW0qy8VRicL05Jyol6lcNRGKKqeMBrFuknJLuicDFFTiaA&ctsc=2-17]]>\n                    </coverUrl>\n                    <height>\n                        <![CDATA[1920]]>\n                    </height>\n                    <width>\n                        <![CDATA[1080]]>\n                    </width>\n                    <mediaType>\n                        <![CDATA[4]]>\n                    </mediaType>\n                    <fullCoverUrl>\n                        <![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzmj2zngWM1SaY6Kohqx5ZoAHBLyHBLQRXQy2gPgBRUDOLGz6JNYtKpyFuNwMRBKia92ALjKkRBfLlIh4U8Hiay4hg&bizid=1023&dotrans=0&hy=SH&idx=1&m=&scene=0&token=ic1n0xDG6awicH6MdcuPR8yf9V1g5iaerNCsMtQUXm73zmBzO4VYacibuBfOiazsZicQUvXLY3pw0NcPM72JGLdIvM0wib4vffBu1nyiaCnJ6RiaXvSo&ctsc=3-17]]>\n                    </fullCoverUrl>\n                </media>\n            </mediaList>\n        </finderFeed>\n    </appmsg>\n    <appinfo>\n        <version>1</version>\n        <appname>Window wechat</appname>\n    </appinfo>\n</msg>\n\u0000", "contents": {"title": "立春，一年中的第一个节气。立是开始，春是希望，万物复苏，好事正酿，人随春好，春与人安。#立春#节气#新年倒计时", "url": "\n                        http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDbd5C4rw9biboXT2QZ0ebwQgRAwOA1tKLgydW3ibianromHQ46jjHiaibM36myo1nJicOltiaCv8QdKDoIxWZZwU2fFLe&bizid=1023&dotrans=0&hy=SH&idx=1&m=&upid=0\n                    "}}, {"seq": 1707016783000, "time": "2024-02-04T11:19:43+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "人随春好，春与人安。聂博士，立春快乐"}, {"seq": 1707381618000, "time": "2024-02-08T16:40:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "不辞加一岁，唯喜到三春；[福][福][福]\n正值腊月，岁序更新，华章再续。\n但入新年，在这辞旧迎新之际，家国同乐之时，我谨代表我司全体员工祝您佳节共欢同乐，也向您致以最诚挚的感激，惟愿我司与您的合作在新的一年辰龙而上，逐光而行[烟花][烟花][烟花]"}, {"seq": 1707381618001, "time": "2024-02-08T16:40:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"a883e19d5882b41240c8f23755676068\" encryver=\"1\" cdnthumbaeskey=\"a883e19d5882b41240c8f23755676068\" cdnthumburl=\"3057020100044b30490201000204ff23008602032df5a10204a13e2a3a020465c4926a042466663066313761322d363261652d343461362d383532342d3130663136303363623966370204051418020201000405004c55cd00\" cdnthumblength=\"11624\" cdnthumbheight=\"149\" cdnthumbwidth=\"73\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032df5a10204a13e2a3a020465c4926a042466663066313761322d363261652d343461362d383532342d3130663136303363623966370204051418020201000405004c55cd00\" length=\"399798\" md5=\"eff79e129ab05ec945d932c3248cb9ba\" hevc_mid_size=\"0\" originsourcemd5=\"eff79e129ab05ec945d932c3248cb9ba\"/></msg>", "contents": {"md5": "eff79e129ab05ec945d932c3248cb9ba"}}, {"seq": 1740639638000, "time": "2025-02-27T15:00:38+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博您好呀，您今年还考虑继续申报项目吗？我们这边可以给到您相应的报酬哦"}, {"seq": 1740696174000, "time": "2025-02-28T06:42:54+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": " 今年再报的话 应该是第三次了吧  好奇你说的相应报酬是什么意思？"}, {"seq": 1740727666000, "time": "2025-02-28T15:27:46+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "就是今年申报的话会给您1w元"}, {"seq": 1740727673000, "time": "2025-02-28T15:27:53+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "作为您申报的奖励"}, {"seq": 1740727719000, "time": "2025-02-28T15:28:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "第三次也没关系呀"}, {"seq": 1740727735000, "time": "2025-02-28T15:28:55+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "换个地方"}, {"seq": 1740965852000, "time": "2025-03-03T09:37:32+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "方便了解一下 这次合作的单位，我需要配合的内容，以及申报奖励的兑现方式吗？"}, {"seq": 1740984634000, "time": "2025-03-03T14:50:34+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，您看您什么时候方便语音沟通说一下这个事呢"}, {"seq": 1741028786000, "time": "2025-03-04T03:06:26+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "明天上午9-10点（你的时间）可以吗？ "}, {"seq": 1741047967000, "time": "2025-03-04T08:26:07+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以的"}, {"seq": 1741050031000, "time": "2025-03-04T09:00:31+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "今天如果方便的话 也可以 "}, {"seq": 1741050114000, "time": "2025-03-04T09:01:54+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "不行的话 就明早"}, {"seq": 1741050452000, "time": "2025-03-04T09:07:32+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以的"}, {"seq": 1741050459000, "time": "2025-03-04T09:07:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我这边给您打过来哦"}, {"seq": 1741051520001, "time": "2025-03-04T09:25:20+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[通话时长 15:57]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>570601338659147403</roomid>\n<roomkey>0</roomkey>\n<inviteid>1588782976</inviteid>\n<msg_type>100</msg_type>\n<timestamp>1741051520069</timestamp>\n<identity><![CDATA[1664385715182074124]]></identity>\n<duration>0</duration>\n<inviteid64>1741050537856</inviteid64>\n<business>1</business>\n<caller_memberid>0</caller_memberid>\n<callee_memberid>1</callee_memberid>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1742520258000, "time": "2025-03-21T09:24:18+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博"}, {"seq": 1742520264000, "time": "2025-03-21T09:24:24+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您现在方便吗？"}, {"seq": 1742520819000, "time": "2025-03-21T09:33:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "你好"}, {"seq": 1742520839000, "time": "2025-03-21T09:33:59+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我这边有帮您匹配到一个企业"}, {"seq": 1742520850000, "time": "2025-03-21T09:34:10+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我发您您看看怎么样"}, {"seq": 1742520856000, "time": "2025-03-21T09:34:16+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "好啊"}, {"seq": 1742520870000, "time": "2025-03-21T09:34:30+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "要是确定在这家企业申报的话，我就后续给您签协议给您报酬"}, {"seq": 1742520930000, "time": "2025-03-21T09:35:30+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "那好 我看看企业情况先"}, {"seq": 1742520938000, "time": "2025-03-21T09:35:38+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "嗯嗯好的"}, {"seq": 1742521254000, "time": "2025-03-21T09:40:54+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "浙江车头制药股份有限公司"}, {"seq": 1742521256000, "time": "2025-03-21T09:40:56+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "https://www.charioteer.cn/"}, {"seq": 1742521267000, "time": "2025-03-21T09:41:07+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您看看，这是企业的官网"}, {"seq": 1742521810000, "time": "2025-03-21T09:50:10+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "你觉得背景匹配吗？"}, {"seq": 1742521842000, "time": "2025-03-21T09:50:42+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "政府的老师看了您的简历，觉得可以"}, {"seq": 1742521863000, "time": "2025-03-21T09:51:03+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "他们要药物制剂的"}, {"seq": 1742521887000, "time": "2025-03-21T09:51:27+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"18c5659bbb59b0f3de14e261293743d7\" encryver=\"1\" cdnthumbaeskey=\"18c5659bbb59b0f3de14e261293743d7\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f54050204a66a42b7020467dcc624042431313766353039302d636136662d343933382d383231342d3239393661646531333737620204051438010201000405004c550500\" cdnthumblength=\"7579\" cdnthumbheight=\"50\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f54050204a66a42b7020467dcc624042431313766353039302d636136662d343933382d383231342d3239393661646531333737620204051438010201000405004c550500\" length=\"39824\" md5=\"d43060daed992b3b3622f6686a7fec83\" hevc_mid_size=\"0\" originsourcemd5=\"d43060daed992b3b3622f6686a7fec83\"/></msg>", "contents": {"md5": "d43060daed992b3b3622f6686a7fec83"}}, {"seq": 1742521992000, "time": "2025-03-21T09:53:12+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "明白 目前还有其他企业选择吗？"}, {"seq": 1742522184000, "time": "2025-03-21T09:56:24+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "比较好一点的企业就车头了"}, {"seq": 1742522195000, "time": "2025-03-21T09:56:35+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "其他的看起来都没车头好"}, {"seq": 1742522257000, "time": "2025-03-21T09:57:37+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "了解 那再试试"}, {"seq": 1742522276000, "time": "2025-03-21T09:57:56+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的聂博，那我这边去跟企业那边说一下"}, {"seq": 1742522286000, "time": "2025-03-21T09:58:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "到时候我跟您签协议哈"}, {"seq": 1742522340000, "time": "2025-03-21T09:59:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "资料你们都全吧 我还需要配合啥吗？"}, {"seq": 1742522584000, "time": "2025-03-21T10:03:04+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "有补充的话我再跟您说，但是应该没啥"}, {"seq": 1742523000000, "time": "2025-03-21T10:10:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1742523519000, "time": "2025-03-21T10:18:39+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "辛苦聂博"}, {"seq": 1742545792000, "time": "2025-03-21T16:29:52+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 6, "content": "<msg>\n    <fromusername>wxid_6ahbbsar7g8i22</fromusername>\n    <scene>0</scene>\n    <commenturl></commenturl>\n    <appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n        <title>人才独家申报协议付费版本.docx</title>\n        <des></des>\n        <action>view</action>\n        <type>6</type>\n        <showtype>0</showtype>\n        <content></content>\n        <url></url>\n        <dataurl></dataurl>\n        <lowurl></lowurl>\n        <lowdataurl></lowdataurl>\n        <recorditem></recorditem>\n        <thumburl></thumburl>\n        <messageaction></messageaction>\n        <laninfo></laninfo>\n        <md5>5caefe066f2bcdc3de692dedd6bf3362</md5>\n        <extinfo></extinfo>\n        <sourceusername></sourceusername>\n        <sourcedisplayname></sourcedisplayname>\n        <commenturl></commenturl>\n        <appattach>\n            <totallen>15686</totallen>\n            <attachid>@cdn_3057020100044b30490201000204ff23008602032f540502046f6a42b7020467dd17ee042462366632323431612d346138312d346637632d396662652d3663643962633566363335630204051400050201000405004c543d00__1</attachid>\n            <emoticonmd5></emoticonmd5>\n            <fileext>docx</fileext>\n            <fileuploadtoken>v1_r8WaPejmbpWlgUbx9O9ioD0tkOLMuMDZWV8bQdXWFiLMozrCN7itEnO4M6194mCpIJeyqZLQ8pki8v+YOqtskv6Z99kdfJw90I2x4XBUfr+yIKQ/V8WYxv++avR69n0jMTnDzPyzLd1pFz2GPxCT/EaEp/jrwQKtVoywTUfeVLboehIFKEFg7y/x/4OraFRiQg0CuCqwEYEXw/qjqb3sNQWatSclBDmGDutXiwz/TytOp2PB05NJHUeybknGXB6kP3au7sgM4Bu9yw==</fileuploadtoken>\n            <overwrite_newmsgid>1652484711894101598</overwrite_newmsgid>\n            <filekey>3b22231bd5a0a4aa433ab0c256e7e78d</filekey>\n            <cdnattachurl>3057020100044b30490201000204ff23008602032f540502046f6a42b7020467dd17ee042462366632323431612d346138312d346637632d396662652d3663643962633566363335630204051400050201000405004c543d00</cdnattachurl>\n            <aeskey>ea03ce69995b6809c01a8840526da1ca</aeskey>\n            <encryver>1</encryver>\n        </appattach>\n        <webviewshared>\n            <publisherId></publisherId>\n            <publisherReqId>0</publisherReqId>\n        </webviewshared>\n        <weappinfo>\n            <pagepath></pagepath>\n            <username></username>\n            <appid></appid>\n            <appservicetype>0</appservicetype>\n        </weappinfo>\n        <websearch />\n    </appmsg>\n    <appinfo>\n        <version>1</version>\n        <appname>Window wechat</appname>\n    </appinfo>\n</msg>\n\u0000", "contents": {"md5": "5caefe066f2bcdc3de692dedd6bf3362", "title": "人才独家申报协议付费版本.docx"}}, {"seq": 1742545806000, "time": "2025-03-21T16:30:06+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，您签一个这个，之后咱们会给您奖励的"}, {"seq": 1742753885000, "time": "2025-03-24T02:18:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"f6a154e32e8d7181ae6939c3d28c2e0d\" encryver=\"1\" cdnthumbaeskey=\"f6a154e32e8d7181ae6939c3d28c2e0d\" cdnthumburl=\"3057020100044b3049020100020464ab305002032de37a02041081af2b020467e0505c042461333432333934352d316162332d343562652d626335312d6162626136356136613633350204011d0a020201000405004c55cd00\" cdnthumblength=\"3631\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002032de37a02041081af2b020467e0505c042461333432333934352d316162332d343562652d626335312d6162626136356136613633350204011d0a020201000405004c55cd00\" length=\"138516\" md5=\"570096dd47ed0e6819cade709ae2e0f8\" hevc_mid_size=\"138516\" originsourcemd5=\"20e145409b94211b2fea2498535c4de0\">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n", "contents": {"md5": "570096dd47ed0e6819cade709ae2e0f8"}}, {"seq": 1742753888000, "time": "2025-03-24T02:18:08+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"7b230d3a53aa635ac5dde16317e48cd9\" encryver=\"1\" cdnthumbaeskey=\"7b230d3a53aa635ac5dde16317e48cd9\" cdnthumburl=\"3057020100044b3049020100020464ab305002032de37a02041081af2b020467e0505f042433626332353930612d663164662d343534662d393362652d3266653262326462303231330204011d0a020201000405004c4c6d00\" cdnthumblength=\"3311\" cdnthumbheight=\"120\" cdnthumbwidth=\"89\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002032de37a02041081af2b020467e0505f042433626332353930612d663164662d343534662d393362652d3266653262326462303231330204011d0a020201000405004c4c6d00\" length=\"95306\" md5=\"3f3f3ccf4e9a54ac107a3934033ab49f\" hevc_mid_size=\"95306\" originsourcemd5=\"ec1f3d033a54ba3c5cbff98c0d6993c9\">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n", "contents": {"md5": "3f3f3ccf4e9a54ac107a3934033ab49f"}}, {"seq": 1742783369000, "time": "2025-03-24T10:29:29+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到聂博，辛苦"}, {"seq": 1742806573000, "time": "2025-03-24T16:56:13+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 6, "content": "<msg>\n    <fromusername>wxid_6ahbbsar7g8i22</fromusername>\n    <scene>0</scene>\n    <commenturl></commenturl>\n    <appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n        <title>聂书芳人才付费盖章版(1).pdf</title>\n        <des></des>\n        <action>view</action>\n        <type>6</type>\n        <showtype>0</showtype>\n        <content></content>\n        <url></url>\n        <dataurl></dataurl>\n        <lowurl></lowurl>\n        <lowdataurl></lowdataurl>\n        <recorditem></recorditem>\n        <thumburl></thumburl>\n        <messageaction></messageaction>\n        <laninfo></laninfo>\n        <md5>326eca1df08bfce6b09e507bf0432421</md5>\n        <extinfo></extinfo>\n        <sourceusername></sourceusername>\n        <sourcedisplayname></sourcedisplayname>\n        <commenturl></commenturl>\n        <appattach>\n            <totallen>708714</totallen>\n            <attachid>@cdn_3057020100044b30490201000204ff23008602032f54050204956a42b7020467e11e2e042463613739626439352d646438632d346430612d613132302d3937653365363162376337650204051800050201000405004c575d00_84cd35d7f56eab9839b75631d19ab7cc_1</attachid>\n            <emoticonmd5></emoticonmd5>\n            <fileext>pdf</fileext>\n            <fileuploadtoken>v1_u86HUu499Ia/1BWTEX2wtIX/7RsRtrfOEYRotdeSqGj0P+Xtfnl4L7qvb9z5kTP3gCiuCfGF+Mb4pl62o1vE2zn+opcu+A1tdfC0Fac9gVdZ64C3AhG13Y2pXChXtIpISNSKYioOBbY0ycWrOzOzDp/C136Ks/Yf0dZk0fw+oxEP2JWFwqYyotQCSpPvIMl1eutMZ3jyMNwRkI2gbS8FQLUNgpErLqf4iMXZPXu99C+YG/FMprqcMEZ4t/XCNwNDu17btajp</fileuploadtoken>\n            <overwrite_newmsgid>3633391306546214558</overwrite_newmsgid>\n            <filekey>f3a68c87d07438567bb249941ebc988f</filekey>\n            <cdnattachurl>3057020100044b30490201000204ff23008602032f54050204956a42b7020467e11e2e042463613739626439352d646438632d346430612d613132302d3937653365363162376337650204051800050201000405004c575d00</cdnattachurl>\n            <aeskey>84cd35d7f56eab9839b75631d19ab7cc</aeskey>\n            <encryver>1</encryver>\n        </appattach>\n        <webviewshared>\n            <publisherId></publisherId>\n            <publisherReqId>0</publisherReqId>\n        </webviewshared>\n        <weappinfo>\n            <pagepath></pagepath>\n            <username></username>\n            <appid></appid>\n            <appservicetype>0</appservicetype>\n        </weappinfo>\n        <websearch />\n    </appmsg>\n    <appinfo>\n        <version>1</version>\n        <appname>Window wechat</appname>\n    </appinfo>\n</msg>\n\u0000", "contents": {"md5": "326eca1df08bfce6b09e507bf0432421", "title": "聂书芳人才付费盖章版(1).pdf"}}, {"seq": 1742806584000, "time": "2025-03-24T16:56:24+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，已经盖好章啦，您存一下"}, {"seq": 1743046625000, "time": "2025-03-27T11:37:05+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，您最新的证件照可以发我一张吗？"}, {"seq": 1743046717000, "time": "2025-03-27T11:38:37+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "身份证或护照吗？"}, {"seq": 1743046831000, "time": "2025-03-27T11:40:31+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "就证件照，照片"}, {"seq": 1743046834000, "time": "2025-03-27T11:40:34+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "2寸的"}, {"seq": 1743046997000, "time": "2025-03-27T11:43:17+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "这边从来不用2寸大头照啊"}, {"seq": 1743047122000, "time": "2025-03-27T11:45:22+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "自拍个人照行不？"}, {"seq": 1743047126000, "time": "2025-03-27T11:45:26+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "要不随便发一张自拍"}, {"seq": 1743047130000, "time": "2025-03-27T11:45:30+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以可以"}, {"seq": 1743047211000, "time": "2025-03-27T11:46:51+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "明天给你行不？"}, {"seq": 1743047219000, "time": "2025-03-27T11:46:59+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没问题"}, {"seq": 1743156527000, "time": "2025-03-28T18:08:47+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"a34b653f0ea023785aefbd60a0baabe3\" encryver=\"1\" cdnthumbaeskey=\"a34b653f0ea023785aefbd60a0baabe3\" cdnthumburl=\"3057020100044b3049020100020464ab305002032de37a02041081af2b020467e6752e042463343939636565362d346233302d343636662d613334342d3333393635323431646231650204011d0a020201000405004c4c6d00\" cdnthumblength=\"4052\" cdnthumbheight=\"102\" cdnthumbwidth=\"120\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b3049020100020464ab305002032de37a02041081af2b020467e6752e042463343939636565362d346233302d343636662d613334342d3333393635323431646231650204011d0a020201000405004c4c6d00\" length=\"11977\" md5=\"5105bbd09b597f41576856e95050efb8\" hevc_mid_size=\"11977\" originsourcemd5=\"9587fd007b8dc59199377f7b4b8fc22e\">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n", "contents": {"md5": "5105bbd09b597f41576856e95050efb8"}}, {"seq": 1743216984000, "time": "2025-03-29T10:56:24+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到 聂博"}, {"seq": 1750387440000, "time": "2025-06-20T10:44:00+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "聂博，现在在准备省级的项目，您是否考虑全职回来呀"}, {"seq": 1750678913000, "time": "2025-06-23T19:41:53+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "wxid_ak5g6ruqf9k612", "senderName": "11966 聂书芳", "isSelf": false, "type": 1, "subType": 0, "content": "机会合适 会考虑"}, {"seq": 1750724621000, "time": "2025-06-24T08:23:41+08:00", "talker": "wxid_ak5g6ruqf9k612", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好滴好滴，那我也为您匹配看看~"}]}