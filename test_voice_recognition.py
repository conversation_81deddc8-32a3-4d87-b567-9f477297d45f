#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语音消息识别功能
"""

def test_voice_message_parsing():
    """测试语音消息解析功能"""
    print("=" * 60)
    print("🎤 测试语音消息识别功能")
    print("=" * 60)
    
    # 模拟语音消息数据
    test_messages = [
        {
            'content': '[语音](http://192.168.2.50:5030/voice/3217913923852777824)',
            'type': 'text',
            'time': '2024-01-24 17:04:20',
            'isSelf': False,
            'url': '',
            'filename': ''
        },
        {
            'content': '[语音](http://192.168.2.50:5030/voice/4198064534402464830)',
            'type': 'text', 
            'time': '2024-01-24 17:07:47',
            'isSelf': True,
            'url': '',
            'filename': ''
        },
        {
            'content': '[语音](http://192.168.2.50:5030/voice/4550755764761457989)',
            'type': 'text',
            'time': '2024-01-24 17:08:04', 
            'isSelf': True,
            'url': '',
            'filename': ''
        }
    ]
    
    # 导入app.py中的函数
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from app import parse_attachment_info
        
        print("📋 测试消息解析:")
        voice_count = 0
        
        for i, msg in enumerate(test_messages):
            print(f"\n--- 消息 {i+1} ---")
            print(f"内容: {msg['content']}")
            print(f"时间: {msg['time']}")
            print(f"发送者: {'我' if msg['isSelf'] else '对方'}")
            
            # 解析附件信息
            attachment_info = parse_attachment_info(msg['content'], msg.get('url', ''), msg.get('filename', ''))
            
            print(f"解析结果:")
            print(f"  - 附件类型: {attachment_info.get('attachment_type', 'None')}")
            print(f"  - 描述: {attachment_info.get('description', 'None')}")
            print(f"  - URL: {attachment_info.get('url', 'None')}")
            
            if attachment_info.get('attachment_type') == 'voice':
                voice_count += 1
                print(f"✅ 成功识别为语音消息")
            else:
                print(f"❌ 未识别为语音消息")
        
        print(f"\n📊 测试结果:")
        print(f"总消息数: {len(test_messages)}")
        print(f"识别为语音的消息数: {voice_count}")
        print(f"识别成功率: {voice_count/len(test_messages)*100:.1f}%")
        
        if voice_count == len(test_messages):
            print("✅ 所有语音消息都被正确识别！")
        else:
            print("❌ 部分语音消息识别失败")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_url_extraction():
    """测试URL提取功能"""
    print("\n" + "=" * 60)
    print("🔗 测试URL提取功能")
    print("=" * 60)
    
    import re
    
    test_content = '[语音](http://192.168.2.50:5030/voice/3217913923852777824)'
    
    print(f"测试内容: {test_content}")
    
    # 测试URL提取
    url_match = re.search(r'\[语音\]\((http[^)]+)\)', test_content)
    if url_match:
        extracted_url = url_match.group(1)
        print(f"✅ 成功提取URL: {extracted_url}")
    else:
        print(f"❌ URL提取失败")
    
    # 测试语音关键词检测
    if '[语音](' in test_content and 'voice/' in test_content:
        print(f"✅ 语音关键词检测成功")
    else:
        print(f"❌ 语音关键词检测失败")

if __name__ == "__main__":
    test_voice_message_parsing()
    test_url_extraction()
