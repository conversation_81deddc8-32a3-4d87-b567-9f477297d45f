{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "49027", "contact_info": {"UserName": "wxid_o8gispcotva012", "Alias": "L476766231", "Remark": "49027林远", "NickName": "<PERSON>"}, "fetch_time": "2025-07-09T13:19:52.625636", "message_count": 33, "chatlog_data": [{"seq": 1748254046000, "time": "2025-05-26T18:07:26+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "我通过了你的朋友验证请求，现在我们可以开始聊天了"}, {"seq": 1748254056000, "time": "2025-05-26T18:07:36+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "您好"}, {"seq": 1748256669000, "time": "2025-05-26T18:51:09+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士您好"}, {"seq": 1748256670000, "time": "2025-05-26T18:51:10+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们是一家专业的猎头公司，搭建高层次人才与国内企业高校就业创业全职兼职合作的桥梁；可以柔性合作或者全职回国就业创业。"}, {"seq": 1748256670001, "time": "2025-05-26T18:51:10+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们现在主要是浙江省的人才项目，都是真实的招聘需求，您有全职回国的计划没有呀"}, {"seq": 1748256840000, "time": "2025-05-26T18:54:00+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "我在找机会回国，你能如何帮组？我传我的研究资料，谢谢"}, {"seq": 1748256917000, "time": "2025-05-26T18:55:17+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您可以说下您的一些诉求，像期望薪资，平台的一些倾向性等等，我们会根据您的简历在结合您的一些诉求去帮您匹配一些合适的单位"}, {"seq": 1748256917001, "time": "2025-05-26T18:55:17+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"\" sdkver=\"0\">\n\t\t<title>YUAN CV 032525.pdf</title>\n\t\t<type>6</type>\n\t\t<appattach>\n\t\t\t<totallen>1169815</totallen>\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<attachid>@cdn_3057020100044b30490201000204a6412f8902030f5bd20204302a982b020468344896042465646233366133662d333035332d346131372d623161612d3430383263376530663638380204011c00050201000405004c53d900_0da0cebc27ff877895095b1cf90bcc03_1</attachid>\n\t\t\t<cdnattachurl>3057020100044b30490201000204a6412f8902030f5bd20204302a982b020468344896042465646233366133662d333035332d346131372d623161612d3430383263376530663638380204011c00050201000405004c53d900</cdnattachurl>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey>0da0cebc27ff877895095b1cf90bcc03</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t\t<filekey>wxid_6ahbbsar7g8i22_9_1748256917</filekey>\n\t\t\t<overwrite_newmsgid>9037676089172037650</overwrite_newmsgid>\n\t\t\t<fileuploadtoken>v1_8v0bmD8AGyOU0qlOTOWnJ+GKm/7aK2e+pfr/0drbvEmqZNgPXeWGwQTYuygmM7F+aq08ohzzQ3bDrQJIVu5RqvfHtBJdUEvweZGCP7AXrFdEZX5IGkJo1zHiWcMd7tC3hn4L3TqK4NzrG2fMUyPA65nGXA3PN80GGBw/85o3aKM4RiwcKde5hNm1L+J5RWN0ehPYi0TuckTJ9ruPWh7VrA==</fileuploadtoken>\n\t\t</appattach>\n\t\t<md5>c4a12aa00ca58858dfeda4ab883d3b22</md5>\n\t</appmsg>\n\t<fromusername>wxid_o8gispcotva012</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n\u0000", "contents": {"md5": "c4a12aa00ca58858dfeda4ab883d3b22", "title": "YUAN CV 032525.pdf"}}, {"seq": 1748256967000, "time": "2025-05-26T18:56:07+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "大概的一个流程是我们会把合适的单位发给您过目，您觉得合适我们会安排您和企业开个会，相当于线上面试"}, {"seq": 1748256986000, "time": "2025-05-26T18:56:26+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "后面是入职过后才申请项目的"}, {"seq": 1748257000000, "time": "2025-05-26T18:56:40+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "我报了今年启明计划"}, {"seq": 1748257028000, "time": "2025-05-26T18:57:08+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这个是国家级的，现在是省级的，是不冲突的哦"}, {"seq": 1748257031000, "time": "2025-05-26T18:57:11+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以同时报的"}, {"seq": 1748257056000, "time": "2025-05-26T18:57:36+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您是报了哪个地方的呀"}, {"seq": 1748257180000, "time": "2025-05-26T18:59:40+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "浙江"}, {"seq": 1748258010000, "time": "2025-05-26T19:13:30+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那可以的呀，我可以帮您匹配看看哦"}, {"seq": 1748591013000, "time": "2025-05-30T15:43:33+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士，下午好！ 我们之前有联系过的，目前我们在准备创业大赛，您有兴趣参加吗         https://www.fujiantalent.com/#/\n这个是大赛的链接，您可以先了解一下"}, {"seq": 1748594832000, "time": "2025-05-30T16:47:12+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "谢谢，我读一下。端午节快乐🎉"}, {"seq": 1748595723000, "time": "2025-05-30T17:02:03+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "请给我申报材料，谢谢"}, {"seq": 1748595841000, "time": "2025-05-30T17:04:01+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "林博  端午节快乐[庆祝]"}, {"seq": 1748595889000, "time": "2025-05-30T17:04:49+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"27fb15c42dc38027563a80282ae14f14\" encryver=\"1\" cdnthumbaeskey=\"27fb15c42dc38027563a80282ae14f14\" cdnthumburl=\"3057020100044b30490201000204a6412f8902030f5bd202042c2a982b0204683974b0042463616133653039632d343731322d343865382d396463312d3465336536663961313332330204211d08020201000405004c556900\" cdnthumblength=\"6160\" cdnthumbheight=\"90\" cdnthumbwidth=\"120\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204a6412f8902030f5bd202042c2a982b0204683974b0042463616133653039632d343731322d343865382d396463312d3465336536663961313332330204211d08020201000405004c556900\" length=\"347311\" md5=\"d79313bd208296b5445dcf9e20d773c2\" hevc_mid_size=\"347311\" originsourcemd5=\"439cd4c5ee5ab860107e51ac49d0ad7b\">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImYwNTA5MDEyMTAxMDAwMDAiLCJwZHFoYXNoIjoiOWM0ZjY3MGI1NzBiMTU4YjI3NWI2NTQ0MGRlZDU5NWRhYjUwZTkwYzQyMWQ2YTU2Zjk3OTk1ZDBmMzM1ZGEyOCJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n", "contents": {"md5": "d79313bd208296b5445dcf9e20d773c2"}}, {"seq": 1748595946000, "time": "2025-05-30T17:05:46+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "林博是这样的，我们会根据您提供的材料材料 撰写申报材料"}, {"seq": 1748595992000, "time": "2025-05-30T17:06:32+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 49, "subType": 57, "content": "请列出所需材料", "contents": {"refer": {"seq": 0, "time": "2025-05-30T15:43:33+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6ahbbsar7g8i22", "senderName": "<PERSON><PERSON> Zhu@海归就业创业", "isSelf": false, "type": 1, "subType": 0, "content": "博士，下午好！ 我们之前有联系过的，目前我们在准备创业大赛，您有兴趣参加吗         https://www.fujiantalent.com/#/\n这个是大赛的链接，您可以先了解一下"}}}, {"seq": 1748596061000, "time": "2025-05-30T17:07:41+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到  稍等一下  林博"}, {"seq": 1748596298000, "time": "2025-05-30T17:11:38+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<img aeskey=\"51610099a97fcbe7e410ea7633e0cd20\" encryver=\"1\" cdnthumbaeskey=\"51610099a97fcbe7e410ea7633e0cd20\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f5406020471f4903a020468397649042463636563653438302d353961612d346334372d623337662d366236396161323132396334020405190a020201000405004c57c200\" cdnthumblength=\"2940\" cdnthumbheight=\"185\" cdnthumbwidth=\"432\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f5406020471f4903a020468397649042463636563653438302d353961612d346334372d623337662d366236396161323132396334020405190a020201000405004c57c200\" length=\"11010\" md5=\"996855a28c25d2fe78dd78d66eecb042\" hevc_mid_size=\"6897\" originsourcemd5=\"871a3474e32f116d9a3d82bdec554d40\">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjMwMDAxMDAwMDAwMDAwMDAiLCJwZHFIYXNoIjoiMGRiODgzZGM0M2U3YjllMDgw\nZjhjZjhiMzA3NDdmZTc0NzBmYmMxODFjMmI3MDY3YjcwNmMxYWJkZDgxMWM3NCJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n", "contents": {"md5": "996855a28c25d2fe78dd78d66eecb042"}}, {"seq": 1748596316000, "time": "2025-05-30T17:11:56+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "大概需要这些信息  林博"}, {"seq": 1748596358000, "time": "2025-05-30T17:12:38+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "很好看的呀  林博", "contents": {"refer": {"seq": 0, "time": "2025-05-30T17:04:49+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "<PERSON>", "isSelf": false, "type": 3, "subType": 0, "content": "", "contents": {"md5": "d79313bd208296b5445dcf9e20d773c2"}}}}, {"seq": 1748598985000, "time": "2025-05-30T17:56:25+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "这是福建省的大赛，还包括人才计划吗？"}, {"seq": 1748599923672, "time": "2025-05-30T18:12:03+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "人才计划的话是浙江这边的省级项目"}, {"seq": 1748599933132, "time": "2025-05-30T18:12:13+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "需要的材料是一样滴"}, {"seq": 1748600845000, "time": "2025-05-30T18:27:25+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "wxid_o8gispcotva012", "senderName": "49027林远", "isSelf": false, "type": 1, "subType": 0, "content": "省人才计划似乎猎头也在申办，我问一下。    "}, {"seq": 1748602561749, "time": "2025-05-30T18:56:01+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您那边的是哪个省的呀"}, {"seq": 1748913959000, "time": "2025-06-03T09:25:59+08:00", "talker": "wxid_o8gispcotva012", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "林博，您这边材料准备的怎么样呀，您也可以把您准备好的先提供给我们哦"}]}