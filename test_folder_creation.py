"""
测试自动文件夹创建功能
"""

import requests
import json
import time

def test_upload_contacts():
    """测试upload_contacts接口的文件夹创建功能"""
    print("="*60)
    print("测试自动文件夹创建功能")
    print("="*60)
    
    # 测试数据
    test_cases = [
        {
            "client_id": "192_168_1_100_测试用户1",
            "contacts": [
                {
                    "UserName": "test_contact_001",
                    "NickName": "测试联系人1",
                    "Alias": "",
                    "Remark": "测试数据"
                },
                {
                    "UserName": "test_contact_002",
                    "NickName": "测试联系人2",
                    "Alias": "test2",
                    "Remark": ""
                }
            ]
        },
        {
            "client_id": "10_0_0_50_测试用户2",
            "contacts": [
                {
                    "UserName": "test_contact_003",
                    "NickName": "测试联系人3",
                    "Alias": "",
                    "Remark": "第二个用户的测试数据"
                }
            ]
        }
    ]
    
    server_url = "http://127.0.0.1:8080/upload_contacts"
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}:")
        print(f"   Client ID: {test_case['client_id']}")
        print(f"   联系人数量: {len(test_case['contacts'])}")
        
        try:
            response = requests.post(server_url, json=test_case, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            print(f"   ✅ 响应: {result}")
            
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n📁 请检查data目录结构:")
    print(f"   应该看到以下文件夹:")
    print(f"   - data/测试用户1/")
    print(f"   - data/测试用户2/")
    print(f"   每个文件夹中应该包含:")
    print(f"   - 联系人JSON文件")
    print(f"   - new_contacts_log.json")

def test_existing_user():
    """测试现有用户的文件夹创建"""
    print(f"\n🔄 测试现有用户 '小司' 的文件夹创建:")
    
    # 模拟小司的数据
    test_data = {
        "client_id": "192_168_1_5_小司",
        "contacts": [
            {
                "UserName": "existing_contact_001",
                "NickName": "现有联系人1",
                "Alias": "",
                "Remark": "为小司添加的测试联系人"
            }
        ]
    }
    
    server_url = "http://127.0.0.1:8080/upload_contacts"
    
    try:
        response = requests.post(server_url, json=test_data, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        print(f"   ✅ 响应: {result}")
        print(f"   📂 应该为 '小司' 创建了文件夹: data/小司/")
        
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")

def cleanup_test_data():
    """清理测试数据"""
    import os
    import shutil
    from pathlib import Path
    
    print(f"\n🧹 清理测试数据...")
    
    data_dir = Path("data")
    test_folders = ["测试用户1", "测试用户2"]
    
    cleaned_count = 0
    for folder_name in test_folders:
        folder_path = data_dir / folder_name
        if folder_path.exists():
            try:
                shutil.rmtree(folder_path)
                print(f"   ✅ 已删除: {folder_path}")
                cleaned_count += 1
            except Exception as e:
                print(f"   ❌ 删除失败: {folder_path} - {str(e)}")
    
    print(f"   清理完成，删除了 {cleaned_count} 个测试文件夹")

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup":
        cleanup_test_data()
        return
    
    print("此测试将验证自动文件夹创建功能")
    print("确保Flask服务器正在运行 (python app.py)")
    print()
    
    choice = input("是否开始测试? (y/N): ").lower().strip()
    if choice != 'y':
        print("测试已取消")
        return
    
    # 运行测试
    test_upload_contacts()
    test_existing_user()
    
    print(f"\n" + "="*60)
    print("测试完成！")
    print("="*60)
    
    # 询问是否清理
    cleanup = input(f"\n是否清理测试数据? (y/N): ").lower().strip()
    if cleanup == 'y':
        cleanup_test_data()

if __name__ == "__main__":
    main()
