#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标签提取API功能
"""

import requests
import json

# 测试用的分析报告
SAMPLE_ANALYSIS_REPORT = """
### 1. 候选人心理状态分析
通过分析与候选人 张工程师 的 45 条聊天记录，发现以下职业心理特征：
- **求职心态**: 候选人表现出积极但谨慎的求职态度，对新机会有兴趣但不急躁
- **职业焦虑**: 对当前工作环境有一定不满，希望寻求更好的发展机会
- **沟通风格**: 表达清晰，回复及时，展现出良好的职业素养

### 2. 求职意愿解读
- **跳槽动机**: 主要出于职业发展考虑，希望获得更大的平台和挑战
- **薪资期望**: 期望薪资水平在30-40万之间，更注重长期发展前景
- **时间安排**: 具有一定的紧迫性，但愿意等待合适的机会

### 3. 专业能力评估
- **技术水平**: 具备扎实的Java后端开发基础，有5年工作经验
- **沟通能力**: 表达清晰，逻辑性强，适合团队协作
- **学习态度**: 对新技术和新领域保持开放态度

### 4. 合作建议
- **沟通策略**: 建议采用专业但友好的沟通方式
- **职位匹配**: 适合中高级Java后端开发或技术管理岗位
- **跟进方式**: 保持定期联系，及时反馈职位信息

### 5. 风险评估
- **稳定性**: 中等风险，建议了解其职业规划
- **期望匹配**: 需要确认薪资和职位期望的匹配度
- **竞争情况**: 可能同时在接触其他机会，需要及时跟进

*本分析基于聊天记录内容，仅供参考。建议结合面试和其他评估方式综合判断。*
"""

def test_tag_extraction_api():
    """测试标签提取API"""
    print("🧪 测试标签提取API")
    print("=" * 60)
    
    # API端点
    url = "http://127.0.0.1:8080/api/extract_tags_from_report"
    
    # 请求数据
    data = {
        "analysis_report": SAMPLE_ANALYSIS_REPORT
    }
    
    print(f"📤 发送请求到: {url}")
    print(f"📄 报告长度: {len(SAMPLE_ANALYSIS_REPORT)} 字符")
    
    try:
        # 发送POST请求
        response = requests.post(url, json=data, timeout=30)
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功")
            print(f"📊 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('status') == 'success':
                tags = result.get('tags', [])
                print(f"\n🏷️ 提取的标签:")
                for i, tag in enumerate(tags, 1):
                    print(f"  {i}. {tag}")
                
                print(f"\n📈 标签统计:")
                print(f"  - 总数量: {len(tags)}")
                print(f"  - 平均长度: {sum(len(tag) for tag in tags) / len(tags):.1f} 字符")
                
                # 分析标签类型
                analyze_tag_types(tags)
                
            else:
                print(f"⚠️ 标签提取失败: {result.get('message', '未知错误')}")
        
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def analyze_tag_types(tags):
    """分析标签类型"""
    print(f"\n🔍 标签类型分析:")
    
    # 定义标签类型
    tag_types = {
        "技能类": ["java", "python", "前端", "后端", "开发", "技术"],
        "状态类": ["全职", "兼职", "求职", "主动", "被动"],
        "薪资类": ["万", "k", "薪资", "期望"],
        "意愿类": ["强烈", "中性", "一般", "积极", "谨慎"],
        "特质类": ["沟通", "扎实", "潜力", "经验", "能力"]
    }
    
    type_counts = {type_name: 0 for type_name in tag_types.keys()}
    
    for tag in tags:
        tag_lower = tag.lower()
        for type_name, keywords in tag_types.items():
            if any(keyword in tag_lower for keyword in keywords):
                type_counts[type_name] += 1
                break
    
    for type_name, count in type_counts.items():
        if count > 0:
            print(f"  - {type_name}: {count} 个")

def test_empty_report():
    """测试空报告"""
    print("\n🧪 测试空报告")
    print("-" * 40)
    
    url = "http://127.0.0.1:8080/api/extract_tags_from_report"
    data = {"analysis_report": ""}
    
    try:
        response = requests.post(url, json=data, timeout=10)
        result = response.json()
        print(f"📊 空报告测试结果: {result}")
    except Exception as e:
        print(f"❌ 空报告测试失败: {e}")

def test_short_report():
    """测试短报告"""
    print("\n🧪 测试短报告")
    print("-" * 40)
    
    url = "http://127.0.0.1:8080/api/extract_tags_from_report"
    data = {
        "analysis_report": "候选人是一名Java工程师，有5年经验，期望薪资30万，主动求职。"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        result = response.json()
        print(f"📊 短报告测试结果: {result}")
        
        if result.get('status') == 'success':
            tags = result.get('tags', [])
            print(f"🏷️ 短报告提取标签: {tags}")
    except Exception as e:
        print(f"❌ 短报告测试失败: {e}")

def main():
    """主函数"""
    print("🚀 标签提取API测试开始")
    print("=" * 60)
    
    # 测试完整报告
    test_tag_extraction_api()
    
    # 测试边界情况
    test_empty_report()
    test_short_report()
    
    print("\n🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
