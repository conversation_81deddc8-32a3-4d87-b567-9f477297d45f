{"user": "小罗", "contact_id": "49295", "contact_info": {"UserName": "henryhu_409999109", "Alias": "", "Remark": "49295 <PERSON><PERSON><PERSON>", "NickName": "行者无疆"}, "fetch_time": "2025-07-09T13:22:41.754399", "message_count": 17, "chatlog_data": [{"seq": 1751503573000, "time": "2025-07-03T08:46:13+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 10000, "subType": 0, "content": "你已添加了行者无疆，现在可以开始聊天了。"}, {"seq": 1751504378000, "time": "2025-07-03T08:59:38+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士 您好！我们是专注于高薪挖掘 海外博士/教授/院士的专业国际猎头公司TopTalents Group"}, {"seq": 1751512606000, "time": "2025-07-03T11:16:46+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 1, "subType": 0, "content": "好的 我的专业是神经科学和人工智能方向 以下是我的简介"}, {"seq": 1751512612000, "time": "2025-07-03T11:16:52+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 1, "subType": 0, "content": "同济大学本科和博士，美国密歇根大学博士后，克利夫兰医学中心研究科学家。主要研究感知决策的神经生物学机理及其启发的人工智能，建立了研究感知决策的新体系，发现了增强决策准确性的神经生物学机理，解决了感知决策传统框架内决策速度和准确性不可兼得的难题。提出了多种受大脑认知启发的人工智能算法，提高了多维度数据分析的效能，更填补了能分析多个体间不同交互行为工具的空白，开发的软件被国际同行高度评价并广泛应用。在知名国际期刊（Nature Communications （小修定稿中）、Current Biology、Cell Reports Methods等）发文15篇，7篇为第一或通讯作者。多次在知名国际会议如脑研究冬会等作特邀报告。国际细胞行为分类竞赛2024第二名，密歇根大学卓越研究奖。将拓展对感知决策的神经算法机理的研究，开发碳基脑启发的硅基智能工具箱，并应用于多维度信息的分析。"}, {"seq": 1751512666000, "time": "2025-07-03T11:17:46+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 1, "subType": 0, "content": "请问有什么适合我的工作机会吗？"}, {"seq": 1751512682000, "time": "2025-07-03T11:18:02+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您有比较完整的简历吗？"}, {"seq": 1751512730000, "time": "2025-07-03T11:18:50+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 1, "subType": 0, "content": "有的 明天发给您可以吗？"}, {"seq": 1751512757000, "time": "2025-07-03T11:19:17+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以的 没问题"}, {"seq": 1751512782000, "time": "2025-07-03T11:19:42+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 1, "subType": 0, "content": "好的 谢啦"}, {"seq": 1751601044000, "time": "2025-07-04T11:50:44+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 1, "subType": 0, "content": "这是我的详细简历 谢谢！"}, {"seq": 1751601044001, "time": "2025-07-04T11:50:44+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 49, "subType": 6, "content": "", "contents": {"md5": "4d4eb02e806c9df378ea673397bb880e", "title": "CV_YujiaHu.pdf"}}, {"seq": 1751606297000, "time": "2025-07-04T13:18:17+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到 胡博"}, {"seq": 1751606315000, "time": "2025-07-04T13:18:35+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您这边一般什么时间方便呢？咱们约一个时间语音沟通一下？"}, {"seq": 1751848061000, "time": "2025-07-07T08:27:41+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以的"}, {"seq": 1751857357000, "time": "2025-07-07T11:02:37+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "henryhu_409999109", "senderName": "49295 <PERSON><PERSON><PERSON>", "isSelf": false, "type": 1, "subType": 0, "content": "现在方便吗？"}, {"seq": 1751857473000, "time": "2025-07-07T11:04:33+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "可以的 您稍等一下"}, {"seq": 1751858198000, "time": "2025-07-07T11:16:38+08:00", "talker": "henryhu_409999109", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": ""}]}