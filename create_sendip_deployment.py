#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建SendIp完整部署包
"""

import os
import shutil
import zipfile
from pathlib import Path
from datetime import datetime

def create_sendip_deployment():
    """创建SendIp部署文件夹"""
    deployment_folder = "SendIp_Deployment"
    
    # 如果文件夹已存在，先删除
    if os.path.exists(deployment_folder):
        shutil.rmtree(deployment_folder)
        print(f"🗑️ 删除已存在的部署文件夹: {deployment_folder}")
    
    # 创建新的部署文件夹
    os.makedirs(deployment_folder)
    print(f"📁 创建部署文件夹: {deployment_folder}")
    
    return deployment_folder

def copy_sendip_files(deployment_folder):
    """复制SendIp相关文件到部署文件夹"""
    files_to_copy = [
        ("dist/SendIp.exe", "SendIp.exe"),
        ("dist/config.txt", "config.txt"),
        ("dist/run_sendip.bat", "run_sendip.bat"),
        ("dist/README.md", "README.md")
    ]
    
    copied_files = []
    
    for src, dst in files_to_copy:
        src_path = Path(src)
        dst_path = Path(deployment_folder) / dst
        
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            file_size = dst_path.stat().st_size
            print(f"✅ 复制文件: {src} -> {dst} ({file_size:,} bytes)")
            copied_files.append(dst)
        else:
            print(f"⚠️ 源文件不存在: {src}")
    
    return copied_files

def create_installation_guide(deployment_folder):
    """创建详细的安装和部署指南"""
    guide_content = f"""# SendIp.exe - IP地址监控工具部署指南

## 📦 包含文件

- `SendIp.exe` - 主程序文件 (约19.6MB)
- `config.txt` - 配置文件
- `run_sendip.bat` - 启动脚本
- `README.md` - 使用说明
- `DEPLOYMENT_GUIDE.md` - 本部署指南

## 🚀 快速部署

### 1. 服务器端配置

确保Flask服务器正在运行并包含IP接收接口：

```python
@app.route("/report_ip", methods=["POST"])
def report_ip():
    # 接收IP地址变化报告
    data = request.get_json()
    username = data.get('username')
    ip_address = data.get('ip')
    # 处理IP变化逻辑
    return jsonify({{"status": "success"}})
```

### 2. 客户端部署

**步骤1: 复制文件**
将整个文件夹复制到目标计算机

**步骤2: 配置服务器地址**
编辑 `config.txt` 文件：
```
http://your-server-ip:8080/report_ip
60
your-computer-name
```

**步骤3: 运行程序**
双击 `run_sendip.bat` 或直接运行 `SendIp.exe`

## 📋 配置详解

### config.txt 格式说明

```
# 第一行：服务器地址（必填）
http://*************:8080/report_ip

# 第二行：检查间隔，单位秒（可选，默认60）
60

# 第三行：用户名（可选，为空时会提示输入）
office-computer-01
```

### 服务器地址配置示例

**本地测试：**
```
http://127.0.0.1:8080/report_ip
```

**局域网部署：**
```
http://*************:8080/report_ip
```

**公网部署：**
```
http://your-domain.com:8080/report_ip
```

**HTTPS部署：**
```
https://your-domain.com/report_ip
```

## 🔧 部署场景

### 场景1: 办公室多台电脑监控

1. 在每台电脑上部署SendIp.exe
2. 配置不同的用户名标识每台电脑：
   - `office-pc-01`
   - `office-pc-02` 
   - `office-laptop-01`
3. 所有电脑指向同一个服务器地址

### 场景2: 远程工作人员IP监控

1. 给每个远程工作人员分发SendIp.exe
2. 配置用户名为员工姓名或工号
3. 服务器可以实时监控所有员工的IP变化

### 场景3: 动态IP环境监控

1. 在动态IP环境（如家庭宽带）部署
2. 程序会自动检测IP变化并报告
3. 服务器端自动更新用户文件夹下的JSON文件名

## 📊 工作流程

```
SendIp.exe (客户端)
    ↓ 每60秒检查IP
    ↓ 发现IP变化
    ↓ POST请求到服务器
    ↓
Flask服务器 (/report_ip)
    ↓ 接收IP变化报告
    ↓ 更新用户文件夹
    ↓ 重命名JSON文件
    ↓
data/用户名/新IP_用户名.json
```

## 🐛 故障排除

### 常见问题及解决方案

**1. 程序无法启动**
```
错误: 双击exe没有反应
```
**解决方案:**
- 检查是否有杀毒软件拦截
- 尝试以管理员身份运行
- 检查config.txt文件格式是否正确

**2. 连接服务器失败**
```
错误: 无法连接到服务器
```
**解决方案:**
- 检查服务器地址是否正确
- 确认服务器正在运行
- 检查防火墙设置
- 测试网络连通性: `ping server-ip`

**3. IP检测不准确**
```
问题: 检测到的IP不是期望的IP
```
**解决方案:**
- 程序优先检测公网IP
- 内网环境可能显示内网IP
- 可以通过修改源码调整IP检测逻辑

**4. 用户名输入问题**
```
问题: 每次启动都要输入用户名
```
**解决方案:**
- 在config.txt第三行填写用户名
- 确保config.txt文件编码为UTF-8

### 日志和调试

**查看程序状态:**
- 程序运行后会在系统托盘显示图标
- 右键托盘图标可以查看状态和退出

**服务器端日志:**
```
📡 收到IP地址变化报告
👤 用户名: office-pc-01
🌐 新IP地址: *************
✅ IP地址变化记录已保存
```

## 🔒 安全考虑

### 数据安全
- 程序只发送用户名和IP地址
- 不收集其他敏感信息
- 建议使用HTTPS加密传输

### 网络安全
- 确保服务器端有适当的访问控制
- 考虑添加API认证机制
- 定期更新和维护

### 系统安全
- 程序以普通用户权限运行
- 不修改系统设置
- 可以随时卸载

## 📈 监控和维护

### 服务器端监控
- 访问 `http://server:8080/ip_monitor` 查看所有IP变化
- 定期备份 `ip_changes.json` 文件
- 监控服务器资源使用情况

### 客户端维护
- 定期检查程序是否正常运行
- 更新配置文件（如服务器地址变更）
- 必要时重启程序

## 📞 技术支持

### 系统要求
- Windows 7/8/10/11
- 网络连接
- 至少50MB可用磁盘空间

### 性能指标
- 程序大小: 约19.6MB
- 内存占用: 约10-30MB
- CPU占用: 极低（仅在检查时短暂占用）
- 网络流量: 每次报告约1KB数据

### 联系信息
如需技术支持，请提供：
1. 错误截图或描述
2. config.txt配置内容
3. 服务器地址和状态
4. 操作系统版本

---
**版本信息**
- 程序版本: 1.0.0
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 支持系统: Windows 7+
- 开发语言: Python 3.12 + Tkinter
"""
    
    guide_path = Path(deployment_folder) / "DEPLOYMENT_GUIDE.md"
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"✅ 创建部署指南: {guide_path}")

def create_zip_package(deployment_folder):
    """创建ZIP压缩包"""
    zip_filename = f"{deployment_folder}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(deployment_folder):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, deployment_folder)
                zipf.write(file_path, arcname)
                print(f"📦 添加到ZIP: {arcname}")
    
    zip_size = Path(zip_filename).stat().st_size / (1024 * 1024)  # MB
    print(f"✅ 创建ZIP包: {zip_filename} ({zip_size:.2f} MB)")
    
    return zip_filename

def main():
    """主函数"""
    print("📦 创建SendIp完整部署包")
    print("=" * 60)
    
    # 检查必要文件
    required_files = ["dist/SendIp.exe", "dist/config.txt"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("请先运行 build_sendip_exe.py 生成SendIp.exe")
        return False
    
    # 创建部署文件夹
    deployment_folder = create_sendip_deployment()
    
    # 复制文件
    copied_files = copy_sendip_files(deployment_folder)
    
    # 创建部署指南
    create_installation_guide(deployment_folder)
    
    # 创建ZIP包
    zip_filename = create_zip_package(deployment_folder)
    
    print("\n🎉 SendIp部署包创建完成!")
    print("=" * 60)
    print(f"📁 部署文件夹: {deployment_folder}/")
    print(f"📦 ZIP压缩包: {zip_filename}")
    print(f"📄 包含文件: {len(copied_files) + 1} 个")
    
    print("\n💡 部署步骤:")
    print("1. 将ZIP文件发送到目标计算机")
    print("2. 解压到任意目录")
    print("3. 编辑config.txt设置服务器地址")
    print("4. 双击run_sendip.bat启动程序")
    print("5. 程序将在系统托盘中运行")
    
    print("\n🔧 服务器配置:")
    print("1. 确保Flask服务器包含/report_ip接口")
    print("2. 访问http://server:8080/ip_monitor查看监控页面")
    print("3. 检查data/用户名/文件夹下的JSON文件更新")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            exit(1)
    except Exception as e:
        print(f"💥 创建部署包时出现错误: {e}")
        exit(1)
