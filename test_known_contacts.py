"""
测试已知有聊天记录的联系人
"""

import requests
import re

def test_known_contacts():
    """测试已知联系人"""
    print("🧪 测试已知有聊天记录的联系人...")
    
    base_url = "http://127.0.0.1:5030/api/v1/chatlog"
    time_range = "2023-01-01~2025-07-07"
    
    # 测试已知的联系人
    test_cases = [
        "filehelper",  # 文件传输助手
        "weixin",      # 微信团队
        "小熊",        # 用户自己
        "小司",        # 其他用户
        "小罗",        # 其他用户
    ]
    
    for talker in test_cases:
        print(f"\n🔍 测试: {talker}")
        
        try:
            params = {"time": time_range, "talker": talker}
            response = requests.get(base_url, params=params, timeout=10)
            response.raise_for_status()
            
            chatlog_text = response.text
            
            if chatlog_text and len(chatlog_text.strip()) > 0:
                # 分析参与者
                participant_count, participants = analyze_chatlog_participants(chatlog_text)
                message_lines = len([line for line in chatlog_text.split('\n') if line.strip()])
                
                print(f"    ✅ 有聊天记录: {message_lines} 行, {participant_count} 个参与者")
                print(f"    👥 参与者: {participants}")
                
                if participant_count >= 3:
                    print(f"    🚨 可能是群聊!")
                else:
                    print(f"    👤 个人聊天")
                    
                # 显示前几行内容
                lines = chatlog_text.split('\n')[:3]
                print(f"    📝 内容预览:")
                for line in lines:
                    if line.strip():
                        print(f"       {line.strip()}")
                        
            else:
                print(f"    ⚪ 无聊天记录")
                
        except Exception as e:
            print(f"    ❌ 请求失败: {str(e)}")

def analyze_chatlog_participants(chatlog_text):
    """分析聊天记录中的参与者数量"""
    if not chatlog_text or not isinstance(chatlog_text, str):
        return 0, []
    
    participants = set()
    lines = chatlog_text.strip().split('\n')
    
    # 分析每行聊天记录
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 查找发送者模式：通常是 "发送者名称 时间戳" 或 "发送者名称 日期 时间"
        # 例如: "我 2023-10-14 16:27:26" 或 "张三 2024-01-01 10:30:00"
        sender_match = re.match(r'^([^\s]+)\s+\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', line)
        if sender_match:
            sender = sender_match.group(1)
            participants.add(sender)
        
        # 查找@提及的用户
        mentions = re.findall(r'@([^\s@]+)', line)
        for mention in mentions:
            participants.add(mention)
        
        # 查找群聊中常见的模式，如 "XXX说:" 或 "XXX："
        speaker_match = re.search(r'([^\s]+)[说：:]', line)
        if speaker_match:
            speaker = speaker_match.group(1)
            participants.add(speaker)
    
    # 过滤掉一些明显不是人名的参与者
    filtered_participants = set()
    for participant in participants:
        # 过滤掉太短或明显不是人名的
        if len(participant) >= 1 and not participant.isdigit():
            filtered_participants.add(participant)
    
    return len(filtered_participants), list(filtered_participants)

if __name__ == "__main__":
    test_known_contacts()
