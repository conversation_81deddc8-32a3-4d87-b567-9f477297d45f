@echo off
title 微信联系人监控系统
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    微信联系人监控系统                        ║
echo ║                                                              ║
echo ║  功能: 每天定时2次分析微信联系人新增和重复情况               ║
echo ║  时间: 09:00 和 21:00                                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查必要目录
if not exist "data" (
    echo ❌ 错误: 未找到data目录，请确保data目录存在并包含JSON文件
    pause
    exit /b 1
)

REM 创建必要目录
if not exist "history" mkdir history
if not exist "reports" mkdir reports
if not exist "logs" mkdir logs

REM 安装依赖
echo 📦 正在检查和安装依赖包...
pip install -q -r requirements.txt
if errorlevel 1 (
    echo ⚠️  依赖安装可能有问题，但继续运行...
)

echo.
echo 🚀 启动选项:
echo.
echo   1. 启动定时监控 (推荐) - 后台持续运行，每天自动分析2次
echo   2. 立即分析一次 - 手动运行完整分析
echo   3. 测试新增检测 - 运行功能测试
echo   4. 查看历史报告 - 打开reports目录
echo   5. 安装为Windows服务 - 需要管理员权限
echo   6. 传统重复分析 - 仅分析重复联系人
echo.

set /p choice="请选择 (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🔄 启动定时监控模式...
    echo.
    echo ⏰ 定时计划:
    echo    - 每天 09:00 自动分析
    echo    - 每天 21:00 自动分析
    echo    - 立即运行一次分析
    echo.
    echo 📁 输出目录:
    echo    - 历史状态: history/
    echo    - 分析报告: reports/
    echo    - 程序日志: reports/contact_analyzer.log
    echo.
    echo 💡 提示: 按 Ctrl+C 可以停止监控
    echo.
    pause
    echo.
    echo 🚀 正在启动...
    python find_duplicate_contacts.py --schedule
    
) else if "%choice%"=="2" (
    echo.
    echo 🔍 立即运行分析...
    python find_duplicate_contacts.py --manual
    echo.
    echo ✅ 分析完成！请查看 reports/ 目录中的报告文件
    
) else if "%choice%"=="3" (
    echo.
    echo 🧪 运行新增联系人检测测试...
    python test_new_contacts.py
    
) else if "%choice%"=="4" (
    echo.
    echo 📊 打开报告目录...
    if exist "reports" (
        explorer reports
    ) else (
        echo ⚠️  reports目录不存在，请先运行一次分析
    )
    
) else if "%choice%"=="5" (
    echo.
    echo 🔧 启动服务管理...
    call manage_service.bat
    
) else if "%choice%"=="6" (
    echo.
    echo 🔄 运行传统重复联系人分析...
    python find_duplicate_contacts.py
    
) else (
    echo ❌ 无效选择
)

echo.
echo 📋 系统信息:
echo    - 数据目录: data/
echo    - 历史目录: history/
echo    - 报告目录: reports/
echo    - 当前时间: %date% %time%
echo.

pause
