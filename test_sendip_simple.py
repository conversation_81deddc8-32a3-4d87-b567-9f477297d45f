#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版SendIp测试 - 用于调试IP发送功能
"""

import requests
import time
import json
from datetime import datetime

def log_message(message):
    """记录日志消息"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    
    # 写入日志文件
    try:
        with open('sendip_test.log', 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except:
        pass

def get_public_ip():
    """获取公网IP地址"""
    try:
        log_message("🌐 正在获取公网IP地址...")
        response = requests.get('https://api.ipify.org', timeout=10)
        ip = response.text.strip()
        log_message(f"✅ 获取到IP地址: {ip}")
        return ip
    except Exception as e:
        log_message(f"❌ 获取IP地址失败: {e}")
        return None

def send_ip_to_server(ip, username, server_url):
    """发送IP地址到服务器"""
    try:
        log_message(f"📡 正在发送IP地址到服务器...")
        log_message(f"   用户名: {username}")
        log_message(f"   IP地址: {ip}")
        log_message(f"   服务器: {server_url}")
        
        data = {"username": username, "ip": ip}
        response = requests.post(server_url, json=data, timeout=30)
        
        log_message(f"📊 服务器响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                log_message(f"✅ IP地址发送成功: {result.get('message', '成功')}")
                return True
            except:
                log_message("✅ IP地址发送成功 (无JSON响应)")
                return True
        else:
            log_message(f"❌ 服务器返回错误状态码: {response.status_code}")
            log_message(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        log_message(f"❌ 连接服务器失败: {e}")
        return False
    except requests.exceptions.Timeout as e:
        log_message(f"❌ 请求超时: {e}")
        return False
    except Exception as e:
        log_message(f"❌ 发送IP地址失败: {e}")
        return False

def test_ip_monitoring():
    """测试IP监控功能"""
    server_url = "http://127.0.0.1:8080/report_ip"
    username = "test_computer"
    check_interval = 10  # 10秒检查一次，便于测试
    
    log_message("🚀 开始IP监控测试")
    log_message(f"   用户名: {username}")
    log_message(f"   服务器: {server_url}")
    log_message(f"   检查间隔: {check_interval} 秒")
    
    # 1. 首次获取IP并发送
    log_message("\n=== 步骤1: 首次获取和发送IP ===")
    current_ip = get_public_ip()
    
    if current_ip:
        log_message("📡 首次发送IP地址到服务器...")
        if send_ip_to_server(current_ip, username, server_url):
            log_message("✅ 首次IP发送成功")
        else:
            log_message("❌ 首次IP发送失败")
    else:
        log_message("❌ 无法获取初始IP地址")
        return
    
    # 2. 开始监控循环
    log_message(f"\n=== 步骤2: 开始监控IP变化 ===")
    log_message(f"当前IP: {current_ip}")
    log_message("请在其他终端切换网络或修改IP，观察检测结果...")
    
    check_count = 0
    
    try:
        while True:
            check_count += 1
            log_message(f"\n--- 第 {check_count} 次检查 ---")
            
            new_ip = get_public_ip()
            
            if new_ip:
                if new_ip != current_ip:
                    log_message(f"🔄 检测到IP地址变化!")
                    log_message(f"   旧IP: {current_ip}")
                    log_message(f"   新IP: {new_ip}")
                    
                    if send_ip_to_server(new_ip, username, server_url):
                        current_ip = new_ip
                        log_message("✅ IP地址更新成功")
                    else:
                        log_message("❌ IP地址更新失败，将在下次检查时重试")
                else:
                    log_message(f"✅ IP地址未变化: {new_ip}")
            else:
                log_message("⚠️ 无法获取IP地址，将在下次检查时重试")
            
            log_message(f"⏰ 等待 {check_interval} 秒后进行下次检查...")
            time.sleep(check_interval)
            
    except KeyboardInterrupt:
        log_message("\n⚠️ 用户中断监控")
    except Exception as e:
        log_message(f"\n❌ 监控过程中出现错误: {e}")

def test_single_send():
    """测试单次发送功能"""
    server_url = "http://127.0.0.1:8080/report_ip"
    username = "test_single"
    
    log_message("🧪 测试单次IP发送功能")
    
    # 获取IP
    ip = get_public_ip()
    if not ip:
        log_message("❌ 无法获取IP地址，测试失败")
        return False
    
    # 发送到服务器
    success = send_ip_to_server(ip, username, server_url)
    
    if success:
        log_message("🎉 单次发送测试成功!")
        return True
    else:
        log_message("💥 单次发送测试失败!")
        return False

def main():
    """主函数"""
    print("🚀 SendIp功能测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试模式:")
        print("1. 单次发送测试")
        print("2. 持续监控测试")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            print("\n" + "=" * 50)
            test_single_send()
        elif choice == '2':
            print("\n" + "=" * 50)
            print("注意: 持续监控测试将一直运行，按 Ctrl+C 停止")
            input("按回车键开始...")
            test_ip_monitoring()
        elif choice == '3':
            print("👋 退出测试")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
