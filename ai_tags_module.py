#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI标签提取模块
"""

from volcenginesdkarkruntime import Ark
from datetime import datetime, time
import re

# 豆包AI配置
DOUBAO_API_KEY = "cdd675a2-53d4-4aa1-bba8-33340ada088f"
DOUBAO_MODEL = "doubao-seed-1.6-250615"

def init_doubao_client():
    """初始化豆包客户端"""
    try:
        client = Ark(api_key=DOUBAO_API_KEY)
        return client
    except Exception as e:
        print(f"❌ 豆包客户端初始化失败: {e}")
        return None

def extract_nickname_tags_with_ai(nickname):
    """使用AI分析昵称中的地区和头衔信息"""
    client = init_doubao_client()
    if not client or not nickname:
        return []
    
    try:
        prompt = f"""
作为一个文本分析助手，请帮我从以下用户昵称中提取关键信息标签。

用户昵称: {nickname}

请提取以下类型的信息标签：
- 技术技能：如Java、Python、前端、后端等
- 职业角色：如工程师、经理、设计师等  
- 工作经验：如1年、3年、5年等
- 地理位置：如北京、上海、深圳等城市名
- 教育背景：如教授、博士、研究员等学术称谓
- 特殊头衔：如长江学者、青千、优青等

输出格式：
- 只输出找到的标签，用逗号分隔
- 如果没有找到相关信息，输出"无标签"
- 不要输出解释或其他内容

示例输出：Java,后端,北京,5年经验
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,
        )
        
        ai_result = response.choices[0].message.content.strip()
        
        if ai_result and ai_result != "无标签":
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析昵称 '{nickname}' 提取标签: {tags}")
            return tags
        
        return []
        
    except Exception as e:
        print(f"❌ AI昵称分析失败: {e}")
        return []

def extract_conversation_tags_with_ai(content):
    """使用AI分析聊天内容提取标签"""
    client = init_doubao_client()
    if not client or not content:
        return []
    
    try:
        prompt = f"""
作为一个职业信息分析助手，请从以下对话内容中提取职业相关的标签信息。

对话内容：
{content[:1200]}

请提取以下类型的标签：
- 专业技能：如Python、Java、React、设计、产品等
- 工作类型：如全职、兼职、远程等
- 求职状态：如主动求职、被动求职等
- 薪资期望：如10-20万、20-30万等
- 工作意愿：如意愿强烈、意愿一般等
- 其他特征：如可推荐朋友、有作品集等

输出要求：
- 只输出确实能从内容中提取的标签
- 用逗号分隔多个标签
- 标签要简洁明确
- 如果没有相关信息，输出"无相关标签"

示例输出：Python,后端开发,全职,主动求职,30-50万,意愿强烈
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.3,
        )
        
        ai_result = response.choices[0].message.content.strip()
        
        if ai_result and ai_result != "无相关标签":
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析聊天内容提取标签: {tags}")
            return tags
        
        return []
        
    except Exception as e:
        print(f"❌ AI聊天内容分析失败: {e}")
        return []

def generate_simple_tags_enhanced(contact_info, messages=None):
    """生成简洁的标签用于聊天记录页面显示（增强版）"""
    tags = []
    
    # 1. 从昵称中提取标签（AI + 基础匹配）
    nickname = contact_info.get('remark', '')
    if nickname:
        # AI分析昵称
        ai_nickname_tags = extract_nickname_tags_with_ai(nickname)
        tags.extend(ai_nickname_tags)
        
        # 基础关键词匹配作为补充
        basic_nickname_tags = extract_basic_nickname_tags(nickname)
        tags.extend(basic_nickname_tags)
    
    if messages:
        # 2. 整合所有消息内容
        integrated_content = integrate_all_message_content(messages)
        
        # 3. AI分析聊天内容
        all_text = integrated_content.get('text_content', '')
        if all_text:
            ai_conversation_tags = extract_conversation_tags_with_ai(all_text)
            tags.extend(ai_conversation_tags)
        
        # 4. 本地分析标签
        local_tags = extract_local_analysis_tags(messages, integrated_content)
        tags.extend(local_tags)
    else:
        tags.append("无聊天记录")
    
    # 去重并保持顺序
    seen = set()
    unique_tags = []
    for tag in tags:
        if tag not in seen:
            seen.add(tag)
            unique_tags.append(tag)
    
    # 限制标签数量，只显示最重要的8个
    return unique_tags[:8]

def extract_basic_nickname_tags(nickname):
    """基础关键词匹配提取昵称标签"""
    tags = []
    nickname_lower = nickname.lower()
    
    # 技术栈关键词
    tech_keywords = {
        "Java": ["java"],
        "Python": ["python", "py"],
        "前端": ["前端", "fe", "frontend"],
        "后端": ["后端", "be", "backend"],
        "AI": ["ai", "算法"],
        "数据": ["数据", "data"]
    }
    
    for tech, keywords in tech_keywords.items():
        if any(keyword in nickname_lower for keyword in keywords):
            tags.append(tech)
    
    return tags

def integrate_all_message_content(messages):
    """整合所有文本消息和附件消息内容"""
    integrated_content = {
        'text_content': '',
        'image_count': 0,
        'voice_count': 0,
        'file_count': 0,
        'call_count': 0
    }
    
    text_parts = []
    
    for msg in messages:
        content = msg.get('content', '')
        msg_type = msg.get('type', 'text')
        
        if msg_type == 'text':
            text_parts.append(content)
            # 检查通话信息
            if any(call_keyword in content for call_keyword in ['通话时长', '语音通话', '视频通话']):
                integrated_content['call_count'] += 1
        elif msg_type == 'attachment':
            attachment_type = msg.get('attachment_type', '')
            if attachment_type == 'image':
                integrated_content['image_count'] += 1
            elif attachment_type == 'voice':
                integrated_content['voice_count'] += 1
            elif attachment_type == 'file':
                integrated_content['file_count'] += 1
    
    integrated_content['text_content'] = ' '.join(text_parts)
    return integrated_content

def extract_local_analysis_tags(messages, integrated_content):
    """本地分析提取标签"""
    tags = []
    
    # 活跃度分析
    message_count = len(messages)
    if message_count > 100:
        tags.append("高活跃")
    elif message_count > 50:
        tags.append("活跃")
    elif message_count > 20:
        tags.append("中等活跃")
    else:
        tags.append("低活跃")
    
    # 通话分析
    call_count = integrated_content.get('call_count', 0)
    if call_count >= 3:
        tags.append("通话频繁")
    elif call_count >= 1:
        tags.append("有通话记录")
    else:
        tags.append("无通话记录")
    
    # 多媒体内容
    if integrated_content.get('image_count', 0) > 0:
        tags.append("有图片")
    if integrated_content.get('voice_count', 0) > 0:
        tags.append("有语音")
    if integrated_content.get('file_count', 0) > 0:
        tags.append("有文件")
    
    return tags
