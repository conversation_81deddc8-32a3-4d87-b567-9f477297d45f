# 微信联系人获取GUI工具

这是一个图形界面工具，用于获取微信联系人信息并提交用户名。

## 功能特性

- 🖥️ 友好的图形用户界面
- 📱 获取所有微信联系人信息
- 👤 用户名提交功能
- 📊 实时操作日志显示
- 💾 日志保存功能
- ⚙️ 服务器配置管理

## 安装和运行

### 方法1: 使用批处理文件（推荐）
```bash
# 双击运行
run_gui.bat
```

### 方法2: 手动运行
```bash
# 安装依赖
pip install -r requirements.txt

# 运行GUI程序
python wechat_gui.py
```

## 使用说明

### 1. 启动程序
- 双击 `run_gui.bat` 或运行 `python wechat_gui.py`
- 程序会打开一个图形界面窗口

### 2. 配置服务器信息
- **服务器地址**: 默认为 `127.0.0.1:8080`（本地服务器）
- **MCP端口**: 默认为 `5030`（微信MCP服务端口）
- 根据实际情况修改这些配置

### 3. 提交用户名
- 在"用户名"输入框中输入您的用户名
- 点击"提交用户名"按钮确认

### 4. 获取微信联系人
- 确保微信MCP服务正在运行（端口5030）
- 确保Flask服务器正在运行（端口8080）
- 点击"获取所有微信联系人"按钮
- 程序会自动：
  - 从MCP服务获取联系人数据
  - 解析CSV格式数据
  - 上传到服务器保存
  - 显示操作结果

### 5. 查看日志
- 所有操作都会在"操作日志"区域显示
- 可以使用"清空日志"按钮清空日志
- 可以使用"保存日志"按钮将日志保存到文件

## 界面说明

### 用户信息区域
- **用户名输入框**: 输入当前用户的用户名
- **提交用户名按钮**: 确认提交用户名

### 服务器配置区域
- **服务器地址**: Flask服务器的地址和端口
- **客户端IP**: 当前客户端的IP地址（用于生成文件名）
- **自动检测IP按钮**: 自动检测当前客户端的IP地址
- **MCP端口**: 微信MCP服务的端口号

### 操作按钮区域
- **获取所有微信联系人**: 主要功能按钮，获取并上传联系人信息
- **清空日志**: 清空操作日志显示
- **保存日志**: 将当前日志保存到文件

### 操作日志区域
- 显示所有操作的详细日志
- 包含时间戳和操作结果
- 支持滚动查看历史记录

## 工作流程

1. **启动服务**: 确保Flask服务器和微信MCP服务都在运行
2. **配置信息**: 设置正确的服务器地址和端口
3. **检测IP**: 程序会自动检测客户端IP，也可以手动输入或重新检测
4. **输入用户名**: 在界面中输入用户名并提交（**重要**：这个用户名将用于生成文件名）
5. **获取联系人**: 点击获取按钮，程序会：
   - 检查用户名和客户端IP是否已设置
   - 直接连接到MCP服务获取联系人CSV数据
   - 解析联系人数据
   - 使用"客户端IP_用户名"格式生成客户端ID
   - 将数据上传到Flask服务器保存为JSON文件
   - 在界面显示操作结果

## 文件命名规则

- **网页版**: 自动检测用户信息，格式为 `客户端IP_用户_检测到的昵称.json`
- **GUI客户端版**: 使用客户端IP和用户提交的用户名，格式为 `客户端IP_用户名.json`

例如：
- 网页版: `192_168_1_100_用户_小明.json`
- GUI版: `192_168_1_100_张三.json`

这样可以让GUI客户端用户自定义用户名，同时使用真实的客户端IP地址，而网页版保持自动检测的便利性。

## 错误处理

程序包含完善的错误处理机制：
- 网络连接错误
- 服务器响应错误
- 数据解析错误
- 文件操作错误

所有错误都会在日志中显示详细信息。

## 注意事项

1. **服务依赖**: 需要Flask服务器和微信MCP服务同时运行
2. **网络连接**: 确保网络连接正常
3. **端口占用**: 确保配置的端口没有被其他程序占用
4. **权限问题**: 确保程序有读写文件的权限

## 技术特性

- 使用tkinter构建GUI界面
- 多线程处理，避免界面冻结
- 完整的日志记录系统
- 自动错误恢复机制
- 用户友好的提示信息

## 文件说明

- `wechat_gui.py`: 主程序文件
- `run_gui.bat`: Windows启动脚本
- `requirements.txt`: Python依赖包列表
- `GUI_README.md`: 本说明文件

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python是否正确安装
   - 运行 `pip install -r requirements.txt` 安装依赖

2. **无法获取联系人**
   - 检查Flask服务器是否运行在8080端口
   - 检查微信MCP服务是否运行在5030端口
   - 检查网络连接是否正常

3. **界面显示异常**
   - 尝试重新启动程序
   - 检查屏幕分辨率设置

如有其他问题，请查看操作日志中的详细错误信息。
