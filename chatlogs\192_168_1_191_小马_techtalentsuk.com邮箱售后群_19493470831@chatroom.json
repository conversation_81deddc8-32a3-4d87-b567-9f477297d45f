boss(iray1991) 2023-04-17 15:14:40
@网易客服－夏夏 

boss(iray1991) 2023-04-17 15:14:48
公司企业邮箱是网易邮箱，如何关闭员工账号的群发功能？

boss(iray1991) 2023-04-17 15:15:05
或者设置只允许群发单显 不允许普通的群发

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:17:05
这个无法关闭哈

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:17:10
群发是基础功能哈

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:17:27
群发单显也是群发，只是发送方式不一样而已

杨海雪(wxid_q6idn7wohxat11) 2023-04-17 15:21:29
> 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> 这个无法关闭哈
那有没有提醒功能呢？比如要群发的时候，跳出来再让发件人确认一遍？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:27:31
> [引用]
可以，管理员后台有审核功能，比如发邮件，收件人超过两个要另一个人同意后才能发出去

boss(iray1991) 2023-04-17 15:29:34
如何设置？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:30:51
管理员后台 您登录下

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:30:54
搜邮件审核

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:31:21
新建规则即可

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:31:40
![图片](http://*************:5030/image/346e3b4aac32f33cb101a6069df3ef89)

boss(iray1991) 2023-04-17 15:36:40
> 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> ![图片](http://*************:5030/image/1b7c60e0d543f28c9bf4f2586d84550b)
这个左边显示下

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:41:27
![图片](http://*************:5030/image/6f0592ba905463cd4733d3e5ab5abe12)

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:41:45
![图片](http://*************:5030/image/0fab97384eaba2926476d38b21f307d4)

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:41:48
直接搜就行

boss(iray1991) 2023-04-17 15:42:39
![图片](http://*************:5030/image/e22ec433c20cfa88b2a57e903c8558c3)

boss(iray1991) 2023-04-17 15:42:44
没有邮件审核

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:43:58
退出重新登录看看

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 15:44:00
刷新下

boss(iray1991) 2023-04-17 18:44:21
SMTP无法绑定境外的客户端

boss(iray1991) 2023-04-17 18:44:23
咋处理

boss(iray1991) 2023-04-17 18:44:30
好像是因为开了二次验证

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 19:09:21
一般客户端软件不支持二次验证的，客户端支持的都是有网易旗下的客户端

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 19:09:41
或者取消验证码登录

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-17 19:10:39
管理员解绑员工手机：
登陆网页端 mail.qiye.163.com，选择管理员登陆：
点账号管理----找到要解绑的那个员工邮箱----点开----鼠标下滑到最下边--安全模式--选择自定义模式，勾掉二次验证即可，如果已经绑定了手机，在手机号傍边有“解绑”---点击解绑（关闭验证）--保存即可

boss(iray1991) 2023-04-18 15:14:11
为保障贵司信息安全，管理员帐号必须开启二次登录验证。

boss(iray1991) 2023-04-18 15:14:13
@网易客服－夏夏 

boss(iray1991) 2023-04-18 15:14:32
管理员是***********************，我这边在给**********************设置取消二次验证的

boss(iray1991) 2023-04-18 15:14:33
时候

boss(iray1991) 2023-04-18 15:14:42
跳出来不让取消

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:17:05
> Reaching(iray1991) 01-01 08:00:00
> 管理员是***********************，我这边在给**********************设置取消二次验证的
info给授权管理员的权限了？

boss(iray1991) 2023-04-18 15:20:41
> 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> 或者取消验证码登录
取消了 在一个客户端授权的时候还是提示无法授权

boss(iray1991) 2023-04-18 15:20:51
Error while processing the request! Outgoing Authentication <NAME_EMAIL>


boss(iray1991) 2023-04-18 15:20:54
咋处理

boss(iray1991) 2023-04-18 15:21:16
SMTP User Name
SMTP Password

boss(iray1991) 2023-04-18 15:21:23
这个是不是就是登陆密码

boss(iray1991) 2023-04-18 15:21:27
还是有另外一套

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:22:43
> Reaching(iray1991) 01-01 08:00:00
> SMTP User Name
> SMTP Password
smtp账号密码不知道是啥。。。

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:22:54
我看下后台

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:22:56
稍等下

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:27:07
您登录的时候，输入的是授权码，还是邮箱密码

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:27:17
您info的账号开了授权码的

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:27:25
手机也绑定了，只是没开验证码而已

boss(iray1991) 2023-04-18 15:27:31
授权码

boss(iray1991) 2023-04-18 15:27:38
要密码还是授权码？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:28:40
您开了授权码，就是生成授权码做密码，不能用邮箱密码

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:28:55
还有一个问题，info您是不是设置了登录ip限制，只能在xx城市登录

boss(iray1991) 2023-04-18 15:29:07

处理请求时出错！**********************的传出身份认证失败。

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:29:09
您登录软件归属地是在允许范围么

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:29:36
> 网易客服－夏夏(19493470831@chatroom) 01-01 08:00:00
> 还有一个问题，info您是不是设置了登录ip限制，只能在xx城市登录
看下这个问题~

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:29:50
![图片](http://*************:5030/image/ba195b529f7e493ef4b209689649db9e)

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:29:54
在黑名单里的

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:30:03
还有四川的ip  也是在黑名单的

boss(iray1991) 2023-04-18 15:30:15
ok

boss(iray1991) 2023-04-18 15:30:17
现在可以了

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:30:23
嗯嗯

boss(iray1991) 2023-04-18 15:30:29
验证后可以开启二次验证吗？

boss(iray1991) 2023-04-18 15:30:37
如果已经验证成功后

boss(iray1991) 2023-04-18 15:30:44
能不能开启二次验证和ip验证

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:30:49
软件本身我不太清楚，邮箱是可以开验证的

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:31:12
ip验证邮箱没有这个功能，只有登录限制 ip或城市等

boss(iray1991) 2023-04-18 15:31:45
现在能看到软件是在哪里登录的吗

boss(iray1991) 2023-04-18 15:32:04
哪里能看到尝试登陆的ip

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:32:06
因为您是绑定的境外的客户端，软件不是我们的哈，我们无法查询软件本身的使用信息哈

boss(iray1991) 2023-04-18 15:32:29
> 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> ![图片](http://*************:5030/image/81cb8d5c496dbc18da81b1072e825fb7)
这个在哪里看到的ip

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:33:13
> Reaching(iray1991) 01-01 08:00:00
> 哪里能看到尝试登陆的ip
网易邮箱的网页端，网址，mail.qiye.163.com  输入账号密码后，点自助查询，登录查询。
2、管理员后台有 数据分析，员工的登录查询日志是可以导出的哈

boss(iray1991) 2023-04-18 15:38:59
![图片](http://*************:5030/image/a8d311c0ca57351e5a794d53186deae2)

boss(iray1991) 2023-04-18 15:39:06
我这里怎么没有你给的那个网址

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:39:29
我查的是失败的记录

boss(iray1991) 2023-04-18 15:39:59
在哪里查失败记录

boss(iray1991) 2023-04-18 15:40:31
现在相当于是我授权了第三方的SMTP，如果我现在重新开通IP限制和二次验证，是不是这个SMTP就会失效

boss(iray1991) 2023-04-18 15:42:59
@网易客服－夏夏 

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:45:25
> Reaching(iray1991) 01-01 08:00:00
> 现在相当于是我授权了第三方的SMTP，如果我现在重新开通IP限制和二次验证，是不是这个SMTP就会失效
我不太明白您说smtp这个是具体指那块，对企业邮箱来说，这个就是发信服务器~~关于ip限制这块：若您目前使用的软件 所触发的ip 不在您邮箱管理员后台允许的限制里，则邮箱就用不了。不管是什么软件或者登录场合都是一样的。这个ip登录限制 是指：邮箱的登录限制，不限制某一个客户端。

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:46:08
二次验证这个是 一般指 网页端登录邮箱，需要一个6位数的验证码。 一般软件都不支持二次验证的，若支持 您输入6位验证码即可

boss(iray1991) 2023-04-18 15:46:40
在哪里查失败记录

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:49:02
> Reaching(iray1991) 01-01 08:00:00
> 在哪里查失败记录
失败的管理员后台目前无法查询，可在具体账号的自主查询里查询

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 15:51:05
失败的不在显示

boss(iray1991) 2023-04-18 16:01:31
![图片](http://*************:5030/image/b1cd3c41f2cb6fe9b8ba28b4952b3097)

boss(iray1991) 2023-04-18 16:01:39
具体账号里面也查不到哇

boss(iray1991) 2023-04-18 16:01:53
@网易客服－夏夏 

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 16:04:02
最新的还没刷新出来

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 16:04:04
@Reaching 

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 16:04:11
您这个账号有上千条错误记录

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 16:04:41
不能每行都显示哈，客户端登录的 最后一次登录后 会统一在 客户端登录里

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 16:04:49
[文件|<EMAIL> 邮箱登录失败记录.xlsx](http://*************:5030/file/650381a7d5b4ae1c68786151ba92bd6e)

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-04-18 16:04:52
我导出给您

boss(iray1991) 2023-05-08 17:23:06
登录失败, 请检查账号名或密码，或按照邮件服务商要求使用授权码登录[ERR.LOGIN.IPDENY]

boss(iray1991) 2023-05-08 17:23:10
@网易客服－夏夏 

boss(iray1991) 2023-05-08 17:23:13
这个看看咋处理

boss(iray1991) 2023-05-08 17:23:16
用的授权码

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:23:36
ip在黑名单里

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:23:43
您看下是不是 后台设置了登录限制

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:23:49
您本地的网络不在允许范围内

boss(iray1991) 2023-05-08 17:24:02
后台能看到这个具体是哪里的ip吗

boss(iray1991) 2023-05-08 17:24:06
我这边能看到吗

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:24:47
您账号是哪个

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:24:49
我帮您看下

boss(iray1991) 2023-05-08 17:24:53
<EMAIL>

boss(iray1991) 2023-05-08 17:24:57
我们自己后台看不了？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:26:48
![图片](http://*************:5030/image/1ac79dadd28839ed36471b940b79ce51)

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:27:33
这个ip不在范围哈

boss(iray1991) 2023-05-08 17:35:08
我们自己后台看不了？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-08 17:35:52
是的~~

boss(iray1991) 2023-05-31 15:21:56
1

boss(iray1991) 2023-05-31 15:22:06
@网易客服－夏夏 

boss(iray1991) 2023-05-31 15:22:10
So, our Mail team suggested you to share the below details with your email service provider (forwarding mailbox provider) to check the delivery status from their end. Since the emails have not been notified to our server from the respective mail server, then the delivery logs have to be investigated from their end. 

Logs:
Message-Id: <<EMAIL>>
From: Mohammad Ahammad <<EMAIL>>
To: "'<EMAIL>'" <<EMAIL>>
Subject: RE: Your application to Visiting Professor
Thread-Topic: Your application to Visiting Professor
Thread-Index: AQHZkf/A4fRpWChRCUuC5Qjb19RI0a9xbTMA
Date: Mon, 29 May 2023 17:09:48 +0000
References:
<<EMAIL>>
In-Reply-To:
<<EMAIL>>

Kindly share the above details with your email service provider (mail provider of this <NAME_EMAIL>) and request them to check and fix this issue. 

boss(iray1991) 2023-05-31 15:22:28
因此，我们的邮件团队建议您与您的电子邮件服务提供商(转发邮箱提供商)共享以下详细信息，以检查其结束时的发送状态。由于电子邮件尚未从各自的邮件服务器通知我们的服务器，那么发送日志必须从其结束时进行调查。

日志：
邮件ID：<<EMAIL>>
出发地：Mohammad Ahammad<<EMAIL>>
致：“info@tech talentsuk.com”<<EMAIL>>
题目：Re：你对客座教授的申请
主题：你对客座教授的申请
线程索引：AQHZkf/A4fRpWChRCUC5Qjb19RI0a9xbTMA
日期：2023年5月29日星期一17：09：48+0000
参考资料：
<<EMAIL>>
答复：
<<EMAIL>>

请与您的电子邮件服务提供商(此电子邮件地址的邮件提供商**********************)分享上述详细信息，并请他们检查并修复此问题。

boss(iray1991) 2023-05-31 15:22:30
麻烦看下

boss(iray1991) 2023-05-31 15:23:53
![图片](http://*************:5030/image/2a4845bb4a5acb9df48327d612840c38)

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-31 15:23:59
我没太看明白这个意思~~是您给对方发信？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-31 15:24:33
好的

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-31 15:25:09
截图收件人和主题 您复制发我下

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-31 15:25:11
@Reaching 

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-31 15:25:45
发件人是：<EMAIL> 么？

boss(iray1991) 2023-05-31 15:25:51
@对

boss(iray1991) 2023-05-31 15:25:55
另外一个群的信息你看下

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-31 15:26:06
好

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-05-31 16:29:53
@You'll 

boss(iray1991) 2023-06-21 16:45:17
![图片](http://*************:5030/image/cae5fd626a08dfc27fa233b7403455c7)

boss(iray1991) 2023-06-21 16:45:23
@网易客服－夏夏 提示pop threshold

boss(iray1991) 2023-06-21 16:45:27
麻烦看下

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-06-21 16:47:14
![图片](http://*************:5030/image/4b1ec56a53171c9ccb076915c2dce59f)

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-06-21 16:47:23
@Reaching 账号发下

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-06-21 16:47:37
我开车哈

boss(iray1991) 2023-06-21 16:49:06
<EMAIL>

网易客服_汐汐(wxid_rcl0vwipdtyk22) 2023-06-21 16:50:16
这个账号具体什么问题？

网易客服_汐汐(wxid_rcl0vwipdtyk22) 2023-06-21 16:50:25
后台报错是密码不对

boss(iray1991) 2023-06-21 16:50:30
Pop Threshold

boss(iray1991) 2023-06-21 16:50:39
在另外的客户端 无法同步邮件过去

凌霜侯(wxid_rg6oypkcz07f22) 2023-06-21 16:50:48
这个客户端不清楚，你设置截图下

网易客服_汐汐(wxid_rcl0vwipdtyk22) 2023-06-21 16:51:23
 服务器配置的位置我们看下哈~

boss(iray1991) 2023-06-21 16:52:54
![图片](http://*************:5030/image/ee35a158a124f4782661a06bd2087d0c)

网易客服_汐汐(wxid_rcl0vwipdtyk22) 2023-06-21 16:53:45
@Reaching  您现在在北京吗？

boss(iray1991) 2023-06-21 16:54:27
不在

网易客服_汐汐(wxid_rcl0vwipdtyk22) 2023-06-21 16:55:15
服务器配置是没有问题的哈~ 

凌霜侯(wxid_rg6oypkcz07f22) 2023-06-21 16:55:27
mail.qiye.163.com 网页版输入账号密码登录，看下是否正常

boss(iray1991) 2023-06-21 16:56:29
没问题

凌霜侯(wxid_rg6oypkcz07f22) 2023-06-21 16:56:50
那就是这个客户端的问题了

boss(iray1991) 2023-07-18 11:17:15
v=spf1 include:transmail.net ~all
 
No SPF record belonging to zeptomail found

boss(iray1991) 2023-07-18 11:17:24
@You'll DNS后台添加了这个记录

boss(iray1991) 2023-07-18 11:17:32
但是客户端还是显示没有

boss(iray1991) 2023-07-18 11:17:34
咋处理

boss(iray1991) 2023-07-18 11:17:51
![图片](http://*************:5030/image/b60da008272c512f858487a85693c781)

boss(iray1991) 2023-07-18 11:17:59
这条记录有2个 一个是163的 是否有冲突

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:18:07
> Reaching(iray1991) 01-01 08:00:00
> v=spf1 include:transmail.net ~all
>  
> No SPF record belonging to zeptomail found
添加错了

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:18:12
> Reaching(iray1991) 01-01 08:00:00
> 这条记录有2个 一个是163的 是否有冲突
有冲突

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:18:14
只能存在一个

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:18:25
![图片](http://*************:5030/image/aba694ac79c587bd0904a33ecdefcdf5)

boss(iray1991) 2023-07-18 11:21:38
我这边现在是用sendgrid这类邮件营销系统

boss(iray1991) 2023-07-18 11:21:56
等于说用了163的邮箱 就不能用其他邮件营销系统了？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:22:24
> Reaching(iray1991) 01-01 08:00:00
> 等于说用了163的邮箱 就不能用其他邮件营销系统了？
行，但是条解析存在 肯定是有冲突的

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:22:40
不然邮件需要检测spf的时候，对方检测哪个对吧？

boss(iray1991) 2023-07-18 11:22:40
有没有其他变通办法？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:22:43
都是在后台的

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:23:06
一个域名都是只能绑定一个解析的~~

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:23:58
您问下营销系统的后台人员，他们这个spf解析能否不做

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:24:09
或者主机值 不写@    

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:24:37
还有一个问题哈，您影响系统用的哪个域名 techtalentsuk.com 也是这个么？

boss(iray1991) 2023-07-18 11:28:14
> 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> 还有一个问题哈，您影响系统用的哪个域名 techtalentsuk.com 也是这个么？
啥意思？

boss(iray1991) 2023-07-18 11:28:27
> 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> 或者主机值 不写@    
不写这个的话 还能写啥你

boss(iray1991) 2023-07-18 11:28:30
啥呢

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:28:59
> Reaching(iray1991) 01-01 08:00:00
> > 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> > 还有一个问题哈，您影响系统用的哪个域名 techtalentsuk.com 也是这个么？
> 啥意思？
您营销系统用的哪个域名发信， techtalentsuk.com 也是这个么？

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:29:10
> Reaching(iray1991) 01-01 08:00:00
> > 网易客服－夏夏(wxid_m6zcd4crcml222) 01-01 08:00:00
> > 或者主机值 不写@    
> 不写这个的话 还能写啥你
需要问问让您添加这个解析的那边人~

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:29:15
问下一定要添加的的么

网易客服－夏夏(wxid_m6zcd4crcml222) 2023-07-18 11:29:22
域名只能存在一个spf解析的

boss(iray1991) 2023-08-09 13:48:17
1

You'll(yang_Yis) 2025-04-22 17:56:40
@Reaching 邮箱有什么问题在这边反馈

boss(iray1991) 2025-04-22 18:02:55
https://feedback.qiye.163.com/static/feedback/#/bounce/ABFOB3cCKFdyUl9acUEqdQYGHjVrfVNyPBoVXkA7GR1YCg%3D%3D

boss(iray1991) 2025-04-22 18:02:59
![图片](http://*************:5030/image/2afd5b2f838bfd4acd9d18a43715f207)

boss(iray1991) 2025-04-22 18:13:20
@📵 @You'll @网易客服－夏夏 @网易客服_汐汐 @凌霜侯 

网易客服_汐汐(wxid_rcl0vwipdtyk22) 2025-04-22 18:14:02
发的广告邮件？

boss(iray1991) 2025-04-22 18:27:24
不是

boss(iray1991) 2025-04-22 18:27:28
是通知邮件

boss(iray1991) 2025-04-22 18:27:39
群发通知

boss(iray1991) 2025-04-22 18:36:03
@网易客服_汐汐 

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-22 19:03:00
对方有过往来的么？

boss(iray1991) 2025-04-22 19:13:15
大部分有

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-22 19:14:53
您公司发推广信么？就您反上边反馈的这个账号

boss(iray1991) 2025-04-22 19:18:16
不是推广信

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-22 19:23:52
我知道，就是您这个账号有发过么？看您刚刚说大部分有往来，那没有往来的 是您主动发的什么类型邮件

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-22 19:24:12
> Reaching(iray1991) 04-22 18:02:59
> ![图片](http://*************:5030/image/c4c1f5926330d62bfb475fd22dbd8547)
这个您发的邮件内容截图我看下

boss(iray1991) 2025-04-22 19:25:08
![图片](http://*************:5030/image/2d6f2e4196b20ad02350296706fe1896)

boss(iray1991) 2025-04-22 19:25:14
内容

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-22 19:57:27
收件人地址是你们客户自己提供的？

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-22 20:01:28
> Reaching(iray1991) 04-22 18:02:59
> ![图片](http://*************:5030/image/c4c1f5926330d62bfb475fd22dbd8547)
发给gmail的邮件内容一样的？

boss(iray1991) 2025-04-23 16:02:05
> 网易客服－夏夏(wxid_m6zcd4crcml222) 04-22 19:57:27
> 收件人地址是你们客户自己提供的？
对

boss(iray1991) 2025-04-23 16:02:16
gmail是一个通知 模板一样 就是改了姓名

boss(iray1991) 2025-04-23 16:02:18
@网易客服－夏夏 

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-23 16:03:01
主题改改 重新发看看

boss(iray1991) 2025-04-23 16:03:18
主题如何改

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-23 16:03:24
您这种容易被邮箱的服务器检测是垃圾邮件。 不管是对方还是自己

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-23 16:03:42
> Reaching(iray1991) 04-23 16:03:18
> 主题如何改
您根据实际情况改，我不知道您公司业务。。。。

boss(iray1991) 2025-04-23 18:19:37
![图片](http://*************:5030/image/81c2fe0ca9edf8d6a8cb5a54f3b9efb2)

boss(iray1991) 2025-04-23 18:19:39
@网易客服－夏夏 

boss(iray1991) 2025-04-23 18:19:43
这种如何群发？

boss(iray1991) 2025-04-23 18:20:08
好像群发单显和动态群发都不行？


boss(iray1991) 2025-04-23 18:20:28
![图片](http://*************:5030/image/e4e9ad1868dbc429d998a6d1b8b386aa)

boss(iray1991) 2025-04-23 18:20:43
几个问题
1 主题好像无法动态？
2 正文内容格式全没了

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-23 18:44:51
您这种群发过于像垃圾邮件类型。您把退信截图下，然后您发的邮件内容也截图下。您确认是收件人对方主动给您公司的邮箱地址。发邮件到***************** 就说被检测是垃圾邮件。 

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-23 18:44:58
会有安全部门查询

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-24 08:55:49
> Reaching(iray1991) 04-23 18:20:08
> 好像群发单显和动态群发都不行？
群发单显就是字面的意思 群发后单独显示，没有别的

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-24 08:57:29
动态群发 是有固定模版的 不是您自己编辑格式

boss(iray1991) 2025-04-24 08:58:07
动态群发不能自己编辑格式？

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-24 09:02:03
![图片](http://*************:5030/image/af22c7fc4e0fac866403091cf52c6eae)

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-24 09:02:29
上边有说明的，你发的那种通知信

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-24 09:03:05
这个不是编辑通用发信~

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-04-24 09:03:24
> Reaching(iray1991) 04-24 08:58:07
> 动态群发不能自己编辑格式？
你发的这种类型 就用群发单显就行

boss(iray1991) 2025-05-26 19:34:16
1

 2025-05-26 19:34:26
"boss"邀请"tiger"加入了群聊

tiger(wxid_epu6wi8ytpir22) 2025-05-26 19:34:47
![图片](http://*************:5030/image/3bf10a5e2b2b60a324fac75cd7a124ce)

tiger(wxid_epu6wi8ytpir22) 2025-05-26 19:34:50
@网易客服－夏夏 @网易客服_汐汐 @凌霜侯 

tiger(wxid_epu6wi8ytpir22) 2025-05-26 19:36:56
这个功能我们没有？

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-05-26 20:36:48
可以升级版本，明天上班帮您看下

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-05-27 09:05:43
> tiger(wxid_epu6wi8ytpir22) 05-26 19:36:56
> 这个功能我们没有？
这个是外贸通版本的业务，发送产品 集中业务之类的。可以购买这个版本的业务

网易客服－夏夏(wxid_m6zcd4crcml222) 2025-05-27 09:06:11
若需要 我这边安排对应的售前工程师和您公司对接就好的~