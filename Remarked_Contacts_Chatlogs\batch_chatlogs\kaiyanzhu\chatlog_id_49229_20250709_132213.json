{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "49229", "contact_info": {"UserName": "wxid_c70dbu11k20x21", "Alias": "L-15210879467", "Remark": "49229李秋言", "NickName": "秋雨无言"}, "fetch_time": "2025-07-09T13:22:13.365250", "message_count": 32, "chatlog_data": [{"seq": 1751346220000, "time": "2025-07-01T13:03:40+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 10000, "subType": 0, "content": "秋雨无言通过了你的朋友验证请求，以上是打招呼的消息。"}, {"seq": 1751348172000, "time": "2025-07-01T13:36:12+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "李博您好呀，我们是一家专业的猎头公司，搭建高层次人才与国内企业高校就业创业全职兼职合作的桥梁；可以柔性合作或者全职回国就业创业。"}, {"seq": 1751348242000, "time": "2025-07-01T13:37:22+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们负责的有国家级的QM和一些省级的项目，您近期有回国的计划没有呀"}, {"seq": 1751348459000, "time": "2025-07-01T13:40:59+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "您好，我也是想申请人才机会回国的"}, {"seq": 1751348513000, "time": "2025-07-01T13:41:53+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那您今年是否有参与申报QM呀"}, {"seq": 1751348570000, "time": "2025-07-01T13:42:50+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "从来没申请过"}, {"seq": 1751349136000, "time": "2025-07-01T13:52:16+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们主要对接的是江浙一带的单位哦，工作地点您是OK的哈"}, {"seq": 1751349229000, "time": "2025-07-01T13:53:49+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您方便的话我跟您语音聊下呀"}, {"seq": 1751349576000, "time": "2025-07-01T13:59:36+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "我们这已经半夜了，咱们明天语音聊好吗"}, {"seq": 1751349589000, "time": "2025-07-01T13:59:49+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的好的"}, {"seq": 1751349599000, "time": "2025-07-01T13:59:59+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您早点休息哦"}, {"seq": 1751349607000, "time": "2025-07-01T14:00:07+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "想问下您什么时候方便呢？"}, {"seq": 1751349635000, "time": "2025-07-01T14:00:35+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我都OK的，看您的时间，您方便的时候跟我说，我给您拨过去"}, {"seq": 1751349671000, "time": "2025-07-01T14:01:11+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "嗯嗯，可是我明天起来您那就半夜了~"}, {"seq": 1751349701000, "time": "2025-07-01T14:01:41+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "咱们约个您也方便的时间啊？"}, {"seq": 1751349736000, "time": "2025-07-01T14:02:16+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "北京时间早上10点左右您那边怎么样呀"}, {"seq": 1751349777000, "time": "2025-07-01T14:02:57+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "可以，我这是晚上8点"}, {"seq": 1751349808000, "time": "2025-07-01T14:03:28+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好滴"}, {"seq": 1751349822000, "time": "2025-07-01T14:03:42+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "嗯，那明天北京时间10点语音见~"}, {"seq": 1751349835000, "time": "2025-07-01T14:03:55+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您早点休息~"}, {"seq": 1751349878000, "time": "2025-07-01T14:04:38+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "好的[握手]"}, {"seq": 1751421723000, "time": "2025-07-02T10:02:03+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "Hi"}, {"seq": 1751422224001, "time": "2025-07-02T10:10:24+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[通话时长 07:09]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>544365204786084342</roomid>\n<roomkey>0</roomkey>\n<inviteid>3370094483</inviteid>\n<msg_type>100</msg_type>\n<timestamp>1751422224556</timestamp>\n<identity><![CDATA[8267064776340234911]]></identity>\n<duration>0</duration>\n<inviteid64>1751421783955</inviteid64>\n<business>1</business>\n<caller_memberid>0</caller_memberid>\n<callee_memberid>1</callee_memberid>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1751422227000, "time": "2025-07-02T10:10:27+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "***********"}, {"seq": 1751425089000, "time": "2025-07-02T10:58:09+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1751425242000, "time": "2025-07-02T11:00:42+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"335f773fbf02020a6fd3b22b2a468a36\" encryver=\"1\" cdnthumbaeskey=\"335f773fbf02020a6fd3b22b2a468a36\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f540602049ff4903a02046864a0da042430396337623861302d373565302d343537382d613830372d3539333335613533306562340204051418020201000405004c4dfe00\" cdnthumblength=\"6353\" cdnthumbheight=\"66\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f540602049ff4903a02046864a0da042430396337623861302d373565302d343537382d613830372d3539333335613533306562340204051418020201000405004c4dfe00\" length=\"107284\" md5=\"b5e54a9a161d4e703e6fc7b7da40d9ec\" hevc_mid_size=\"0\" originsourcemd5=\"b5e54a9a161d4e703e6fc7b7da40d9ec\"/></msg>", "contents": {"md5": "b5e54a9a161d4e703e6fc7b7da40d9ec"}}, {"seq": 1751425242001, "time": "2025-07-02T11:00:42+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "李博，这是需要准备的一些材料，您可以先看一下哦"}, {"seq": 1751426962000, "time": "2025-07-02T11:29:22+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "好的~"}, {"seq": 1751438746000, "time": "2025-07-02T14:45:46+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "您好，还可以再加两条我的专长：油田化学剂方面，1.油田堵水剂研发（聚合物微球，自聚集胶体颗粒等），2. 油田高效驱油剂开发（强乳化体系，复配表面活性剂体系等）。是不是这样可以更加侧重产业化一点？可能能匹配的企业更多吧"}, {"seq": 1751444687000, "time": "2025-07-02T16:24:47+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好呀好呀"}, {"seq": 1751444692000, "time": "2025-07-02T16:24:52+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我这边登记下"}, {"seq": 1751475253000, "time": "2025-07-03T00:54:13+08:00", "talker": "wxid_c70dbu11k20x21", "talkerName": "", "isChatRoom": false, "sender": "wxid_c70dbu11k20x21", "senderName": "49229李秋言", "isSelf": false, "type": 1, "subType": 0, "content": "[抱拳]"}]}