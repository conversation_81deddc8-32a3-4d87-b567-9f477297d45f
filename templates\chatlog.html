<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信聊天记录查看器</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 100%;
            height: 100vh;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            flex-shrink: 0;
        }

        .header h1 {
            color: #07c160;
            margin: 0 0 10px 0;
            font-size: 20px;
            display: flex;
            align-items: center;
        }

        .header h1::before {
            content: "💬";
            margin-right: 10px;
        }

        .search-section {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }

        .main-content {
            flex: 1;
            display: flex;
            height: calc(100vh - 120px);
            overflow: hidden;
        }

        .search-input {
            width: 400px;
            padding: 10px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            border-color: #07c160;
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
            font-weight: bold;
            color: #333;
        }

        .users-list {
            flex: 1;
            overflow-y: auto;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .btn-primary {
            background: #07c160;
            color: white;
        }

        .btn-primary:hover {
            background: #06ad56;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #666;
        }

        .btn-secondary:hover {
            background: #e0e0e0;
        }

        .user-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .user-item:hover {
            background-color: #f8f8f8;
        }

        .user-item.active {
            background-color: #e7f7e7;
            border-right: 3px solid #07c160;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #07c160;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            flex-shrink: 0;
        }

        .user-info {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .user-stats {
            font-size: 12px;
            color: #666;
        }

        .contact-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .contact-item:hover {
            background-color: #f8f8f8;
        }

        .contact-item:last-child {
            border-bottom: none;
        }

        .contact-info {
            flex: 1;
        }

        .contact-id {
            font-weight: bold;
            color: #07c160;
            font-size: 14px;
        }

        .contact-name {
            color: #333;
            font-size: 14px;
            margin-top: 2px;
        }

        .contact-user {
            color: #666;
            font-size: 12px;
            margin-top: 2px;
        }

        .contact-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-top: 6px;
        }

        .contact-tag {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 500;
            white-space: nowrap;
        }

        .contact-tag.profession {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .contact-tag.tech {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .contact-tag.location {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }

        .contact-tag.status {
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
        }

        .contact-tag.salary {
            background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
        }

        .contact-tag.activity {
            background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
        }

        /* 附件消息样式 */
        .attachment-message {
            background: #f0f0f0 !important;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 8px 12px;
        }

        .message.self .attachment-message {
            background: rgba(255,255,255,0.2) !important;
            border-color: rgba(255,255,255,0.3);
        }

        .attachment-content {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .attachment-icon {
            font-size: 18px;
            flex-shrink: 0;
        }

        .attachment-description {
            flex: 1;
            font-size: 14px;
            color: #666;
        }

        .message.self .attachment-description {
            color: rgba(255,255,255,0.9);
        }

        .attachment-filename {
            font-size: 12px;
            color: #999;
            width: 100%;
            margin-top: 4px;
            word-break: break-all;
        }

        .message.self .attachment-filename {
            color: rgba(255,255,255,0.7);
        }

        /* 不同类型附件的特殊样式 */
        .attachment-image {
            border-color: #4CAF50;
        }

        .attachment-voice {
            border-color: #FF9800;
        }

        .attachment-video {
            border-color: #2196F3;
        }

        .attachment-file {
            border-color: #9C27B0;
        }

        .attachment-link {
            border-color: #00BCD4;
        }

        .attachment-location {
            border-color: #F44336;
        }

        .attachment-emoji {
            border-color: #FFEB3B;
        }

        .attachment-voice-call {
            border-color: #4CAF50;
        }

        /* 语音通话样式 */
        .voice-call-container {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 1px solid #4CAF50;
            border-radius: 12px;
            padding: 15px;
            margin-top: 8px;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }

        .call-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
        }

        .call-icon {
            font-size: 24px;
            background: #4CAF50;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
        }

        .call-details {
            flex: 1;
        }

        .call-type {
            font-weight: bold;
            color: #2E7D32;
            font-size: 16px;
            margin-bottom: 4px;
        }

        .call-duration {
            font-size: 18px;
            font-weight: bold;
            color: #1B5E20;
            background: rgba(76, 175, 80, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            display: inline-block;
        }

        .call-status {
            text-align: center;
            padding-top: 8px;
            border-top: 1px solid rgba(76, 175, 80, 0.2);
        }

        .call-status-text {
            font-size: 12px;
            color: #666;
            background: rgba(0, 0, 0, 0.05);
            padding: 4px 8px;
            border-radius: 12px;
        }

        /* 在自己发送的消息中的通话样式 */
        .message.self .voice-call-container {
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            border-color: rgba(255,255,255,0.3);
        }

        .message.self .call-type {
            color: rgba(255,255,255,0.9);
        }

        .message.self .call-duration {
            color: white;
            background: rgba(255,255,255,0.2);
        }

        .message.self .call-status-text {
            color: rgba(255,255,255,0.7);
            background: rgba(255,255,255,0.1);
        }

        /* 智能分析按钮样式 */
        .analyze-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .analyze-btn:active {
            transform: translateY(0);
        }

        .analyze-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .analyze-icon {
            font-size: 16px;
        }

        .analyze-text {
            font-weight: 600;
        }

        /* 分析报告模态框样式 */
        .analysis-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .analysis-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .analysis-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80%;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .analysis-modal.show .analysis-content {
            transform: scale(1);
        }

        .analysis-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .analysis-title {
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .analysis-body {
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }

        .loading-spinner {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .analysis-result {
            line-height: 1.6;
            color: #333;
        }

        .analysis-result h3 {
            color: #667eea;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .analysis-result h3:first-child {
            margin-top: 0;
        }

        .analysis-result p {
            margin-bottom: 10px;
        }

        .analysis-result ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .analysis-result li {
            margin-bottom: 5px;
        }

        /* 分析操作按钮样式 */
        .analysis-actions {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .copy-btn, .export-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .copy-btn {
            background: #4CAF50;
            color: white;
        }

        .copy-btn:hover {
            background: #45a049;
        }

        .export-btn {
            background: #2196F3;
            color: white;
        }

        .export-btn:hover {
            background: #1976D2;
        }

        .error-message {
            text-align: center;
            padding: 40px 20px;
            color: #f44336;
        }

        .error-message h3 {
            margin-bottom: 15px;
        }

        .analysis-summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .analysis-summary h3 {
            margin-top: 0;
            color: #667eea;
        }

        /* 标签样式 */
        .tags-container {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }

        .tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            transition: all 0.2s ease;
        }

        .tag-extracted {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }

        .tag-analysis {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
        }

        .generate-followup-btn {
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
        }

        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* 跟进消息模态框样式 */
        .followup-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1001;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .followup-modal.show {
            opacity: 1;
            visibility: visible;
        }

        .followup-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .followup-modal.show .followup-content {
            transform: scale(1);
        }

        .followup-header {
            padding: 20px;
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
            color: white;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .followup-title {
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .followup-body {
            padding: 20px;
        }

        .qualification-section {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #9C27B0;
        }

        .qualification-qualified {
            background: #E8F5E8;
            border-left-color: #4CAF50;
        }

        .qualification-unqualified {
            background: #FFEBEE;
            border-left-color: #F44336;
        }

        .professional-tags-section {
            margin-bottom: 20px;
        }

        .professional-tag {
            display: inline-block;
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin: 2px;
            box-shadow: 0 2px 4px rgba(156, 39, 176, 0.3);
        }

        .followup-messages-section {
            margin-bottom: 20px;
        }

        .followup-message-item {
            margin-bottom: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #9C27B0;
        }

        .followup-message-time {
            font-weight: bold;
            color: #9C27B0;
            margin-bottom: 8px;
        }

        .followup-message-content {
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
        }

        .followup-actions {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .extracted-tags-summary {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e0e0e0;
        }

        .extracted-tags-summary p {
            margin-bottom: 8px;
            font-weight: 600;
        }

        .contact-tags {
            margin-top: 10px;
        }

        /* 标签提取功能样式 */
        .extract-tags-btn {
            background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-left: 8px;
        }

        .extract-tags-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(156, 39, 176, 0.4);
        }

        .extract-tags-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .extracted-tags-container {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .extracted-tags-container h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .tag-extracted {
            background: linear-gradient(135deg, #9C27B0 0%, #673AB7 100%);
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tag-extracted:hover {
            background: linear-gradient(135deg, #8E24AA 0%, #5E35B1 100%);
            transform: translateY(-1px);
        }

        .tag-applied {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .tags-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
        }

        .apply-tags-btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .apply-tags-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        .clear-tags-btn {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .clear-tags-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
        }

        /* 可编辑标签样式 */
        .tags-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .tags-header h4 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }

        .tags-controls {
            display: flex;
            gap: 8px;
        }

        .add-tag-btn, .edit-mode-btn {
            background: #2196F3;
            color: white;
            border: none;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .add-tag-btn:hover, .edit-mode-btn:hover {
            background: #1976D2;
            transform: scale(1.1);
        }

        .editable-tag {
            position: relative;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .tag-delete {
            display: none;
            background: rgba(255, 255, 255, 0.3);
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            text-align: center;
            line-height: 16px;
            transition: all 0.2s ease;
        }

        .tag-delete:hover {
            background: rgba(255, 255, 255, 0.5);
            transform: scale(1.2);
        }

        .edit-mode .tag-delete {
            display: inline-block;
        }

        .edit-mode .editable-tag {
            padding-right: 8px;
        }

        .add-tag-input {
            display: none;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
            padding: 8px;
            background: white;
            border: 2px solid #2196F3;
            border-radius: 6px;
        }

        .add-tag-input input {
            flex: 1;
            border: none;
            outline: none;
            padding: 4px 8px;
            font-size: 14px;
            border-radius: 4px;
            background: #f5f5f5;
        }

        .add-tag-input button {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .add-tag-input button:first-of-type {
            color: #4CAF50;
        }

        .add-tag-input button:first-of-type:hover {
            background: #E8F5E8;
        }

        .add-tag-input button:last-of-type {
            color: #f44336;
        }

        .add-tag-input button:last-of-type:hover {
            background: #FFEBEE;
        }

        .contact-tags {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        /* 新标签分类样式 */
        .auto-extracted-tags {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e0e0e0;
        }

        .auto-extracted-tags p {
            margin-bottom: 8px;
            font-weight: 600;
        }

        .tag-auto {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
        }

        .apply-auto-tags-btn {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-left: 8px;
        }

        .apply-auto-tags-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4);
        }

        .categorized-tags-container {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .categorized-tags-container h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .tags-categories {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .tag-category {
            background: white;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .tag-category h5 {
            margin: 0 0 8px 0;
            color: #555;
            font-size: 14px;
        }

        .tag-judgment {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
        }

        .tag-manual {
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
            color: white;
        }

        .tag-ai {
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
            color: white;
        }

        .tag-remark {
            background: linear-gradient(135deg, #FF5722 0%, #D84315 100%);
            color: white;
        }

        /* 自动生成标签样式 */
        .tag-comprehensive {
            background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
            color: white;
            position: relative;
        }

        .edit-auto-tags-btn {
            background: #607D8B;
            color: white;
            border: none;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .edit-auto-tags-btn:hover {
            background: #455A64;
            transform: scale(1.1);
        }

        .toggle-categories-btn {
            background: #2196F3;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
            transition: all 0.2s ease;
        }

        .toggle-categories-btn:hover {
            background: #1976D2;
        }

        .apply-auto-tags-btn {
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .apply-auto-tags-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
        }

        .comprehensive-tags {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .categorized-tags {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .contact-tags-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        /* 附件预览样式 */
        .attachment-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .attachment-size {
            font-size: 11px;
            color: #999;
            background: rgba(0,0,0,0.1);
            padding: 2px 6px;
            border-radius: 10px;
        }

        .attachment-preview {
            margin-top: 8px;
        }

        /* 图片容器样式 */
        .image-container {
            position: relative;
            display: inline-block;
        }

        .attachment-image-preview {
            max-width: 250px;
            max-height: 250px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s;
            display: block;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .attachment-image-preview:hover {
            transform: scale(1.02);
        }

        .retry-btn {
            padding: 4px 8px;
            background: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 5px;
        }

        .retry-btn:hover {
            background: #06ad56;
        }

        /* 语音容器样式 */
        .voice-container {
            background: #f0f8ff;
            border: 1px solid #d0e7ff;
            border-radius: 8px;
            padding: 10px;
            margin-top: 8px;
        }

        .voice-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            color: #666;
        }

        .voice-icon {
            font-size: 16px;
        }

        /* 视频容器样式 */
        .video-container {
            margin-top: 8px;
        }

        .video-controls {
            margin-top: 8px;
            text-align: center;
        }

        .video-btn {
            padding: 6px 12px;
            background: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }

        .video-btn:hover {
            background: #06ad56;
        }

        .attachment-audio {
            width: 100%;
            max-width: 250px;
            height: 40px;
        }

        .attachment-video-preview {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
        }

        .attachment-download,
        .attachment-link-preview {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(0,0,0,0.1);
            border-radius: 6px;
            text-decoration: none;
            color: inherit;
            transition: background-color 0.2s;
        }

        .attachment-download:hover,
        .attachment-link-preview:hover {
            background: rgba(0,0,0,0.2);
        }

        .attachment-url {
            margin-top: 8px;
            font-size: 12px;
            word-break: break-all;
        }

        .attachment-url a {
            color: #07c160;
            text-decoration: none;
        }

        .attachment-url a:hover {
            text-decoration: underline;
        }

        /* 附件超链接按钮样式 */
        .attachment-link-btn {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            background: #07c160;
            color: white !important;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            margin-top: 8px;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }

        .attachment-link-btn:hover {
            background: #06ad56;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(7,193,96,0.3);
        }

        .attachment-link-btn:active {
            transform: translateY(0);
        }

        /* 在自己发送的消息中的链接按钮 */
        .message.self .attachment-link-btn {
            background: rgba(255,255,255,0.2);
            color: white !important;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .message.self .attachment-link-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .attachment-error {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            background: rgba(255,0,0,0.1);
            border-radius: 6px;
            color: #666;
        }

        /* 图片模态框样式 */
        .image-modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            text-align: center;
        }

        .image-modal-img {
            max-width: 100%;
            max-height: 80vh;
            border-radius: 8px;
        }

        .image-modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 30px;
            font-weight: bold;
            cursor: pointer;
            background: rgba(0,0,0,0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-modal-close:hover {
            background: rgba(0,0,0,0.7);
        }

        .image-modal-caption {
            color: white;
            margin-top: 10px;
            font-size: 14px;
        }

        /* PDF预览样式 */
        .pdf-container {
            width: 100%;
            margin-top: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background: #f9f9f9;
        }

        .pdf-viewer {
            width: 100%;
            height: 400px;
            border: none;
            display: block;
        }

        .pdf-viewer.expanded {
            height: 600px;
        }

        .pdf-controls {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            padding: 8px;
            background: #f5f5f5;
            border-radius: 0 0 8px 8px;
        }

        .pdf-btn {
            padding: 6px 12px;
            background: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .pdf-btn:hover {
            background: #06ad56;
        }

        /* 文本文件样式 */
        .text-container {
            margin-top: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 10px;
            background: #f9f9f9;
        }

        .load-text-btn {
            width: 100%;
            padding: 10px;
            background: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .load-text-btn:hover {
            background: #06ad56;
        }

        .text-content {
            max-height: 300px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .attachment-text-preview {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 10px;
            background: #f9f9f9;
            font-family: monospace;
        }

        .text-content {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 12px;
            line-height: 1.4;
        }

        .attachment-controls {
            display: flex;
            gap: 8px;
            margin-top: 8px;
            flex-wrap: wrap;
        }

        .attachment-btn {
            padding: 6px 12px;
            background: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .attachment-btn:hover {
            background: #06ad56;
        }

        .attachment-fallback {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 20px;
            background: #f0f0f0;
            border-radius: 8px;
            text-align: center;
        }

        /* 全屏模态框样式 */
        .fullscreen-modal {
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
        }

        .fullscreen-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .fullscreen-header {
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .fullscreen-title {
            font-size: 16px;
            font-weight: bold;
        }

        .fullscreen-close {
            font-size: 24px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
        }

        .fullscreen-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .fullscreen-iframe {
            flex: 1;
            width: 100%;
            border: none;
            background: white;
        }

        /* 加载和错误状态 */
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #07c160;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* PDF和Office预览回退样式 */
        .pdf-fallback,
        .office-fallback {
            padding: 15px;
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
        }

        .pdf-options,
        .office-options {
            display: flex;
            gap: 8px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .pdf-viewer-container,
        .office-viewer-container {
            position: relative;
        }

        /* 按钮禁用状态 */
        .attachment-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .attachment-btn:disabled:hover {
            background: #ccc;
        }

        /* 文本内容样式优化 */
        .text-content {
            max-height: 400px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }



        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
            height: 100%;
            overflow: hidden;
        }

        .chat-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header-left {
            flex: 1;
        }

        .chat-header-right {
            flex-shrink: 0;
        }

        .chat-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .chat-info {
            font-size: 12px;
            color: #666;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f8f8;
            height: 0; /* 这个技巧确保flex子元素可以滚动 */
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.self {
            flex-direction: row-reverse;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            line-height: 1.4;
            position: relative;
        }

        .message.other .message-bubble {
            background: white;
            border: 1px solid #e0e0e0;
            margin-left: 10px;
        }

        .message.self .message-bubble {
            background: #07c160;
            color: white;
            margin-right: 10px;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 5px;
            text-align: center;
        }

        .message.self .message-time {
            text-align: right;
        }

        .message.other .message-time {
            text-align: left;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #07c160;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            flex-shrink: 0;
        }

        .message.self .avatar {
            background: #1989fa;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #f56565;
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }

        .stat-item {
            font-size: 12px;
            color: #666;
        }

        .stat-value {
            font-weight: bold;
            color: #07c160;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .search-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .chat-container {
                height: 500px;
            }
            
            .message-bubble {
                max-width: 85%;
            }
        }

        /* 联系人标签样式 */
        .contact-tags-section {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #4CAF50;
        }

        .contact-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 5px;
        }

        .contact-tag {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            color: white;
            white-space: nowrap;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .contact-tag.profession {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .contact-tag.tech {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .contact-tag.location {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }

        .contact-tag.status {
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
        }

        .contact-tag.salary {
            background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%);
        }

        .contact-tag.activity {
            background: linear-gradient(135deg, #607D8B 0%, #455A64 100%);
        }

        .contact-tag.default {
            background: linear-gradient(135deg, #795548 0%, #5D4037 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部搜索区域 -->
        <div class="header">
            <h1>微信聊天记录查看器</h1>
            <div class="search-section">
                <input type="text" id="searchInput" class="search-input" placeholder="输入联系人ID或备注搜索...">
                <button class="btn btn-primary" onclick="searchContact()">搜索</button>
                <button class="btn btn-secondary" onclick="loadUsers()">刷新用户列表</button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧用户列表 -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <span>用户列表</span>
                </div>
                <div class="users-list" id="usersList">
                    <div class="empty-state">
                        <div class="icon">👥</div>
                        <div>点击"刷新用户列表"加载用户</div>
                    </div>
                </div>
            </div>

            <!-- 右侧聊天区域 -->
            <div class="chat-area">
                <div class="chat-container">
                    <div class="chat-header">
                        <div class="chat-header-left">
                            <div class="chat-title" id="chatTitle">选择用户查看聊天记录</div>
                            <div class="chat-info" id="chatInfo">请先选择左侧的用户</div>
                        </div>
                        <div class="chat-header-right" id="chatActions" style="display: none;">
                            <button id="analyzeBtn" class="analyze-btn" onclick="analyzeCurrentChat()">
                                <span class="analyze-icon">🧠</span>
                                <span class="analyze-text">智能分析</span>
                            </button>
                        </div>
                    </div>
                    <div class="chat-messages" id="chatMessages">
                        <div class="empty-state">
                            <div class="icon">💬</div>
                            <div>请选择用户查看聊天记录</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分析报告模态框 -->
    <div id="analysisModal" class="analysis-modal">
        <div class="analysis-content">
            <div class="analysis-header">
                <div class="analysis-title">
                    <span>🧠</span>
                    <span>智能分析报告</span>
                </div>
                <button class="close-btn" onclick="closeAnalysisModal()">&times;</button>
            </div>
            <div class="analysis-body" id="analysisBody">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>正在分析聊天记录，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 跟进消息模态框 -->
    <div id="followupModal" class="followup-modal">
        <div class="followup-content">
            <div class="followup-header">
                <div class="followup-title">
                    <span>💬</span>
                    <span>跟进消息生成</span>
                </div>
                <button class="close-btn" onclick="closeFollowupModal()">&times;</button>
            </div>
            <div class="followup-body" id="followupBody">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>正在生成跟进消息，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allContacts = [];
        let allUsers = [];
        let currentUser = null;
        let currentContactId = null;

        // 搜索联系人
        async function searchContact() {
            const searchTerm = document.getElementById('searchInput').value.trim();

            if (!searchTerm) {
                return;
            }

            // 如果是纯数字，直接查询聊天记录
            if (/^\d+$/.test(searchTerm)) {
                await loadChatlogById(searchTerm);
                return;
            }

            // 否则搜索并高亮匹配的用户
            highlightMatchingUsers(searchTerm);
        }

        // 加载用户列表
        async function loadUsers() {
            try {
                const response = await fetch('/api/search_contacts');
                const data = await response.json();

                if (data.status === 'success') {
                    allContacts = data.contacts;

                    // 按用户分组
                    const groupedContacts = {};
                    data.contacts.forEach(contact => {
                        if (!groupedContacts[contact.user]) {
                            groupedContacts[contact.user] = [];
                        }
                        groupedContacts[contact.user].push(contact);
                    });

                    allUsers = Object.keys(groupedContacts).map(username => ({
                        name: username,
                        contacts: groupedContacts[username],
                        contactCount: groupedContacts[username].length
                    }));

                    displayUsers(allUsers);
                } else {
                    showError('加载用户失败: ' + data.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 显示用户列表
        function displayUsers(users) {
            const usersList = document.getElementById('usersList');

            if (users.length === 0) {
                usersList.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">👥</div>
                        <div>没有找到用户</div>
                    </div>
                `;
                return;
            }

            const usersHtml = users.map(user => `
                <div class="user-item" onclick="selectUser('${user.name}')">
                    <div class="user-avatar">${getUserIcon(user.name)}</div>
                    <div class="user-info">
                        <div class="user-name">${user.name}</div>
                        <div class="user-stats">${user.contactCount} 个联系人</div>
                    </div>
                </div>
            `).join('');

            usersList.innerHTML = usersHtml;
        }

        // 选择用户
        function selectUser(username) {
            currentUser = username;

            // 更新用户选中状态
            document.querySelectorAll('.user-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.user-item').classList.add('active');

            // 显示该用户的联系人聊天记录
            const user = allUsers.find(u => u.name === username);
            if (user && user.contacts.length > 0) {
                // 显示第一个联系人的聊天记录，或者显示联系人列表让用户选择
                displayUserContacts(user);
            }
        }

        // 显示用户的联系人并加载聊天记录
        async function displayUserContacts(user) {
            const chatTitle = document.getElementById('chatTitle');
            const chatInfo = document.getElementById('chatInfo');

            chatTitle.textContent = `${user.name} 的联系人 (${user.contactCount}个)`;
            chatInfo.innerHTML = `点击下方联系人查看聊天记录`;

            // 在聊天区域显示联系人列表
            const chatMessages = document.getElementById('chatMessages');

            const contactsHtml = user.contacts.map(contact => `
                <div class="contact-item" onclick="loadChatlogById('${contact.id}', '${contact.remark}')">
                    <div class="contact-info">
                        <div class="contact-id">ID: ${contact.id}</div>
                        <div class="contact-name">${contact.remark}</div>
                        <div class="contact-user">昵称: ${contact.nickname || '无'}</div>
                    </div>
                </div>
            `).join('');

            chatMessages.innerHTML = `
                <div style="padding: 20px;">
                    <h3 style="margin-bottom: 15px; color: #333;">选择联系人查看聊天记录：</h3>
                    <div style="max-height: 400px; overflow-y: auto;">
                        ${contactsHtml}
                    </div>
                </div>
            `;
        }

        // 根据ID加载聊天记录
        async function loadChatlogById(contactId, contactName = '') {
            currentContactId = contactId;

            // 显示加载状态
            showLoading();

            try {
                const response = await fetch(`/api/get_chatlog_by_id/${contactId}`);
                const data = await response.json();

                if (data.status === 'success') {
                    // 1. 先显示聊天记录
                    displayChatlog(data.data);

                    // 2. 后台自动分析标签
                    generateContactTags(contactId, data.data);
                } else {
                    showError('获取聊天记录失败: ' + data.message);
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }

        // 自动生成联系人标签（新架构）
        async function generateContactTags(contactId, chatData) {
            try {
                console.log('🏷️ 开始自动生成标签...');

                // 在标题旁显示标签生成状态
                const chatTitle = document.getElementById('chatTitle');
                const originalTitle = chatTitle.textContent;
                chatTitle.innerHTML = `${originalTitle} <span style="color: #666; font-size: 12px;">🔄 自动分析中...</span>`;

                // 调用新的自动标签生成API
                const response = await fetch(`/api/auto_generate_tags/${contactId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(chatData)
                });

                const result = await response.json();

                if (result.status === 'success') {
                    console.log('✅ 自动标签生成成功');

                    // 显示自动生成的标签
                    displayAutoGeneratedTags(result.tags);

                    // 更新标题，显示完成状态
                    chatTitle.innerHTML = `${originalTitle} <span style="color: #4CAF50; font-size: 12px;">✅ 已生成标签</span>`;

                    // 3秒后恢复原标题
                    setTimeout(() => {
                        chatTitle.innerHTML = originalTitle;
                    }, 3000);
                } else {
                    console.error('❌ 自动标签生成失败:', result.message);
                    chatTitle.innerHTML = `${originalTitle} <span style="color: #f44336; font-size: 12px;">❌ 标签生成失败</span>`;

                    // 3秒后恢复原标题
                    setTimeout(() => {
                        chatTitle.innerHTML = originalTitle;
                    }, 3000);
                }
            } catch (error) {
                console.error('❌ 自动标签生成请求失败:', error);
                const chatTitle = document.getElementById('chatTitle');
                const originalTitle = chatTitle.textContent.replace(/🔄 自动分析中\.\.\./, '').trim();
                chatTitle.innerHTML = `${originalTitle} <span style="color: #f44336; font-size: 12px;">❌ 网络错误</span>`;

                // 3秒后恢复原标题
                setTimeout(() => {
                    chatTitle.innerHTML = originalTitle;
                }, 3000);
            }
        }

        // 显示自动生成的标签
        function displayAutoGeneratedTags(tagsResult) {
            const chatInfo = document.getElementById('chatInfo');

            if (!tagsResult || !tagsResult.comprehensive) {
                console.log('⚠️ 没有可显示的标签');
                return;
            }

            // 检查是否已经有标签区域
            const existingTagsSection = chatInfo.querySelector('.contact-tags-section');
            if (existingTagsSection) {
                existingTagsSection.remove();
            }

            // 生成分类标签HTML
            const categorizedTagsHtml = generateCategorizedTagsHtml(tagsResult);

            // 在现有信息后添加标签
            const currentInfo = chatInfo.innerHTML;
            chatInfo.innerHTML = currentInfo + `
                <div class="contact-tags-section" style="margin-top: 15px;">
                    <div class="tags-header">
                        <div style="font-size: 14px; color: #333; margin-bottom: 10px; font-weight: 600;">🏷️ 自动生成标签</div>
                        <div class="tags-controls">
                            <button onclick="editAutoTags()" class="edit-auto-tags-btn" title="编辑标签">✏️</button>
                        </div>
                    </div>

                    <!-- 综合标签 -->
                    <div class="comprehensive-tags" style="margin-bottom: 15px;">
                        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">综合标签:</div>
                        <div class="tags-container" id="comprehensiveTagsContainer">
                            ${tagsResult.comprehensive.map(tag => `<span class="tag tag-comprehensive">${tag}</span>`).join('')}
                        </div>
                    </div>

                    <!-- 分类标签 -->
                    <div class="categorized-tags" style="display: none;" id="categorizedTagsContainer">
                        ${categorizedTagsHtml}
                    </div>

                    <div class="tags-actions" style="margin-top: 10px;">
                        <button onclick="toggleCategorizedTags()" class="toggle-categories-btn">📋 查看分类</button>
                        <button onclick="applyAutoTagsToContact()" class="apply-auto-tags-btn">✅ 应用到联系人</button>
                    </div>
                </div>
            `;
        }

        function generateCategorizedTagsHtml(tagsResult) {
            let html = '';

            const categories = [
                { key: 'judgment', name: '1️⃣ 判断备注', class: 'tag-judgment' },
                { key: 'manual', name: '2️⃣ 人工检索', class: 'tag-manual' },
                { key: 'ai', name: '3️⃣ AI对话', class: 'tag-ai' },
                { key: 'remark', name: '4️⃣ 备注提取', class: 'tag-remark' }
            ];

            categories.forEach(category => {
                const tags = tagsResult[category.key] || [];
                if (tags.length > 0) {
                    html += `
                        <div class="tag-category" style="margin-bottom: 10px;">
                            <div style="font-size: 12px; color: #666; margin-bottom: 5px;">${category.name}:</div>
                            <div class="tags-container">
                                ${tags.map(tag => `<span class="tag ${category.class}">${tag}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            return html;
        }

        function toggleCategorizedTags() {
            const categorizedContainer = document.getElementById('categorizedTagsContainer');
            const toggleBtn = document.querySelector('.toggle-categories-btn');

            if (categorizedContainer.style.display === 'none') {
                categorizedContainer.style.display = 'block';
                toggleBtn.textContent = '📋 隐藏分类';
            } else {
                categorizedContainer.style.display = 'none';
                toggleBtn.textContent = '📋 查看分类';
            }
        }

        function applyAutoTagsToContact() {
            const comprehensiveTags = Array.from(document.querySelectorAll('#comprehensiveTagsContainer .tag'))
                .map(tag => tag.textContent.trim());

            if (comprehensiveTags.length === 0) {
                showMessage('❌ 没有可应用的标签', 'error');
                return;
            }

            // 应用标签到联系人信息区域
            updateContactTagsDisplay(comprehensiveTags);

            showMessage(`✅ 已应用 ${comprehensiveTags.length} 个自动标签`, 'success');
        }

        function editAutoTags() {
            // 进入编辑模式，允许用户修改自动生成的标签
            const comprehensiveContainer = document.getElementById('comprehensiveTagsContainer');
            const tags = Array.from(comprehensiveContainer.querySelectorAll('.tag'));

            // 为每个标签添加删除按钮
            tags.forEach(tag => {
                if (!tag.querySelector('.tag-delete')) {
                    const deleteBtn = document.createElement('span');
                    deleteBtn.className = 'tag-delete';
                    deleteBtn.textContent = '×';
                    deleteBtn.onclick = () => {
                        tag.remove();
                        showMessage('✅ 标签已删除', 'info');
                    };
                    tag.appendChild(deleteBtn);
                }
            });

            // 添加新标签输入框
            if (!document.getElementById('addAutoTagInput')) {
                const addTagDiv = document.createElement('div');
                addTagDiv.id = 'addAutoTagInput';
                addTagDiv.className = 'add-tag-input';
                addTagDiv.style.display = 'flex';
                addTagDiv.innerHTML = `
                    <input type="text" placeholder="添加新标签" maxlength="20" />
                    <button onclick="confirmAddAutoTag()">✅</button>
                    <button onclick="cancelAddAutoTag()">❌</button>
                `;
                comprehensiveContainer.parentNode.appendChild(addTagDiv);

                const input = addTagDiv.querySelector('input');
                input.focus();
                input.onkeypress = function(e) {
                    if (e.key === 'Enter') {
                        confirmAddAutoTag();
                    } else if (e.key === 'Escape') {
                        cancelAddAutoTag();
                    }
                };
            }

            showMessage('✏️ 已进入编辑模式，点击×删除标签', 'info');
        }

        function confirmAddAutoTag() {
            const input = document.querySelector('#addAutoTagInput input');
            const newTagText = input.value.trim();

            if (newTagText.length === 0) {
                showMessage('❌ 标签内容不能为空', 'error');
                return;
            }

            // 检查是否已存在
            const existingTags = Array.from(document.querySelectorAll('#comprehensiveTagsContainer .tag'))
                .map(tag => tag.textContent.replace('×', '').trim());

            if (existingTags.includes(newTagText)) {
                showMessage('❌ 标签已存在', 'warning');
                return;
            }

            // 添加新标签
            const comprehensiveContainer = document.getElementById('comprehensiveTagsContainer');
            const newTag = document.createElement('span');
            newTag.className = 'tag tag-comprehensive';
            newTag.innerHTML = `${newTagText}<span class="tag-delete" onclick="this.parentElement.remove(); showMessage('✅ 标签已删除', 'info');">×</span>`;
            comprehensiveContainer.appendChild(newTag);

            // 清空输入框
            input.value = '';
            showMessage(`✅ 已添加标签: ${newTagText}`, 'success');
        }

        function cancelAddAutoTag() {
            const addTagInput = document.getElementById('addAutoTagInput');
            if (addTagInput) {
                addTagInput.remove();
            }
        }

        // 显示联系人标签
        function displayContactTags(tags, tagSummary) {
            const chatInfo = document.getElementById('chatInfo');

            // 生成标签HTML
            const tagsHtml = generateTagsHtml(tagSummary);

            // 在现有信息后添加标签
            const currentInfo = chatInfo.innerHTML;
            chatInfo.innerHTML = currentInfo + `
                <div class="contact-tags-section" style="margin-top: 10px;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 5px;">🏷️ 候选人标签:</div>
                    <div class="contact-tags">
                        ${tagsHtml}
                    </div>
                </div>
            `;
        }

        // 生成标签HTML
        function generateTagsHtml(tagSummary) {
            if (!tagSummary || tagSummary.length === 0) {
                return '<span style="color: #999; font-size: 12px;">暂无标签</span>';
            }

            return tagSummary.map(tag => {
                const tagClass = getTagClass(tag);
                return `<span class="contact-tag ${tagClass}">${tag}</span>`;
            }).join('');
        }

        // 根据标签内容确定样式类
        function getTagClass(tag) {
            if (['开发', '产品', '设计', '运营', '销售', '市场', '财务', 'HR', '测试', '数据'].includes(tag)) {
                return 'profession';
            } else if (['Java', 'Python', '前端', '后端', '全栈', 'AI', '大数据'].includes(tag)) {
                return 'tech';
            } else if (['北京', '上海', '深圳', '广州', '杭州', '成都', '海外'].includes(tag)) {
                return 'location';
            } else if (['兼职', '全职', '主动求职', '被动求职'].includes(tag)) {
                return 'status';
            } else if (tag.includes('万') || tag.includes('K')) {
                return 'salary';
            } else if (['活跃', '不活跃', '一般活跃'].includes(tag)) {
                return 'activity';
            }
            return 'default';
        }

        // 高亮匹配的用户
        function highlightMatchingUsers(searchTerm) {
            const userItems = document.querySelectorAll('.user-item');
            let hasMatch = false;

            userItems.forEach(item => {
                const userName = item.querySelector('.user-name').textContent;
                const userStats = item.querySelector('.user-stats').textContent;

                if (userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    userStats.toLowerCase().includes(searchTerm.toLowerCase())) {
                    item.style.backgroundColor = '#fff3cd';
                    item.style.border = '2px solid #ffc107';
                    hasMatch = true;
                } else {
                    item.style.backgroundColor = '';
                    item.style.border = '';
                }
            });

            if (!hasMatch) {
                showError(`没有找到包含 "${searchTerm}" 的用户`);
            }
        }

        // 格式化附件消息
        function formatAttachmentMessage(msg) {
            const attachmentType = msg.attachment_type || 'unknown';
            const description = msg.description || '[附件]';
            const filename = msg.filename || '';
            const url = msg.url || '';
            const size = msg.size || '';

            let icon = '';
            let className = 'attachment-message';
            let attachmentContent = '';

            // 如果有URL，创建超链接
            const linkHtml = url ? `<a href="${escapeHtml(url)}" target="_blank" class="attachment-link-btn">🔗 点击打开文件</a>` : '';

            switch (attachmentType) {
                case 'image':
                    icon = '🖼️';
                    className += ' attachment-image';
                    if (url) {
                        attachmentContent = `
                            <div class="attachment-preview">
                                <div class="image-container">
                                    <img src="${escapeHtml(url)}" alt="${escapeHtml(filename || 'WeChat图片')}"
                                         class="attachment-image-preview"
                                         onclick="openImageModal('${escapeHtml(url)}', '${escapeHtml(filename || 'WeChat图片')}')"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                                         loading="lazy">
                                    <div class="attachment-error" style="display:none;">
                                        <span class="attachment-icon">🖼️</span>
                                        <span>图片加载失败</span>
                                        <button onclick="retryLoadImage('${escapeHtml(url)}', this)" class="retry-btn">重试</button>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    break;

                case 'voice':
                    icon = '🎵';
                    className += ' attachment-voice';
                    if (url) {
                        attachmentContent = `
                            <div class="attachment-preview">
                                <div class="voice-container">
                                    <div class="voice-info">
                                        <span class="voice-icon">🎵</span>
                                        <span class="voice-text">微信语音消息</span>
                                    </div>
                                    <audio controls class="attachment-audio" preload="metadata">
                                        <source src="${escapeHtml(url)}" type="audio/mpeg">
                                        您的浏览器不支持音频播放
                                    </audio>
                                </div>
                            </div>
                        `;
                    }
                    break;

                case 'video':
                    icon = '🎬';
                    className += ' attachment-video';
                    if (url) {
                        attachmentContent = `
                            <div class="attachment-preview">
                                <div class="video-container">
                                    <video controls class="attachment-video-preview" preload="metadata">
                                        <source src="${escapeHtml(url)}" type="video/mp4">
                                        <source src="${escapeHtml(url)}" type="video/webm">
                                        您的浏览器不支持视频播放
                                    </video>
                                    <div class="video-controls">
                                        <button onclick="toggleVideoSize(this)" class="video-btn">放大</button>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    break;

                case 'file':
                    icon = '📄';
                    className += ' attachment-file';
                    if (url) {
                        const fileExt = getFileExtension(filename || url).toLowerCase();

                        if (fileExt === 'pdf') {
                            // PDF文件直接嵌入显示
                            attachmentContent = `
                                <div class="attachment-preview">
                                    <div class="pdf-container">
                                        <iframe src="${escapeHtml(url)}"
                                                class="pdf-viewer"
                                                frameborder="0"
                                                title="PDF预览">
                                            <p>您的浏览器不支持PDF预览。</p>
                                        </iframe>
                                    </div>
                                    <div class="pdf-controls">
                                        <button onclick="togglePdfSize(this)" class="pdf-btn">放大</button>
                                        <a href="${escapeHtml(url)}" target="_blank" class="pdf-btn">新窗口打开</a>
                                    </div>
                                </div>
                            `;
                        } else if (fileExt === 'txt') {
                            // 文本文件显示
                            attachmentContent = `
                                <div class="attachment-preview">
                                    <div class="text-container" id="text-${Date.now()}">
                                        <button onclick="loadTextFile('${escapeHtml(url)}', 'text-${Date.now()}')" class="load-text-btn">点击加载文本内容</button>
                                    </div>
                                </div>
                            `;
                        }
                    }
                    break;

                case 'link':
                    icon = '🔗';
                    className += ' attachment-link';
                    // 链接类型主要依靠超链接
                    break;

                case 'location':
                    icon = '📍';
                    className += ' attachment-location';
                    break;

                case 'emoji':
                    icon = '😊';
                    className += ' attachment-emoji';
                    break;

                case 'voice_call':
                    icon = '📞';
                    className += ' attachment-voice-call';
                    if (msg.call_duration) {
                        attachmentContent = `
                            <div class="attachment-preview">
                                <div class="voice-call-container">
                                    <div class="call-info">
                                        <span class="call-icon">📞</span>
                                        <div class="call-details">
                                            <div class="call-type">语音通话</div>
                                            <div class="call-duration">${escapeHtml(msg.call_duration)}</div>
                                        </div>
                                    </div>
                                    <div class="call-status">
                                        <span class="call-status-text">通话已结束</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                    break;

                default:
                    icon = '📎';
                    className += ' attachment-unknown';
            }

            return `
                <div class="message-bubble ${className}">
                    <div class="attachment-content">
                        <div class="attachment-header">
                            <span class="attachment-icon">${icon}</span>
                            <span class="attachment-description">${escapeHtml(description)}</span>
                            ${size ? `<span class="attachment-size">${escapeHtml(size)}</span>` : ''}
                        </div>
                        ${filename ? `<div class="attachment-filename">${escapeHtml(filename)}</div>` : ''}
                        ${attachmentContent}
                        ${linkHtml}
                        ${url && !linkHtml ? `<div class="attachment-url"><a href="${escapeHtml(url)}" target="_blank" class="attachment-link-btn">🔗 ${escapeHtml(url)}</a></div>` : ''}
                    </div>
                </div>
            `;
        }

        // 获取文件扩展名
        function getFileExtension(filename) {
            if (!filename) return '';
            const lastDot = filename.lastIndexOf('.');
            return lastDot > 0 ? filename.substring(lastDot + 1) : '';
        }

        // 重试加载图片
        function retryLoadImage(url, button) {
            const container = button.closest('.image-container');
            const img = container.querySelector('.attachment-image-preview');
            const errorDiv = container.querySelector('.attachment-error');

            button.textContent = '加载中...';
            button.disabled = true;

            // 添加时间戳避免缓存
            const retryUrl = url + (url.includes('?') ? '&' : '?') + 't=' + Date.now();

            img.src = retryUrl;
            img.onload = function() {
                img.style.display = 'block';
                errorDiv.style.display = 'none';
            };
            img.onerror = function() {
                button.textContent = '重试';
                button.disabled = false;
            };
        }

        // 切换PDF大小
        function togglePdfSize(button) {
            const pdfViewer = button.closest('.attachment-preview').querySelector('.pdf-viewer');

            if (pdfViewer.classList.contains('expanded')) {
                pdfViewer.classList.remove('expanded');
                button.textContent = '放大';
            } else {
                pdfViewer.classList.add('expanded');
                button.textContent = '缩小';
            }
        }

        // 切换视频大小
        function toggleVideoSize(button) {
            const video = button.closest('.video-container').querySelector('.attachment-video-preview');

            if (video.classList.contains('expanded')) {
                video.classList.remove('expanded');
                video.style.maxWidth = '300px';
                video.style.maxHeight = '200px';
                button.textContent = '放大';
            } else {
                video.classList.add('expanded');
                video.style.maxWidth = '500px';
                video.style.maxHeight = '400px';
                button.textContent = '缩小';
            }
        }

        // 加载文本文件内容
        async function loadTextFile(url, containerId) {
            const container = document.getElementById(containerId);

            try {
                container.innerHTML = '<div class="loading">正在加载文本内容...</div>';

                // 尝试直接获取文件内容
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const text = await response.text();

                // 限制显示长度
                const maxLength = 5000;
                const displayText = text.length > maxLength ?
                    text.substring(0, maxLength) + '\n\n... (文件太大，只显示前5000字符)' :
                    text;

                container.innerHTML = `<div class="text-content">${escapeHtml(displayText)}</div>`;

            } catch (error) {
                console.error('加载文本文件失败:', error);
                container.innerHTML = `
                    <div class="error">
                        <p>加载失败: ${error.message}</p>
                        <a href="${escapeHtml(url)}" target="_blank" class="pdf-btn">在新窗口中打开</a>
                    </div>
                `;
            }
        }

        // 重试显示图片
        function tryDisplayImage(url, button) {
            const container = button.closest('.attachment-preview');
            const img = container.querySelector('.attachment-image-preview');
            const errorDiv = container.querySelector('.attachment-error');

            // 尝试代理URL
            const proxyUrl = `/proxy_file?url=${encodeURIComponent(url)}`;

            button.textContent = '加载中...';
            button.disabled = true;

            const newImg = new Image();
            newImg.onload = function() {
                img.src = proxyUrl;
                img.style.display = 'block';
                errorDiv.style.display = 'none';
            };
            newImg.onerror = function() {
                button.textContent = '加载失败';
                button.disabled = false;
            };
            newImg.src = proxyUrl;
        }

        // 加载PDF查看器
        function loadPdfViewer(viewerUrl, button) {
            const container = button.closest('.attachment-preview');
            const iframe = container.querySelector('.attachment-pdf-preview');

            button.textContent = '加载中...';
            button.disabled = true;

            iframe.src = viewerUrl;
            iframe.onload = function() {
                iframe.style.display = 'block';
                container.querySelector('.pdf-fallback').style.display = 'none';
            };
            iframe.onerror = function() {
                button.textContent = '加载失败';
                button.disabled = false;
            };
        }

        // 加载Office查看器
        function loadOfficeViewer(viewerUrl, button) {
            const container = button.closest('.attachment-preview');
            const iframe = container.querySelector('.attachment-office-preview');

            button.textContent = '加载中...';
            button.disabled = true;

            iframe.src = viewerUrl;
            iframe.onload = function() {
                iframe.style.display = 'block';
                container.querySelector('.office-fallback').style.display = 'none';
            };
            iframe.onerror = function() {
                button.textContent = '加载失败';
                button.disabled = false;
            };
        }

        // 转换为文本显示
        async function convertToText(url, button) {
            const container = button.closest('.attachment-preview');

            button.textContent = '转换中...';
            button.disabled = true;

            try {
                // 尝试通过代理获取文件并显示为文本
                const proxyUrl = `/proxy_file?url=${encodeURIComponent(url)}`;
                const response = await fetch(proxyUrl);

                if (response.ok) {
                    const text = await response.text();

                    // 创建文本显示区域
                    const textDiv = document.createElement('div');
                    textDiv.className = 'attachment-text-preview';
                    textDiv.innerHTML = `<pre class="text-content">${escapeHtml(text.substring(0, 5000))}${text.length > 5000 ? '\n\n... (文件太大，只显示前5000字符)' : ''}</pre>`;

                    // 替换iframe
                    const iframe = container.querySelector('.attachment-office-preview');
                    iframe.style.display = 'none';
                    container.appendChild(textDiv);
                    container.querySelector('.office-fallback').style.display = 'none';
                } else {
                    throw new Error('无法获取文件内容');
                }
            } catch (error) {
                button.textContent = '转换失败';
                button.disabled = false;
                alert('文本转换失败: ' + error.message);
            }
        }

        // 下载文件
        function downloadFile(url, filename) {
            const a = document.createElement('a');
            a.href = url;
            a.download = filename || 'download';
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        // 加载文本文件内容
        async function loadTextContent(url, elementId) {
            const element = document.getElementById(elementId);
            try {
                element.innerHTML = '<div class="loading">正在加载...</div>';
                const response = await fetch(url);
                const text = await response.text();
                element.innerHTML = `<pre class="text-content">${escapeHtml(text)}</pre>`;
            } catch (error) {
                element.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
            }
        }

        // 全屏查看文档
        function openFullscreen(url, type) {
            const modal = document.createElement('div');
            modal.className = 'fullscreen-modal';

            let content = '';
            if (type === 'pdf') {
                content = `<iframe src="${escapeHtml(url)}" class="fullscreen-iframe"></iframe>`;
            } else if (type === 'office') {
                content = `<iframe src="${escapeHtml(url)}" class="fullscreen-iframe"></iframe>`;
            } else {
                content = `<iframe src="${escapeHtml(url)}" class="fullscreen-iframe"></iframe>`;
            }

            modal.innerHTML = `
                <div class="fullscreen-content">
                    <div class="fullscreen-header">
                        <span class="fullscreen-title">文档预览</span>
                        <span class="fullscreen-close" onclick="this.closest('.fullscreen-modal').remove()">&times;</span>
                    </div>
                    ${content}
                </div>
            `;

            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            document.body.appendChild(modal);
        }

        // 打开图片模态框
        function openImageModal(url, filename) {
            const modal = document.createElement('div');
            modal.className = 'image-modal';
            modal.innerHTML = `
                <div class="image-modal-content">
                    <span class="image-modal-close" onclick="this.parentElement.parentElement.remove()">&times;</span>
                    <img src="${escapeHtml(url)}" alt="${escapeHtml(filename)}" class="image-modal-img">
                    <div class="image-modal-caption">${escapeHtml(filename)}</div>
                </div>
            `;

            modal.onclick = function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            };

            document.body.appendChild(modal);
        }

        // 获取用户图标
        function getUserIcon(username) {
            if (!username) return '?';
            // 提取中文字符或第一个字符
            const match = username.match(/[\u4e00-\u9fa5]/);
            return match ? match[0] : username.charAt(0).toUpperCase();
        }



        // 显示聊天记录
        function displayChatlog(chatData) {
            const chatTitle = document.getElementById('chatTitle');
            const chatInfo = document.getElementById('chatInfo');
            const chatMessages = document.getElementById('chatMessages');
            const chatActions = document.getElementById('chatActions');

            // 更新标题和信息
            chatTitle.textContent = `${chatData.contact_info.remark} (ID: ${chatData.contact_info.id})`;
            chatInfo.innerHTML = `
                <div>昵称: ${chatData.contact_info.nickname || '无'} | 用户: ${chatData.contact_info.user}</div>
                <div class="stats">
                    <span class="stat-item">总消息: <span class="stat-value">${chatData.total_count}</span></span>
                    <span class="stat-item">文本消息: <span class="stat-value">${chatData.text_count}</span></span>
                </div>
            `;

            // 显示分析按钮并存储当前聊天数据
            if (chatData.messages.length > 0) {
                chatActions.style.display = 'block';
                window.currentChatData = chatData;
            } else {
                chatActions.style.display = 'none';
            }

            // 显示消息
            if (chatData.messages.length === 0) {
                chatMessages.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">📝</div>
                        <div>暂无文本聊天记录</div>
                    </div>
                `;
                return;
            }

            const messagesHtml = chatData.messages.map(msg => {
                const time = formatTime(msg.time || msg.timestamp);
                const content = msg.content || msg.message || '';
                const isSelf = msg.isSelf || false;
                const sender = msg.sender || '';
                const messageType = msg.type || 'text';

                // 处理不同类型的消息
                let messageContent = '';
                if (messageType === 'attachment') {
                    messageContent = formatAttachmentMessage(msg);
                } else {
                    messageContent = `<div class="message-bubble">${escapeHtml(content)}</div>`;
                }

                return `
                    <div class="message ${isSelf ? 'self' : 'other'}">
                        <div class="avatar">${isSelf ? '我' : getAvatarText(sender || chatData.contact_info.nickname || chatData.contact_info.remark)}</div>
                        <div>
                            ${messageContent}
                            <div class="message-time">${time} ${sender ? '- ' + sender : ''}</div>
                        </div>
                    </div>
                `;
            }).join('');

            chatMessages.innerHTML = messagesHtml;
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 显示加载状态
        function showLoading() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="loading">
                    <div>⏳ 正在加载聊天记录...</div>
                </div>
            `;
        }

        // 显示错误
        function showError(message) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = `
                <div class="error">
                    <div>❌ ${message}</div>
                </div>
            `;
        }

        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '未知时间';
            
            try {
                const date = new Date(timeStr);
                return date.toLocaleString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch {
                return timeStr.substring(0, 16);
            }
        }

        // 获取头像文字
        function getAvatarText(name) {
            if (!name) return '?';
            // 提取中文字符或第一个字符
            const match = name.match(/[\u4e00-\u9fa5]/);
            return match ? match[0] : name.charAt(0).toUpperCase();
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchContact();
            }
        });

        // 页面加载时自动加载用户列表
        window.addEventListener('load', function() {
            loadUsers();
        });

        // 智能分析相关函数
        async function analyzeCurrentChat() {
            if (!window.currentChatData) {
                alert('请先选择一个聊天记录');
                return;
            }

            const analyzeBtn = document.getElementById('analyzeBtn');
            const modal = document.getElementById('analysisModal');
            const modalBody = document.getElementById('analysisBody');

            // 禁用按钮
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '<span class="analyze-icon">⏳</span><span class="analyze-text">分析中...</span>';

            // 显示模态框和加载状态
            modal.classList.add('show');
            modalBody.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>正在分析聊天记录，请稍候...</p>
                </div>
            `;

            try {
                const contactId = window.currentChatData.contact_info.id;
                const response = await fetch(`/api/analyze_chat/${contactId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(window.currentChatData)
                });

                const result = await response.json();

                if (result.status === 'success') {
                    displayAnalysisResult(result.data);
                } else {
                    throw new Error(result.message || '分析失败');
                }
            } catch (error) {
                console.error('分析失败:', error);
                modalBody.innerHTML = `
                    <div class="error-message">
                        <h3>❌ 分析失败</h3>
                        <p>错误信息: ${error.message}</p>
                        <p>请稍后重试或联系管理员。</p>
                    </div>
                `;
            } finally {
                // 恢复按钮状态
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '<span class="analyze-icon">🧠</span><span class="analyze-text">智能分析</span>';
            }
        }

        function displayAnalysisResult(analysisData) {
            const modalBody = document.getElementById('analysisBody');

            // 从分析结果中提取标签
            const extractedTags = extractTagsFromAnalysisReport(analysisData.analysis || '');

            modalBody.innerHTML = `
                <div class="analysis-result">
                    <div class="analysis-summary">
                        <h3>📊 分析概览</h3>
                        <p><strong>分析时间:</strong> ${new Date().toLocaleString()}</p>
                        <p><strong>联系人:</strong> ${window.currentChatData.contact_info.remark}</p>
                        <p><strong>消息数量:</strong> ${window.currentChatData.total_count} 条</p>
                        ${extractedTags.comprehensive.length > 0 ? `
                        <div class="auto-extracted-tags">
                            <p><strong>🏷️ 自动提取标签:</strong></p>
                            <div class="tags-container">
                                ${extractedTags.comprehensive.map(tag => `<span class="tag tag-auto">${tag}</span>`).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>

                    <div class="analysis-content">
                        ${formatAnalysisContent(analysisData.analysis || '分析结果为空')}
                    </div>

                    <div class="analysis-actions">
                        <button onclick="copyAnalysisResult()" class="copy-btn">📋 复制结果</button>
                        <button onclick="exportAnalysisResult()" class="export-btn">💾 导出报告</button>
                        ${extractedTags.comprehensive.length > 0 ?
                            `<button onclick="applyAutoExtractedTags()" class="apply-auto-tags-btn">✅ 应用自动标签</button>` :
                            `<button onclick="extractTagsFromReport()" class="extract-tags-btn">🏷️ 手动提取标签</button>`
                        }
                        <button onclick="generateFollowUpMessages()" class="generate-followup-btn">💬 生成跟进消息</button>
                    </div>

                    ${extractedTags.comprehensive.length > 0 ? `
                    <div class="categorized-tags-container">
                        <h4>🏷️ 分类标签详情</h4>
                        <div class="tags-categories">
                            ${extractedTags.judgment.length > 0 ? `
                            <div class="tag-category">
                                <h5>1️⃣ 判断备注</h5>
                                <div class="tags-container">
                                    ${extractedTags.judgment.map(tag => `<span class="tag tag-judgment">${tag}</span>`).join('')}
                                </div>
                            </div>
                            ` : ''}

                            ${extractedTags.manual.length > 0 ? `
                            <div class="tag-category">
                                <h5>2️⃣ 人工检索</h5>
                                <div class="tags-container">
                                    ${extractedTags.manual.map(tag => `<span class="tag tag-manual">${tag}</span>`).join('')}
                                </div>
                            </div>
                            ` : ''}

                            ${extractedTags.ai.length > 0 ? `
                            <div class="tag-category">
                                <h5>3️⃣ AI对话</h5>
                                <div class="tags-container">
                                    ${extractedTags.ai.map(tag => `<span class="tag tag-ai">${tag}</span>`).join('')}
                                </div>
                            </div>
                            ` : ''}

                            ${extractedTags.remark.length > 0 ? `
                            <div class="tag-category">
                                <h5>4️⃣ 备注提取</h5>
                                <div class="tags-container">
                                    ${extractedTags.remark.map(tag => `<span class="tag tag-remark">${tag}</span>`).join('')}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    ` : ''}

                    <div id="extractedTagsContainer" class="extracted-tags-container" style="display: none;">
                        <h4>🏷️ 手动提取的标签</h4>
                        <div id="extractedTagsList" class="tags-container"></div>
                        <div class="tags-actions">
                            <button onclick="applyExtractedTags()" class="apply-tags-btn">✅ 应用标签</button>
                            <button onclick="clearExtractedTags()" class="clear-tags-btn">🗑️ 清除</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 从分析报告中提取新格式的标签
        function extractTagsFromAnalysisReport(analysisText) {
            const tags = {
                judgment: [],
                manual: [],
                ai: [],
                remark: [],
                comprehensive: []
            };

            // 查找标签总结部分
            const tagSummaryMatch = analysisText.match(/=== 候选人标签总结 ===([\s\S]*?)```/);
            if (!tagSummaryMatch) {
                console.log('⚠️ 未找到标签总结格式');
                return tags;
            }

            const tagSummaryText = tagSummaryMatch[1];

            // 解析各类标签
            const judgmentMatch = tagSummaryText.match(/【判断备注】([^【\n]+)/);
            if (judgmentMatch) {
                tags.judgment = judgmentMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
            }

            const manualMatch = tagSummaryText.match(/【人工检索】([^【\n]+)/);
            if (manualMatch) {
                tags.manual = manualMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
            }

            const aiMatch = tagSummaryText.match(/【AI对话】([^【\n]+)/);
            if (aiMatch) {
                tags.ai = aiMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
            }

            const remarkMatch = tagSummaryText.match(/【备注提取】([^【\n]+)/);
            if (remarkMatch) {
                tags.remark = remarkMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
            }

            const comprehensiveMatch = tagSummaryText.match(/【综合标签】([^【\n]+)/);
            if (comprehensiveMatch) {
                tags.comprehensive = comprehensiveMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
            }

            console.log('🏷️ 解析到的标签:', tags);
            return tags;
        }

        // 应用自动提取的标签
        function applyAutoExtractedTags() {
            const autoTags = document.querySelectorAll('.tag-auto');
            const tags = Array.from(autoTags).map(tag => tag.textContent.trim());

            if (tags.length === 0) {
                showMessage('❌ 没有可应用的自动标签', 'error');
                return;
            }

            // 更新联系人信息区域的标签显示（追加，不覆盖）
            updateContactTagsDisplay(tags);

            showMessage(`✅ 已应用 ${tags.length} 个自动提取的标签`, 'success');
        }

        // 标签提取功能
        function extractTagsFromReport() {
            const analysisContent = document.querySelector('.analysis-content');
            if (!analysisContent) {
                alert('❌ 未找到分析报告内容');
                return;
            }

            // 获取分析报告的文本内容
            const reportText = analysisContent.innerText || analysisContent.textContent;

            if (!reportText || reportText.trim().length === 0) {
                alert('❌ 分析报告内容为空');
                return;
            }

            console.log('🏷️ 开始提取标签，报告长度:', reportText.length);

            // 显示加载状态
            const extractBtn = document.querySelector('.extract-tags-btn');
            const originalText = extractBtn.textContent;
            extractBtn.textContent = '🔄 提取中...';
            extractBtn.disabled = true;

            // 发送请求到后端提取标签
            fetch('/api/extract_tags_from_report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    analysis_report: reportText
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('🏷️ 标签提取结果:', data);

                if (data.status === 'success' && data.tags && data.tags.length > 0) {
                    displayExtractedTags(data.tags);
                    showMessage('✅ 成功提取 ' + data.tags.length + ' 个标签', 'success');
                } else {
                    showMessage('⚠️ ' + (data.message || '未能提取到标签'), 'warning');
                }
            })
            .catch(error => {
                console.error('❌ 标签提取失败:', error);
                showMessage('❌ 标签提取失败: ' + error.message, 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                extractBtn.textContent = originalText;
                extractBtn.disabled = false;
            });
        }

        function displayExtractedTags(tags) {
            const container = document.getElementById('extractedTagsContainer');
            const tagsList = document.getElementById('extractedTagsList');

            // 清空现有标签
            tagsList.innerHTML = '';

            // 添加新标签
            tags.forEach(tag => {
                const tagElement = document.createElement('span');
                tagElement.className = 'tag tag-extracted';
                tagElement.textContent = tag;
                tagElement.onclick = () => removeExtractedTag(tagElement, tag);
                tagElement.title = '点击删除此标签';
                tagsList.appendChild(tagElement);
            });

            // 显示标签容器
            container.style.display = 'block';

            // 滚动到标签区域
            container.scrollIntoView({ behavior: 'smooth' });
        }

        function removeExtractedTag(tagElement, tag) {
            if (confirm(`确定要删除标签 "${tag}" 吗？`)) {
                tagElement.remove();

                // 如果没有标签了，隐藏容器
                const tagsList = document.getElementById('extractedTagsList');
                if (tagsList.children.length === 0) {
                    document.getElementById('extractedTagsContainer').style.display = 'none';
                }
            }
        }

        function applyExtractedTags() {
            const tagsList = document.getElementById('extractedTagsList');
            const tags = Array.from(tagsList.children).map(tag => tag.textContent);

            if (tags.length === 0) {
                alert('❌ 没有可应用的标签');
                return;
            }

            // 更新联系人信息区域的标签显示（追加，不覆盖）
            updateContactTagsDisplay(tags);

            showMessage(`✅ 已追加 ${tags.length} 个标签到联系人信息`, 'success');

            // 隐藏提取的标签容器
            document.getElementById('extractedTagsContainer').style.display = 'none';
        }

        function clearExtractedTags() {
            if (confirm('确定要清除所有提取的标签吗？')) {
                document.getElementById('extractedTagsList').innerHTML = '';
                document.getElementById('extractedTagsContainer').style.display = 'none';
            }
        }

        function updateContactTagsDisplay(newTags) {
            // 在聊天记录页面中，标签应该追加到 chatInfo 区域
            const chatInfo = document.getElementById('chatInfo');
            if (!chatInfo) {
                console.error('❌ 未找到 chatInfo 区域');
                return;
            }

            // 查找现有的标签区域
            let existingTagsSection = chatInfo.querySelector('.contact-tags-section');
            let existingTags = [];

            // 如果已经有标签区域，获取现有标签
            if (existingTagsSection) {
                const existingTagElements = existingTagsSection.querySelectorAll('.tag');
                existingTags = Array.from(existingTagElements)
                    .map(tag => tag.textContent.trim())
                    .filter(tag => tag.length > 0);
            }

            // 合并标签，去重
            const allTags = [...new Set([...existingTags, ...newTags])];

            // 如果已有标签区域，先移除
            if (existingTagsSection) {
                existingTagsSection.remove();
            }

            // 创建新的标签区域
            const tagsHtml = `
                <div class="contact-tags-section" style="margin-top: 15px;">
                    <div class="tags-header">
                        <div style="font-size: 14px; color: #333; margin-bottom: 10px; font-weight: 600;">🏷️ 联系人标签</div>
                        <div class="tags-controls">
                            <button onclick="addNewTag()" class="add-tag-btn" title="添加标签">➕</button>
                            <button onclick="toggleEditMode()" class="edit-mode-btn" title="编辑模式">✏️</button>
                        </div>
                    </div>

                    <!-- 综合标签 -->
                    <div class="comprehensive-tags" style="margin-bottom: 15px;">
                        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">综合标签:</div>
                        <div class="tags-container" id="comprehensiveTagsContainer">
                            ${allTags.map(tag => `<span class="tag tag-comprehensive editable-tag">
                                <span class="tag-text">${tag}</span>
                                <span class="tag-delete" onclick="deleteTag(this)" title="删除标签">×</span>
                            </span>`).join('')}
                        </div>
                    </div>

                    <!-- 添加新标签输入框（默认隐藏） -->
                    <div class="add-tag-input" id="addTagInput" style="display: none; margin-top: 10px;">
                        <input type="text" placeholder="输入新标签" style="padding: 5px; border: 1px solid #ddd; border-radius: 4px; margin-right: 5px;">
                        <button onclick="confirmAddTag()" style="padding: 5px 10px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">确定</button>
                        <button onclick="cancelAddTag()" style="padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 5px;">取消</button>
                    </div>
                </div>
            `;

            // 追加到 chatInfo 区域
            chatInfo.insertAdjacentHTML('beforeend', tagsHtml);

            console.log(`✅ 已更新联系人标签显示，共 ${allTags.length} 个标签`);
        }

        function createEditableTag(tagText) {
            const tagElement = document.createElement('span');
            tagElement.className = 'tag tag-applied editable-tag';
            tagElement.innerHTML = `
                <span class="tag-text">${tagText}</span>
                <span class="tag-delete" onclick="deleteTag(this)" title="删除标签">×</span>
            `;
            return tagElement;
        }

        function addNewTag() {
            const addTagInput = document.getElementById('addTagInput');
            if (addTagInput) {
                addTagInput.style.display = 'flex';
                const input = addTagInput.querySelector('input');
                input.focus();
                input.value = '';

                // 添加回车键支持
                input.onkeypress = function(e) {
                    if (e.key === 'Enter') {
                        confirmAddTag();
                    } else if (e.key === 'Escape') {
                        cancelAddTag();
                    }
                };
            }
        }

        function confirmAddTag() {
            const addTagInput = document.getElementById('addTagInput');
            const input = addTagInput.querySelector('input');
            const newTagText = input.value.trim();

            if (newTagText.length === 0) {
                showMessage('❌ 标签内容不能为空', 'error');
                return;
            }

            if (newTagText.length > 20) {
                showMessage('❌ 标签长度不能超过20个字符', 'error');
                return;
            }

            // 检查是否已存在
            const existingTags = Array.from(document.querySelectorAll('#comprehensiveTagsContainer .tag-text'))
                .map(tag => tag.textContent.trim());

            if (existingTags.includes(newTagText)) {
                showMessage('❌ 标签已存在', 'warning');
                return;
            }

            // 添加新标签到综合标签容器
            const tagsContainer = document.getElementById('comprehensiveTagsContainer');
            if (tagsContainer) {
                const newTagHtml = `
                    <span class="tag tag-comprehensive editable-tag">
                        <span class="tag-text">${newTagText}</span>
                        <span class="tag-delete" onclick="deleteTag(this)" title="删除标签">×</span>
                    </span>
                `;
                tagsContainer.insertAdjacentHTML('beforeend', newTagHtml);
            }

            // 隐藏输入框
            addTagInput.style.display = 'none';

            showMessage(`✅ 已添加标签: ${newTagText}`, 'success');
        }

        function cancelAddTag() {
            const addTagInput = document.getElementById('addTagInput');
            addTagInput.style.display = 'none';
        }

        function deleteTag(deleteBtn) {
            const tagElement = deleteBtn.closest('.tag');
            const tagText = tagElement.querySelector('.tag-text').textContent;

            if (confirm(`确定要删除标签 "${tagText}" 吗？`)) {
                tagElement.remove();
                showMessage(`✅ 已删除标签: ${tagText}`, 'success');
            }
        }

        function toggleEditMode() {
            const tagsContainer = document.getElementById('contactTagsWrapper');
            const editBtn = document.querySelector('.edit-mode-btn');

            if (tagsContainer.classList.contains('edit-mode')) {
                // 退出编辑模式
                tagsContainer.classList.remove('edit-mode');
                editBtn.textContent = '✏️';
                editBtn.title = '编辑模式';
                showMessage('✅ 已退出编辑模式', 'info');
            } else {
                // 进入编辑模式
                tagsContainer.classList.add('edit-mode');
                editBtn.textContent = '👁️';
                editBtn.title = '查看模式';
                showMessage('✏️ 已进入编辑模式，点击×删除标签', 'info');
            }
        }

        function showMessage(message, type = 'info') {
            // 创建消息提示
            const messageDiv = document.createElement('div');
            messageDiv.className = `message message-${type}`;
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            // 设置背景色
            switch(type) {
                case 'success':
                    messageDiv.style.backgroundColor = '#4CAF50';
                    break;
                case 'warning':
                    messageDiv.style.backgroundColor = '#FF9800';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#F44336';
                    break;
                default:
                    messageDiv.style.backgroundColor = '#2196F3';
            }

            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        function formatAnalysisContent(content) {
            // 将AI返回的文本格式化为HTML
            return content
                .replace(/### (.*?)$/gm, '<h3>$1</h3>')
                .replace(/## (.*?)$/gm, '<h3>$1</h3>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\n\n/g, '</p><p>')
                .replace(/\n/g, '<br>')
                .replace(/^/, '<p>')
                .replace(/$/, '</p>');
        }

        function closeAnalysisModal() {
            const modal = document.getElementById('analysisModal');
            modal.classList.remove('show');
        }

        function copyAnalysisResult() {
            const analysisContent = document.querySelector('.analysis-result').innerText;
            navigator.clipboard.writeText(analysisContent).then(() => {
                alert('分析结果已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择文本复制');
            });
        }

        function exportAnalysisResult() {
            const analysisContent = document.querySelector('.analysis-result').innerText;
            const contactName = window.currentChatData.contact_info.remark;
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

            const blob = new Blob([analysisContent], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `聊天分析报告_${contactName}_${timestamp}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 点击模态框外部关闭
        document.getElementById('analysisModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAnalysisModal();
            }
        });

        // 生成跟进消息
        async function generateFollowUpMessages() {
            if (!window.currentChatData) {
                alert('请先选择一个聊天记录');
                return;
            }

            // 检查是否有分析结果
            const analysisContent = document.querySelector('.analysis-result');
            if (!analysisContent) {
                alert('请先进行智能分析');
                return;
            }

            const modal = document.getElementById('followupModal');
            const modalBody = document.getElementById('followupBody');

            // 显示模态框和加载状态
            modal.classList.add('show');
            modalBody.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner"></div>
                    <p>正在生成跟进消息，请稍候...</p>
                </div>
            `;

            try {
                // 获取分析结果和候选人信息
                const analysisResult = analysisContent.innerText || analysisContent.textContent;
                const candidateName = window.currentChatData.contact_info.remark || '';

                // 获取候选人标签（如果有的话）
                let candidateTags = {};
                const tagsContainer = document.getElementById('comprehensiveTagsContainer');
                if (tagsContainer) {
                    const tagElements = tagsContainer.querySelectorAll('.tag');
                    candidateTags.comprehensive = Array.from(tagElements).map(tag => tag.textContent.trim());
                }

                const response = await fetch('/api/generate_follow_up_messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        analysis_result: analysisResult,
                        candidate_tags: candidateTags,
                        candidate_name: candidateName
                    })
                });

                const result = await response.json();

                if (result.status === 'success') {
                    displayFollowUpMessages(result.data);
                } else {
                    throw new Error(result.message || '生成跟进消息失败');
                }

            } catch (error) {
                console.error('生成跟进消息失败:', error);
                modalBody.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #f44336;">
                        <h3>❌ 生成失败</h3>
                        <p>${error.message}</p>
                        <button onclick="closeFollowupModal()" class="btn" style="margin-top: 15px;">关闭</button>
                    </div>
                `;
            }
        }

        // 显示跟进消息结果
        function displayFollowUpMessages(data) {
            const modalBody = document.getElementById('followupBody');

            const qualification = data.qualification || '未知';
            const professionalTags = data.professional_tags || [];
            const followUpMessages = data.follow_up_messages || {};

            let qualificationHtml = '';
            if (qualification === '合格') {
                qualificationHtml = `
                    <div class="qualification-section qualification-qualified">
                        <h3>✅ 候选人资格评估</h3>
                        <p><strong>评估结果：</strong><span style="color: #4CAF50; font-weight: bold;">合格候选人</span></p>
                        <p>该候选人符合珠三角/长三角高层次人才招募标准，建议进行后续跟进。</p>
                    </div>
                `;
            } else {
                qualificationHtml = `
                    <div class="qualification-section qualification-unqualified">
                        <h3>❌ 候选人资格评估</h3>
                        <p><strong>评估结果：</strong><span style="color: #F44336; font-weight: bold;">不合格</span></p>
                        <p>该候选人暂不符合当前招募标准，建议暂缓跟进或调整策略。</p>
                    </div>
                `;
            }

            let professionalTagsHtml = '';
            if (professionalTags.length > 0) {
                professionalTagsHtml = `
                    <div class="professional-tags-section">
                        <h3>🏷️ 专业领域标签</h3>
                        <div class="professional-tags-container">
                            ${professionalTags.map(tag => `<span class="professional-tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                `;
            }

            let messagesHtml = '';
            if (Object.keys(followUpMessages).length > 0) {
                const messageTimeLabels = {
                    '1_hour': '1小时后跟进',
                    '1_week': '1周后跟进',
                    '3_weeks': '3周后跟进',
                    '1_month': '1个月后跟进',
                    '2_months': '2个月后跟进',
                    '4_months': '4个月后跟进',
                    '6_months': '6个月后跟进'
                };

                messagesHtml = `
                    <div class="followup-messages-section">
                        <h3>💬 跟进消息列表</h3>
                        ${Object.entries(followUpMessages).map(([key, message]) => `
                            <div class="followup-message-item">
                                <div class="followup-message-time">${messageTimeLabels[key] || key}</div>
                                <div class="followup-message-content">${message}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            modalBody.innerHTML = `
                ${qualificationHtml}
                ${professionalTagsHtml}
                ${messagesHtml}

                <div class="followup-actions">
                    <button onclick="copyFollowUpMessages()" class="copy-btn">📋 复制所有消息</button>
                    <button onclick="exportFollowUpMessages()" class="export-btn">💾 导出消息</button>
                    <button onclick="closeFollowupModal()" class="btn">关闭</button>
                </div>
            `;
        }

        // 关闭跟进消息模态框
        function closeFollowupModal() {
            const modal = document.getElementById('followupModal');
            modal.classList.remove('show');
        }

        // 复制跟进消息
        function copyFollowUpMessages() {
            const messagesContent = document.querySelector('.followup-messages-section');
            if (!messagesContent) {
                alert('没有找到跟进消息内容');
                return;
            }

            const messages = messagesContent.querySelectorAll('.followup-message-item');
            let copyText = '跟进消息列表：\n\n';

            messages.forEach(item => {
                const time = item.querySelector('.followup-message-time').textContent;
                const content = item.querySelector('.followup-message-content').textContent;
                copyText += `${time}:\n${content}\n\n`;
            });

            navigator.clipboard.writeText(copyText).then(() => {
                alert('跟进消息已复制到剪贴板');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            });
        }

        // 导出跟进消息
        function exportFollowUpMessages() {
            const messagesContent = document.querySelector('.followup-messages-section');
            if (!messagesContent) {
                alert('没有找到跟进消息内容');
                return;
            }

            const messages = messagesContent.querySelectorAll('.followup-message-item');
            const candidateName = window.currentChatData.contact_info.remark || '候选人';
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

            let exportText = `跟进消息 - ${candidateName}\n生成时间: ${new Date().toLocaleString()}\n\n`;

            messages.forEach(item => {
                const time = item.querySelector('.followup-message-time').textContent;
                const content = item.querySelector('.followup-message-content').textContent;
                exportText += `${time}:\n${content}\n\n${'='.repeat(50)}\n\n`;
            });

            const blob = new Blob([exportText], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `跟进消息_${candidateName}_${timestamp}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 点击跟进消息模态框外部关闭
        document.getElementById('followupModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeFollowupModal();
            }
        });

    </script>
</body>
</html>
