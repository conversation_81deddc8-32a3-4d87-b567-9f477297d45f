#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import requests
import json

# 简单的测试数据
test_data = {
    "contact_info": {
        "id": "20",
        "remark": "测试联系人",
        "nickname": "Test"
    },
    "messages": [
        {
            "type": "text",
            "content": "你好",
            "time": "2024-01-15 10:00:00",
            "isSelf": True
        }
    ],
    "total_count": 1
}

def test_auto_tags():
    """测试自动标签生成"""
    print("🧪 测试自动标签生成")
    
    url = "http://127.0.0.1:8080/api/auto_generate_tags/20"
    
    try:
        response = requests.post(url, json=test_data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_auto_tags()
