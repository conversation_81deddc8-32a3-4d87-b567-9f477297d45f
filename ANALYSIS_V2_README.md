# 微信联系人重复分析工具 V2

这是重写版本的微信联系人重复分析工具，专门适配新的文件夹结构，能够分析各用户文件夹中的联系人重复情况并生成详细的分析报告。

## 🎯 功能特性

### 适配新结构
- **输入**: `data/用户名/联系人文件.json` 格式
- **自动扫描**: 扫描所有用户文件夹
- **智能过滤**: 自动排除日志文件（new_contacts_log.json, ip_change_log.json）
- **多文件支持**: 支持每个用户有多个联系人文件

### 全面分析
- **重复检测**: 识别在多个用户间重复的联系人
- **跨用户对比**: 分析不同用户间的联系人重叠情况
- **统计分析**: 提供详细的数据统计信息
- **排序展示**: 按重复度排序显示结果

### 多格式报告
- **JSON详细报告**: 包含完整分析数据
- **CSV摘要报告**: 便于Excel查看的重复联系人列表
- **用户对比表**: 矩阵式显示各用户的联系人分布
- **统计报告**: 文本格式的统计摘要
- **分析日志**: 详细的处理过程记录

## 📁 文件结构

### 输入结构
```
data/
├── 张三/
│   ├── 192_168_1_100_张三.json
│   ├── 10_0_0_50_张三.json
│   ├── new_contacts_log.json      # 自动跳过
│   └── ip_change_log.json         # 自动跳过
├── 李四/
│   ├── 192_168_1_200_李四.json
│   └── new_contacts_log.json
└── 王五/
    └── 172_16_1_100_王五.json
```

### 输出结构
```
analysis_reports/
├── duplicate_analysis_YYYYMMDD_HHMMSS.json    # 详细JSON报告
├── duplicate_summary_YYYYMMDD_HHMMSS.csv      # CSV摘要
├── user_comparison_YYYYMMDD_HHMMSS.csv        # 用户对比表
├── statistics_YYYYMMDD_HHMMSS.txt             # 统计报告
└── analysis.log                               # 分析日志
```

## 🚀 使用方法

### 方法1: 使用批处理文件（推荐）
```bash
# 双击运行
run_analysis_v2.bat
```

### 方法2: 直接运行Python脚本
```bash
python analyze_contacts_v2.py
```

## 📊 报告详解

### 1. 详细JSON报告 (duplicate_analysis_*.json)
```json
{
  "analysis_info": {
    "timestamp": "2025-07-08T11:20:35",
    "total_users": 3,
    "total_duplicate_contacts": 36
  },
  "user_summary": {
    "张三": {
      "files_count": 2,
      "total_contacts": 150,
      "files": ["192_168_1_100_张三.json", "10_0_0_50_张三.json"]
    }
  },
  "duplicate_contacts": [
    {
      "contact_key": "filehelper",
      "nickname": "文件传输助手",
      "appears_in_users": ["张三", "李四", "王五"],
      "user_count": 3,
      "file_count": 5
    }
  ]
}
```

### 2. CSV摘要报告 (duplicate_summary_*.csv)
| 联系人标识 | 用户名 | 昵称 | 出现用户数 | 出现的用户 |
|------------|--------|------|------------|------------|
| filehelper | filehelper | 文件传输助手 | 3 | 张三; 李四; 王五 |

### 3. 用户对比表 (user_comparison_*.csv)
| 联系人 | 张三 | 李四 | 王五 | 总出现次数 |
|--------|------|------|------|------------|
| 文件传输助手 | ✓ | ✓ | ✓ | 3 |
| 微信支付 | ✓ | ✓ |  | 2 |

### 4. 统计报告 (statistics_*.txt)
```
微信联系人重复分析统计报告
==================================================

分析时间: 2025-07-08 11:20:35

基本统计:
  总用户数: 3
  总文件数: 5
  总联系人数: 2338
  重复联系人数: 36

重复度最高的联系人 (前10名):
   1. 文件传输助手 (filehelper)
      出现在 3 个用户中
      用户: 张三, 李四, 王五
```

## 🔍 分析结果示例

基于实际运行结果：

### 📈 统计数据
- **总用户数**: 3个用户（小司、小罗、test_ip_change_user）
- **总文件数**: 5个联系人文件
- **总联系人数**: 2,338个
- **重复联系人数**: 36个

### 🏆 重复度最高的联系人
1. **文件传输助手** - 出现在3个用户中
2. **小马@就业创业** - 出现在2个用户中
3. **小熊@海归就业创业** - 出现在2个用户中
4. **漂流瓶** - 出现在2个用户中

### 👥 用户分布
- **小罗**: 1个文件，2,104个联系人
- **小司**: 1个文件，222个联系人  
- **test_ip_change_user**: 3个文件，12个联系人

## ⚙️ 技术特点

### 智能识别
- **联系人匹配**: 优先使用UserName，备用昵称+别名组合
- **文件过滤**: 自动跳过日志文件
- **错误处理**: 完善的异常处理机制

### 性能优化
- **索引建立**: 高效的联系人索引算法
- **内存管理**: 优化的数据结构
- **批量处理**: 支持大量数据处理

### 报告生成
- **多格式输出**: JSON、CSV、TXT多种格式
- **时间戳命名**: 避免文件覆盖
- **详细日志**: 完整的处理过程记录

## 🔧 配置选项

### 修改目录路径
```python
analyzer = ContactAnalyzerV2(
    data_dir="data",                    # 数据目录
    reports_dir="analysis_reports"      # 报告输出目录
)
```

### 自定义联系人匹配
修改 `generate_contact_key` 方法来调整联系人匹配逻辑。

## 📋 使用场景

### 1. 企业管理
- 员工微信联系人重叠分析
- 客户关系网络分析
- 业务联系人分布统计

### 2. 数据分析
- 用户群体特征分析
- 社交网络重叠度研究
- 联系人质量评估

### 3. 系统维护
- 重复数据识别
- 数据清理依据
- 存储优化参考

## 🚨 注意事项

1. **数据结构**: 确保数据按用户文件夹组织
2. **文件格式**: 联系人文件必须是有效的JSON格式
3. **权限要求**: 确保有读取data目录和写入reports目录的权限
4. **内存使用**: 大量数据时注意内存使用情况

## 🆚 与V1版本对比

| 特性 | V1版本 | V2版本 |
|------|--------|--------|
| 输入结构 | 根目录JSON文件 | 用户文件夹结构 |
| 文件过滤 | 手动指定 | 自动识别和过滤 |
| 报告格式 | 单一格式 | 多格式报告 |
| 用户对比 | 不支持 | 支持矩阵对比 |
| 统计分析 | 基础统计 | 详细统计分析 |

V2版本完全适配了新的文件夹结构，提供了更丰富的分析功能和更详细的报告！
