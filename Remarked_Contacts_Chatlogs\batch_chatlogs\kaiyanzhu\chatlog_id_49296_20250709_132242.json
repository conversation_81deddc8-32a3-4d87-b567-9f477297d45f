{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "49296", "contact_info": {"UserName": "wxid_r26uq4ety8tx21", "Alias": "huan6215", "Remark": "49296 杨欢", "NickName": "<PERSON><PERSON>"}, "fetch_time": "2025-07-09T13:22:42.908185", "message_count": 25, "chatlog_data": [{"seq": 1751382527000, "time": "2025-07-01T23:08:47+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 10000, "subType": 0, "content": "<PERSON>an刚刚把你添加到通讯录，现在可以开始聊天了。"}, {"seq": 1751382528000, "time": "2025-07-01T23:08:48+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "I'm <PERSON><PERSON>, from LinkedIn, referred by <PERSON><PERSON><PERSON>"}, {"seq": 1751382528001, "time": "2025-07-01T23:08:48+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": false, "type": 10000, "subType": 0, "content": ""}, {"seq": 1751417293000, "time": "2025-07-02T08:48:13+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 10000, "subType": 0, "content": "你已添加了Huan，现在可以开始聊天了。"}, {"seq": 1751417314000, "time": "2025-07-02T08:48:34+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士您好，您对人才计划感兴趣吗"}, {"seq": 1751417555000, "time": "2025-07-02T08:52:35+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您方便同步一份简历吗"}, {"seq": 1751419569000, "time": "2025-07-02T09:26:09+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "你好，请问可以语音通话吗，我想了解一下细节，谢谢！"}, {"seq": 1751419685000, "time": "2025-07-02T09:28:05+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好呀，您现在有时间吗"}, {"seq": 1751420228000, "time": "2025-07-02T09:37:08+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "十点可以吗？"}, {"seq": 1751420268000, "time": "2025-07-02T09:37:48+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "十点我有约啦，下午或者明天呢"}, {"seq": 1751421023000, "time": "2025-07-02T09:50:23+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "十点半或十一点？"}, {"seq": 1751421054000, "time": "2025-07-02T09:50:54+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "十一点左右我给您拨过去哈"}, {"seq": 1751421069000, "time": "2025-07-02T09:51:09+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1751425135000, "time": "2025-07-02T10:58:55+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "抱歉，博士，我们部门一会要开会，您看我明天早上10点给您拨过去OK吗"}, {"seq": 1751425138000, "time": "2025-07-02T10:58:58+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername=\"wxid_6ahbbsar7g8i22\" tousername=\"wxid_r26uq4ety8tx21\" type=\"2\" androidmd5=\"b0a50b0e304c9ca67e6ffdc8b70e5efe\" androidlen=\"5750\" aeskey=\"0567d93327a1444e9597f309f81c0884\" encrypturl=\"http://vweixinf.tc.qq.com/110/20402/stodownload?m=3c09a49acaecf0b2f11b6764553ad5dd&amp;filekey=30440201010430302e02016e0402534804203363303961343961636165636630623266313162363736343535336164356464020300d160040d00000004627466730000000132&amp;hy=SH&amp;storeid=264398bb200073222340eb23d0000006e02004fb253482d38db01e6b60d861&amp;ef=2&amp;bizid=1022\" externurl=\"\" externmd5=\"\"/></msg>"}, {"seq": 1751425168000, "time": "2025-07-02T10:59:28+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "好的，没问题"}, {"seq": 1751425209000, "time": "2025-07-02T11:00:09+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "嗯嗯，不过您可以先同步一份简历的哦"}, {"seq": 1751508218000, "time": "2025-07-03T10:03:38+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士您好，您现在方便吗"}, {"seq": 1751508219001, "time": "2025-07-03T10:03:39+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[对方无应答]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>567551106303020598</roomid>\n<roomkey>0</roomkey>\n<inviteid>3456468612</inviteid>\n<msg_type>100</msg_type>\n<timestamp>1751508219890</timestamp>\n<identity><![CDATA[8535755947202384656]]></identity>\n<duration>0</duration>\n<inviteid64>1751508158084</inviteid64>\n<business>1</business>\n<caller_memberid>0</caller_memberid>\n<callee_memberid>1</callee_memberid>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1751508250001, "time": "2025-07-03T10:04:10+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[已在其它设备接听]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>1432263827538003258</roomid>\n<roomkey>0</roomkey>\n<inviteid>0</inviteid>\n<msg_type>101</msg_type>\n<timestamp>1751508250646</timestamp>\n<identity><![CDATA[5382845096336746127]]></identity>\n<duration>0</duration>\n<inviteid64>0</inviteid64>\n<business>1</business>\n<caller_memberid>0</caller_memberid>\n<callee_memberid>1</callee_memberid>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1751509460000, "time": "2025-07-03T10:24:20+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "对啦，博士，怎么称呼您哦[可怜]"}, {"seq": 1751509529000, "time": "2025-07-03T10:25:29+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "我的名字，杨欢。您的名字呢？"}, {"seq": 1751509563000, "time": "2025-07-03T10:26:03+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我叫朱开艳，您叫我小朱就好"}, {"seq": 1751509645000, "time": "2025-07-03T10:27:25+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "wxid_r26uq4ety8tx21", "senderName": "49296 杨欢", "isSelf": false, "type": 1, "subType": 0, "content": "好的。谢谢详细的介绍[强]"}, {"seq": 1751509658000, "time": "2025-07-03T10:27:38+08:00", "talker": "wxid_r26uq4ety8tx21", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您早点休息~"}]}