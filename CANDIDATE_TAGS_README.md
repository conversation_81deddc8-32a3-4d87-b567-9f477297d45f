# 🏷️ 候选人标签系统使用指南

## 📋 功能概述

候选人标签系统会在查看聊天记录时自动分析候选人信息，并在联系人昵称后面显示简洁的标签，帮助快速了解候选人特征。

## 🔄 工作流程

### 1. **先显示聊天记录**
- 用户点击联系人后，立即显示聊天内容
- 用户可以正常浏览聊天记录

### 2. **后台自动分析**
- 系统在后台分析全部聊天记录
- 标题显示"🔄 分析中..."状态

### 3. **动态添加标签**
- 分析完成后，在联系人信息下方显示标签
- 标签按颜色分类，便于快速识别

## 🏷️ 标签分类体系

### 📊 标签来源

#### 1. **本地程序判断**
- **昵称解析**: 从昵称中提取专业信息、技术栈、地区、经验等
- **活跃度分析**: 根据消息数量判断活跃程度
- **响应及时性**: 工作时间15分钟内，非工作时间30分钟内
  - 工作时间: 8:30-11:45, 13:00-17:30 (双休制)
  - 分析消息间隔时间，计算及时回复比例
- **通话频率**: 统计聊天记录中的通话次数

#### 2. **人工检索字段**（预留）
- **简历匹配**: 根据简历字段匹配的内容
- **背景验证**: 人工核实的信息

#### 3. **AI对话分析** (整合所有消息内容)
- **内容整合**: 分析所有文本消息和附件消息
- **专业标签**: 从对话中识别技术栈、行业领域、专业技能
- **工作类型**: 兼职/全职偏好分析
- **期望年薪**: 通过正则表达式精确提取薪资信息
- **申报意愿**:
  - 强烈: "非常感兴趣"、"很想"、"迫切"、"急需"
  - 中性: "考虑"、"看看"、"了解一下"、"可以聊聊"
  - 一般: "不太"、"暂时不"、"再说"、"不确定"
- **推荐能力**: 是否愿意推荐朋友或同事
- **特殊人才**: 是否入选过千人计划、青千、优青等特殊人才计划
- **多媒体分析**: 统计图片、语音、文件、简历等附件信息

### 🎨 标签颜色分类

#### 🟢 专业领域标签 (绿色) - 扩展版
**技术开发类**
- **软件开发**: 软件开发、开发工程师、程序员
- **前端**: 前端开发、Web前端、H5开发
- **后端**: 后端开发、服务端开发
- **全栈**: 全栈开发、全栈工程师
- **移动开发**: APP开发、iOS、Android、Flutter
- **游戏开发**: 游戏程序、Unity、UE4
- **嵌入式**: 嵌入式开发、单片机、物联网

**技术专业类**
- **算法**: 算法工程师、机器学习、深度学习
- **数据**: 数据科学、数据分析、大数据
- **测试**: 测试工程师、QA、自动化测试
- **运维**: 运维工程师、DevOps、云运维
- **安全**: 安全工程师、网络安全、信息安全
- **架构师**: 系统架构、技术架构

**产品设计类**
- **产品**: 产品经理、产品策划
- **UI设计**: UI设计师、界面设计、视觉设计
- **UX设计**: UX设计师、用户体验、交互设计
- **平面设计**: 平面设计、品牌设计
- **工业设计**: 产品设计、外观设计

**市场营销类**
- **市场**: 市场营销、品牌营销、数字营销
- **销售**: 销售、客户经理、商务拓展
- **运营**: 产品运营、用户运营、内容运营

**职能支持类**
- **HR**: 人力资源、招聘、培训
- **财务**: 财务、会计、审计、税务
- **法务**: 法务、法律顾问、合规
- **行政**: 行政管理、后勤、采购

**专业技术类**
- **机械**: 机械工程、机械设计、制造工程
- **电子**: 电子工程、硬件工程、电路设计
- **化工**: 化工、化学工程、材料科学
- **医药**: 生物医药、制药、临床研究
- **金融**: 金融、投资、银行、保险
- **咨询**: 管理咨询、战略咨询
- **教育**: 教育培训、老师、讲师

**新兴领域**
- **区块链**: 区块链、数字货币、DeFi
- **AI**: 人工智能、机器学习、NLP
- **物联网**: IoT、智能硬件、传感器
- **新能源**: 太阳能、风能、电池技术
- **自动驾驶**: 无人驾驶、车联网
- **VR/AR**: 虚拟现实、增强现实、元宇宙

#### 🔵 技术技能标签 (蓝色)
- **Java**: Java开发
- **Python**: Python开发
- **前端**: 前端开发
- **后端**: 后端开发
- **全栈**: 全栈开发
- **AI**: 人工智能
- **大数据**: 大数据技术
- **区块链**: 区块链技术
- **云计算**: 云计术

#### 🟠 地理位置标签 (橙色)
- **北京**: 北京地区
- **上海**: 上海地区
- **深圳**: 深圳地区
- **广州**: 广州地区
- **杭州**: 杭州地区
- **成都**: 成都地区
- **海外**: 海外地区

#### 🟣 工作状态标签 (紫色)
- **兼职**: 兼职工作
- **全职**: 全职工作
- **主动求职**: 主动找工作
- **被动求职**: 被动看机会
- **意愿强烈**: 求职意愿强烈
- **意愿中性**: 求职意愿中等
- **意愿一般**: 求职意愿较低

#### 🔴 薪资标签 (红色)
- **10-20万**: 年薪10-20万
- **20-30万**: 年薪20-30万
- **30-50万**: 年薪30-50万
- **50万+**: 年薪50万以上

#### ⚫ 活跃度和沟通标签 (灰色)
**活跃度**
- **高活跃**: 消息>100条，互动频繁
- **活跃**: 消息50-100条，互动积极
- **中等活跃**: 消息20-50条，适中互动
- **低活跃**: 消息5-20条，较少互动
- **很少联系**: 消息<5条，极少互动

**回复及时性**
- **回复及时**: 80%以上消息及时回复
- **回复一般**: 50-80%消息及时回复
- **回复较慢**: <50%消息及时回复

**通话情况**
- **通话频繁**: 通话记录≥5次
- **有通话记录**: 通话记录2-4次
- **偶有通话**: 通话记录1次
- **无通话记录**: 无通话记录

**多媒体内容**
- **有图片**: 聊天中包含图片
- **有语音**: 聊天中包含语音消息
- **有文件**: 聊天中包含文件附件
- **有简历**: 聊天中包含简历文件

#### 🟤 其他标签 (棕色)
- **可推荐**: 可以推荐朋友
- **特殊人才**: 入选过特殊人才计划
- **应届**: 应届毕业生
- **1-3年**: 1-3年工作经验
- **3-5年**: 3-5年工作经验
- **5年+**: 5年以上工作经验

## 🔍 标签提取逻辑

### 从昵称提取标签
```
示例昵称: "3236 Java后端开发 北京 5年经验"
提取标签: [Java, 后端, 北京, 5年+]
```

### 从聊天内容提取标签
```
聊天内容: "我是一名Python开发工程师，有3年经验，目前在考虑全职机会，期望年薪25万左右"
提取标签: [Python, 开发, 3-5年, 全职, 20-30万]
```

### AI分析提取标签
- **情感分析**: 分析求职意愿强度
- **关键词提取**: 识别专业技能和期望
- **语义理解**: 理解深层含义

## 💻 技术实现

### API端点
```
POST /api/generate_tags/{contact_id}
```

### 请求格式
```json
{
  "contact_info": {
    "id": "联系人ID",
    "remark": "联系人昵称"
  },
  "messages": [
    {
      "content": "消息内容",
      "type": "text",
      "time": "时间戳"
    }
  ],
  "total_count": 100
}
```

### 响应格式
```json
{
  "status": "success",
  "data": {
    "candidate_name": "候选人姓名",
    "tags": {
      "basic_info": {...},
      "work_status": {...},
      "professional_skills": {...}
    },
    "tag_summary": ["Java", "后端", "北京", "全职", "20-30万"],
    "recommendation_score": 85
  }
}
```

## 🎯 使用场景

### 1. **快速筛选候选人**
通过标签快速识别符合要求的候选人

### 2. **批量评估**
在联系人列表中快速浏览所有候选人的关键信息

### 3. **精准匹配**
根据标签信息进行职位匹配

### 4. **沟通策略**
根据标签调整沟通方式和话术

## 📈 优化建议

### 1. **标签准确性**
- 定期更新标签提取规则
- 根据实际使用情况调整权重

### 2. **性能优化**
- 缓存分析结果
- 异步处理大量数据

### 3. **用户体验**
- 提供标签编辑功能
- 支持自定义标签

## 🔧 故障排除

### 常见问题

1. **标签不显示**
   - 检查聊天记录是否加载完成
   - 确认网络连接正常

2. **标签不准确**
   - 聊天记录内容可能不够丰富
   - AI分析可能需要更多上下文

3. **分析时间过长**
   - 聊天记录过多时分析时间较长
   - 可以考虑分批处理

### 调试方法
- 查看浏览器控制台输出
- 检查Flask应用日志
- 使用API测试工具验证

## 🚀 未来规划

1. **智能推荐**: 基于标签的职位推荐
2. **趋势分析**: 候选人市场趋势分析
3. **自动匹配**: 自动匹配候选人和职位
4. **报表生成**: 生成候选人分析报表
