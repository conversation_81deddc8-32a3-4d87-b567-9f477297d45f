# 简化版根据ID获取聊天记录工具

import json
import re
import requests
import urllib.parse
from pathlib import Path
from datetime import datetime
import sys
import time

def extract_ip_from_filename(filename):
    """从文件名中提取IP地址"""
    parts = filename.replace('.json', '').split('_')
    if len(parts) >= 4:
        ip_parts = parts[:4]
        return '.'.join(ip_parts)
    return None

def extract_id_from_remark(remark):
    """从备注中提取ID（前面的数字）"""
    if not remark:
        return None
    match = re.match(r'^(\d+)', str(remark).strip())
    return match.group(1) if match else None

def get_user_contacts_and_ip(user_folder):
    """获取用户的联系人数据和IP地址"""
    contact_files = [f for f in user_folder.glob("*.json") 
                    if not f.name.startswith(("new_contacts", "ip_change"))]
    
    if not contact_files:
        return None, None
    
    latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
    ip_address = extract_ip_from_filename(latest_file.name)
    
    if not ip_address:
        return None, None
    
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            contacts = json.load(f)
        return contacts if isinstance(contacts, list) else None, ip_address
    except:
        return None, None

def find_contact_by_id(contacts, target_id):
    """根据ID查找联系人"""
    remark_pattern = re.compile(r'^\d+.*$')
    
    for contact in contacts:
        remark = contact.get("Remark", "")
        if remark and remark_pattern.match(str(remark).strip()):
            contact_id = extract_id_from_remark(remark)
            if contact_id == target_id:
                return contact
    return None

def get_talker_value(contact):
    """获取talker参数值"""
    for field in ['Remark', 'NickName', 'Alias', 'UserName']:
        value = contact.get(field, '')
        if value and str(value).strip():
            return str(value).strip()
    return None

def fetch_chatlog(ip_address, contact):
    """获取聊天记录"""
    talker = get_talker_value(contact)
    if not talker:
        return None, "无有效talker参数"
    
    current_date = datetime.now().strftime("%Y-%m-%d")
    time_range = f"2023-01-01%7E{current_date}"
    encoded_talker = urllib.parse.quote(talker)
    
    api_url = f"http://{ip_address}:5030/api/v1/chatlog?time={time_range}&talker={encoded_talker}&format=json"
    
    try:
        response = requests.get(api_url, timeout=10)
        response.raise_for_status()
        return response.json(), None
    except requests.exceptions.Timeout:
        return None, "请求超时"
    except requests.exceptions.ConnectionError:
        return None, "连接失败"
    except requests.exceptions.HTTPError as e:
        return None, f"HTTP错误: {e}"
    except json.JSONDecodeError:
        return None, "响应不是有效的JSON"
    except Exception as e:
        return None, f"未知错误: {e}"

def print_chatlog(chatlog_data, contact_info, max_messages=50):
    """打印聊天记录到控制台（只显示文本消息）"""
    if not chatlog_data or not isinstance(chatlog_data, list):
        print("📝 无聊天记录")
        return

    if len(chatlog_data) == 0:
        print("📝 无聊天记录")
        return

    # 过滤出文本消息
    text_messages = []
    for msg in chatlog_data:
        msg_type = msg.get('type', msg.get('msgType', ''))
        content = msg.get('content', msg.get('message', ''))

        # 只保留文本消息（type为1或空，且有内容）
        if (msg_type == 1 or msg_type == '1' or not msg_type) and content and str(content).strip():
            text_messages.append(msg)

    print(f"📊 共找到 {len(chatlog_data)} 条聊天记录，其中 {len(text_messages)} 条文本消息")
    print(f"👤 联系人信息:")
    print(f"   备注: {contact_info.get('Remark', '')}")
    print(f"   昵称: {contact_info.get('NickName', '')}")
    print(f"   用户名: {contact_info.get('UserName', '')}")
    print()

    if len(text_messages) == 0:
        print("📝 无文本聊天记录")
        return

    # 显示最近的文本消息
    messages_to_show = text_messages[-max_messages:] if len(text_messages) > max_messages else text_messages

    print("💬 文本聊天记录:")
    print("-" * 80)

    for i, msg in enumerate(messages_to_show, 1):
        # 提取消息字段
        timestamp = msg.get('timestamp', msg.get('time', ''))
        content = msg.get('content', msg.get('message', ''))

        # 格式化时间
        if timestamp:
            try:
                if 'T' in timestamp:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                else:
                    dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                time_str = dt.strftime('%m-%d %H:%M')
            except:
                time_str = str(timestamp)[:16]
        else:
            time_str = "未知时间"

        # 处理文本内容
        msg_content = str(content).strip()

        # 限制内容长度
        if len(msg_content) > 200:
            msg_content = msg_content[:200] + "..."

        print(f"{i:3d}. {time_str} | {msg_content}")

    if len(text_messages) > max_messages:
        print(f"\n... 还有 {len(text_messages) - max_messages} 条更早的文本消息")

def get_chatlog_by_id(target_id):
    """根据ID获取并打印聊天记录"""
    print(f"🔍 查找ID为 {target_id} 的联系人聊天记录...")
    print("=" * 60)

    data_dir = Path("../data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return False

    excluded_folders = {"test_ip_change_user"}
    user_folders = [d for d in data_dir.iterdir()
                   if d.is_dir() and d.name not in excluded_folders]

    print(f"📂 在data目录下找到 {len(user_folders)} 个用户文件夹:")
    for folder in user_folders:
        print(f"   - {folder.name}")
    
    # 搜索联系人
    for user_folder in user_folders:
        username = user_folder.name
        print(f"\n👤 搜索用户: {username}")
        
        contacts, ip_address = get_user_contacts_and_ip(user_folder)
        if not contacts:
            print(f"   ❌ 无法读取联系人数据")
            continue
        
        print(f"   🌐 IP地址: {ip_address}")
        
        # 查找目标联系人
        contact = find_contact_by_id(contacts, target_id)
        if contact:
            print(f"   ✅ 找到联系人!")
            
            # 获取聊天记录
            print(f"\n📱 获取聊天记录...")
            chatlog_data, error = fetch_chatlog(ip_address, contact)
            
            if chatlog_data is not None:
                print(f"   ✅ 成功获取聊天记录")
                print()
                print_chatlog(chatlog_data, contact)
                return True
            else:
                print(f"   ❌ 获取聊天记录失败: {error}")
                return False
        else:
            print(f"   📝 未找到ID为 {target_id} 的联系人")
    
    print(f"\n❌ 在所有用户中都未找到ID为 {target_id} 的联系人")
    return False

def save_chatlog_to_file(chatlog_data, contact_info, username, contact_id):
    """保存聊天记录到文件"""
    output_data = {
        "user": username,
        "contact_id": contact_id,
        "contact_info": {
            "UserName": contact_info.get("UserName", ""),
            "Alias": contact_info.get("Alias", ""),
            "Remark": contact_info.get("Remark", ""),
            "NickName": contact_info.get("NickName", "")
        },
        "fetch_time": datetime.now().isoformat(),
        "message_count": len(chatlog_data) if isinstance(chatlog_data, list) else 0,
        "chatlog_data": chatlog_data
    }

    # 创建用户文件夹
    user_output_dir = Path(f"batch_chatlogs/{username}")
    user_output_dir.mkdir(parents=True, exist_ok=True)

    # 保存文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"chatlog_id_{contact_id}_{timestamp}.json"
    output_file = user_output_dir / filename

    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        print(f"   💾 聊天记录已保存到: {output_file}")
        return output_file

    except Exception as e:
        print(f"   ❌ 保存文件失败: {e}")
        return None

def batch_save_by_id_list(id_list):
    """根据ID列表批量保存聊天记录"""
    print(f"📦 批量保存 {len(id_list)} 个ID的聊天记录...")
    print("=" * 60)

    data_dir = Path("../data")
    if not data_dir.exists():
        print("❌ data目录不存在")
        return False

    excluded_folders = {"test_ip_change_user"}
    user_folders = [d for d in data_dir.iterdir()
                   if d.is_dir() and d.name not in excluded_folders]

    print(f"📂 在data目录下找到 {len(user_folders)} 个用户文件夹")

    success_count = 0
    failed_ids = []

    for i, target_id in enumerate(id_list, 1):
        print(f"\n[{i}/{len(id_list)}] 处理ID: {target_id}")
        print("-" * 40)

        found = False

        # 搜索联系人
        for user_folder in user_folders:
            username = user_folder.name

            contacts, ip_address = get_user_contacts_and_ip(user_folder)
            if not contacts:
                continue

            # 查找目标联系人
            contact = find_contact_by_id(contacts, target_id)
            if contact:
                print(f"   👤 在用户 {username} 中找到联系人")
                print(f"   🌐 IP地址: {ip_address}")
                print(f"   📝 备注: {contact.get('Remark', '')}")
                print(f"   👤 昵称: {contact.get('NickName', '')}")

                # 获取聊天记录
                print(f"   📱 获取聊天记录...")
                chatlog_data, error = fetch_chatlog(ip_address, contact)

                if chatlog_data is not None:
                    # 保存到文件
                    output_file = save_chatlog_to_file(chatlog_data, contact, username, target_id)
                    if output_file:
                        print(f"   ✅ 成功保存 {len(chatlog_data)} 条聊天记录")
                        success_count += 1
                    else:
                        failed_ids.append(target_id)
                else:
                    print(f"   ❌ 获取聊天记录失败: {error}")
                    failed_ids.append(target_id)

                found = True
                break

        if not found:
            print(f"   ❌ 未找到ID为 {target_id} 的联系人")
            failed_ids.append(target_id)

        # 添加延迟避免请求过于频繁
        if i < len(id_list):
            time.sleep(1)

    # 输出总结
    print("\n" + "=" * 60)
    print(f"🎉 批量保存完成!")
    print(f"   ✅ 成功: {success_count}/{len(id_list)}")
    print(f"   ❌ 失败: {len(failed_ids)}")

    if failed_ids:
        print(f"   失败的ID: {', '.join(failed_ids)}")

    print(f"   📁 文件保存在: batch_chatlogs/ 目录下")

    return success_count

def main():
    """主函数"""
    print("📱 简化版根据ID获取聊天记录工具")
    print("=" * 60)

    if len(sys.argv) > 1:
        # 命令行模式
        if sys.argv[1] == "batch":
            # 批量模式
            if len(sys.argv) > 2:
                # 从命令行参数获取ID列表
                id_list = [id.strip() for id in sys.argv[2].split(',') if id.strip()]
                if id_list:
                    batch_save_by_id_list(id_list)
                else:
                    print("❌ 没有有效的ID")
            else:
                print("用法: python simple_get_chatlog_by_id.py batch <ID1,ID2,ID3>")
                print("示例: python simple_get_chatlog_by_id.py batch 3236,16669,46724")
        else:
            # 单个ID模式
            target_id = sys.argv[1].strip()
            if target_id.isdigit():
                get_chatlog_by_id(target_id)
            else:
                print("❌ ID必须是数字")
    else:
        # 交互式模式
        print("选择模式:")
        print("1. 单个ID查询")
        print("2. 批量ID保存")

        choice = input("请选择 (1-2): ").strip()

        if choice == "1":
            target_id = input("请输入ID: ").strip()
            if not target_id:
                print("❌ ID不能为空")
                return
            if not target_id.isdigit():
                print("❌ ID必须是数字")
                return
            get_chatlog_by_id(target_id)

        elif choice == "2":
            ids_input = input("请输入多个ID(用逗号分隔): ").strip()
            if not ids_input:
                print("❌ ID列表不能为空")
                return

            id_list = [id.strip() for id in ids_input.split(',') if id.strip()]
            if not id_list:
                print("❌ 没有有效的ID")
                return

            # 验证所有ID都是数字
            invalid_ids = [id for id in id_list if not id.isdigit()]
            if invalid_ids:
                print(f"❌ 以下ID不是数字: {', '.join(invalid_ids)}")
                return

            batch_save_by_id_list(id_list)
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
