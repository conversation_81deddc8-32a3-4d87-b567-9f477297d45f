<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>微信联系人接收服务</title>
    <style>
        body { font-family: sans-serif; margin: 40px; }
        button { padding: 10px 20px; font-size: 16px; margin: 5px; }
        #result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .loading { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .detail-item { margin: 5px 0; }
        .detail-label { font-weight: bold; }
    </style>
</head>
<body>
    <h2>📥 微信联系人管理系统</h2>
    <p>点击下方按钮后，客户端浏览器将向本机发送联系人上传请求。</p>

    <div style="margin-bottom: 20px;">
        <button onclick="uploadContacts()">获取联系人</button>
        <button onclick="window.open('/chatlog', '_blank')" style="background-color: #07c160; color: white;">💬 查看聊天记录</button>
        <button onclick="window.open('/send_message', '_blank')" style="background-color: #ff6b35; color: white;">📤 发送微信消息</button>
        <button onclick="window.open('/candidate_tags', '_blank')" style="background-color: #9c27b0; color: white;">🏷️ 候选人标签</button>
    </div>

    <div id="result"></div>

    <script>
        async function uploadContacts() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = "loading";
            resultDiv.innerHTML = "正在获取微信联系人信息...";

            try {
                const res = await fetch('/fetch_and_store');
                const data = await res.json();

                if (data.status === 'success') {
                    resultDiv.className = "success";
                    resultDiv.innerHTML = `
                        <h3>✅ 联系人获取成功！</h3>
                        <div class="detail-item">
                            <span class="detail-label">微信用户：</span>${data.user_nickname}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">客户端IP：</span>${data.client_ip}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">文件标识：</span>${data.client_id}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">联系人数量：</span>${data.count}
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">保存状态：</span>${data.upload_result?.status || '未知'}
                        </div>
                    `;
                } else {
                    resultDiv.className = "error";
                    resultDiv.innerHTML = `
                        <h3>❌ 获取失败</h3>
                        <div class="detail-item">错误信息：${data.message}</div>
                    `;
                }
            } catch (err) {
                resultDiv.className = "error";
                resultDiv.innerHTML = `
                    <h3>❌ 请求失败</h3>
                    <div class="detail-item">错误信息：${err.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
