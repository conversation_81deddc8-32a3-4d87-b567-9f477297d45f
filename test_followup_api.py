#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_follow_up_messages_api():
    """测试跟进消息生成API"""
    
    # 测试数据
    test_data = {
        "analysis_result": """
        # 候选人聊天记录分析报告
        
        ## 📊 分析内容概览
        - **聊天文本分析**：✅ 已分析（25条文本消息）
        - **图片内容分析**：❌ 无图片内容
        - **文档内容分析**：✅ 已分析（1个文档，已提取文本内容）
        - **语音内容分析**：❌ 无语音内容
        - **分析对象**：张博士_英国_AI专家
        - **消息总数**：25条
        
        ## 📋 详细分析内容
        
        ### 1. 候选人心理状态分析
        通过分析与候选人张博士的25条聊天记录，发现以下职业心理特征：
        - **求职心态**: 候选人表现出积极但谨慎的求职态度，对新机会有兴趣但不急躁
        - **职业焦虑**: 对当前工作环境有一定不满，希望寻求更好的发展机会
        - **沟通风格**: 表达清晰，回复及时，展现出良好的职业素养
        
        ### 2. 求职意愿解读
        - **跳槽动机**: 主要出于职业发展考虑，希望获得更大的平台和挑战
        - **薪资期望**: 期望薪资水平合理，更注重长期发展前景
        - **时间安排**: 具有一定的紧迫性，但愿意等待合适的机会
        
        ### 3. 专业能力评估
        - **技术水平**: 具备扎实的AI和机器学习专业基础，有丰富的项目经验
        - **沟通能力**: 表达清晰，逻辑性强，适合团队协作
        - **学习态度**: 对新技术和新领域保持开放态度
        
        ### 4. 合作建议
        - **沟通策略**: 建议采用专业但友好的沟通方式
        - **职位匹配**: 适合高级AI算法工程师或技术管理岗位
        - **跟进方式**: 保持定期联系，及时反馈职位信息
        
        ### 5. 风险评估
        - **稳定性**: 中等风险，建议了解其职业规划
        - **期望匹配**: 需要确认薪资和职位期望的匹配度
        - **竞争情况**: 可能同时在接触其他机会，需要及时跟进
        """,
        "candidate_tags": {
            "comprehensive": ["博士", "AI专家", "英国", "5年经验", "求职积极", "沟通良好"],
            "judgment": ["活跃", "回复及时"],
            "manual": ["博士学历", "AI专业", "5年经验"],
            "ai": ["Python", "机器学习", "深度学习", "全职", "30-50万"],
            "remark": ["英国", "博士", "AI专家"]
        },
        "candidate_name": "张博士_英国_AI专家"
    }
    
    # 发送请求
    url = "http://127.0.0.1:8080/api/generate_follow_up_messages"
    
    try:
        print("🚀 开始测试跟进消息生成API...")
        print(f"📡 请求URL: {url}")
        print(f"📊 测试数据: 候选人={test_data['candidate_name']}")
        
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📈 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"📋 响应数据:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 检查结果结构
            if result.get('status') == 'success':
                data = result.get('data', {})
                qualification = data.get('qualification', '未知')
                professional_tags = data.get('professional_tags', [])
                follow_up_messages = data.get('follow_up_messages', {})
                
                print(f"\n📊 结果分析:")
                print(f"   候选人资格: {qualification}")
                print(f"   专业标签数量: {len(professional_tags)}")
                print(f"   跟进消息数量: {len(follow_up_messages)}")
                
                if professional_tags:
                    print(f"   专业标签: {', '.join(professional_tags)}")
                
                if follow_up_messages:
                    print(f"   跟进消息时间点: {', '.join(follow_up_messages.keys())}")
                
                return True
            else:
                print(f"❌ API返回错误: {result.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    success = test_follow_up_messages_api()
    if success:
        print("\n🎉 测试通过!")
    else:
        print("\n💥 测试失败!")
