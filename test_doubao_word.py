#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API Word文档分析测试脚本
测试本地Word文档上传和分析功能
"""

import json
import os
from pathlib import Path

# 尝试导入豆包SDK
try:
    from volcenginesdkarkruntime import Ark
    DOUBAO_AVAILABLE = True
except ImportError:
    print("❌ 豆包SDK未安装，请运行: pip install volcengine-python-sdk[ark]")
    DOUBAO_AVAILABLE = False

# 尝试导入Word文档处理库
try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    print("❌ python-docx未安装，请运行: pip install python-docx")
    DOCX_AVAILABLE = False

# 豆包API配置
DOUBAO_API_KEY = "cdd675a2-53d4-4aa1-bba8-33340ada088f"
DOUBAO_MODEL = "doubao-seed-1.6-250615"

def extract_word_content(file_path):
    """提取Word文档的文本内容"""
    if not DOCX_AVAILABLE:
        print("❌ python-docx库不可用")
        return None
    
    try:
        print(f"📄 正在提取Word文档内容: {file_path}")
        doc = Document(file_path)
        content = []
        
        # 提取段落内容
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                content.append(paragraph.text.strip())
        
        # 提取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    content.append(" | ".join(row_text))
        
        full_content = "\n".join(content)
        print(f"✅ Word文档内容提取成功，共 {len(full_content)} 字符")
        return full_content
        
    except Exception as e:
        print(f"❌ Word文档内容提取失败: {e}")
        return None

def init_doubao_client():
    """初始化豆包AI客户端"""
    if not DOUBAO_AVAILABLE:
        return None
    
    try:
        client = Ark(
            api_key=DOUBAO_API_KEY,
            timeout=1800,  # 30分钟超时
        )
        return client
    except Exception as e:
        print(f"❌ 豆包客户端初始化失败: {e}")
        return None

def analyze_word_with_doubao(word_content, filename="文档"):
    """使用豆包API分析Word文档内容"""
    
    if not DOUBAO_AVAILABLE:
        print("❌ 豆包SDK不可用")
        return None
    
    # 初始化客户端
    client = init_doubao_client()
    if not client:
        return None
    
    try:
        print("🚀 正在调用豆包API分析Word文档...")
        print(f"📝 模型: {DOUBAO_MODEL}")
        print(f"📄 文档名称: {filename}")
        print(f"📏 文档内容长度: {len(word_content)} 字符")
        
        # 构建分析prompt - 与app.py中成功案例保持一致
        prompt = f"""
你是一个专业的猎头心理分析师，请深入分析以下候选人的简历文档内容。分析角度：你是猎头，这是一个人才候选人的简历。请重点关注候选人的职业素养、能力特质、求职潜力和市场价值。

## 简历文档信息
- 文档名称：{filename}
- 文档类型：简历/CV
- 内容长度：{len(word_content)} 字符

## 简历内容
```
{word_content}
```

## 请重点从以下猎头-人才评估维度进行深度分析：

### 1. 候选人心理特质分析
- **职业心态**: 从简历呈现方式分析候选人的职业态度和自我认知
- **成就动机**: 分析候选人对成就和认可的追求程度
- **稳定性倾向**: 从工作经历判断候选人的职业稳定性和忠诚度
- **成长意愿**: 评估候选人的学习能力和自我提升意识

### 2. 求职意愿和动机解读
- **跳槽动机**: 分析候选人可能的离职原因和求职动机
- **职位期望**: 理解候选人对新职位的期望和要求
- **薪资敏感度**: 基于背景评估候选人对薪资的期望和底线
- **发展诉求**: 判断候选人最看重的职业发展要素

### 3. 职业规划和发展潜力
- **短期目标**: 推测候选人近期的职业发展计划
- **长期愿景**: 分析候选人的职业发展长远目标
- **技能提升**: 识别候选人希望获得的能力提升
- **行业适应性**: 判断候选人对不同行业的适应能力

### 4. 沟通配合度预测
- **专业表达**: 从简历质量评估候选人的沟通表达能力
- **信息透明度**: 分析候选人信息分享的开放程度
- **合作态度**: 预测候选人与猎头合作的配合程度
- **响应特征**: 基于简历风格预测候选人的沟通响应特点

### 5. 市场竞争力评估
- **核心优势**: 识别候选人最突出的竞争优势
- **稀缺程度**: 评估候选人技能组合的市场稀缺性
- **替代风险**: 分析候选人被其他候选人替代的可能性
- **议价能力**: 评估候选人在求职中的议价空间

### 6. 猎头合作策略建议
- **沟通策略**: 建议与该候选人最有效的沟通方式
- **职位匹配**: 分析什么类型的职位最适合该候选人
- **说服要点**: 指出在推荐职位时应该重点强调的方面
- **关系维护**: 建议如何长期维护与该候选人的关系

### 7. 风险评估和注意事项
- **跳槽风险**: 评估候选人再次跳槽的可能性
- **期望管理**: 识别可能导致候选人不满的期望差异
- **沟通障碍**: 分析可能影响合作的沟通问题
- **竞争风险**: 判断候选人是否可能同时与其他猎头合作

### 8. 简历专项深度分析
请提供专门的简历总结：
- **基本信息总结**: 候选人的基本背景信息（年龄、学历、工作年限等）
- **工作经历亮点**: 总结候选人的主要工作经历和职业发展轨迹
- **核心技能评估**: 列出候选人的核心专业技能和能力
- **教育背景分析**: 分析候选人的教育背景和专业匹配度
- **职业发展趋势**: 从简历看出的职业发展方向和潜力
- **薪资水平预估**: 基于工作经历和技能水平预估薪资范围
- **适合职位类型**: 推荐最适合候选人的职位类型和级别
- **简历质量评价**: 评估简历的专业程度和完整性
- **潜在风险点**: 识别简历中可能的风险因素（如频繁跳槽、技能不匹配等）
- **面试建议**: 针对该候选人的面试重点和注意事项

请用中文回答，分析要深入细致，重点关注从猎头角度评估候选人的职业素养和个人特质。分析要客观、专业、深入，特别关注候选人的心理特征和求职动机。
"""
        
        # 调用API
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            max_tokens=4000,
            temperature=0.7
        )
        
        print("✅ API调用成功!")
        return response
        
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 豆包API Word文档分析测试")
    print("=" * 60)
    
    # Word文档路径
    word_path = r"F:\Practicum\server_receiver_final\downloaded_files\LiuYun_CV-CN_1.docx"
    
    # 检查文档是否存在
    if not os.path.exists(word_path):
        print(f"❌ Word文档不存在: {word_path}")
        return
    
    print(f"📁 文档路径: {word_path}")
    print(f"📏 文件大小: {os.path.getsize(word_path)} 字节")
    
    # 提取Word文档内容
    word_content = extract_word_content(word_path)
    if not word_content:
        return
    
    # 分析文档
    filename = os.path.basename(word_path)
    result = analyze_word_with_doubao(word_content, filename)
    
    if result:
        print("\n" + "=" * 60)
        print("📋 分析结果:")
        print("=" * 60)
        
        # 提取分析内容
        if hasattr(result, 'choices') and len(result.choices) > 0:
            analysis = result.choices[0].message.content
            print(analysis)
            
            # 保存结果到文件
            output_file = "doubao_word_analysis_result.json"
            result_dict = {
                "model": DOUBAO_MODEL,
                "document_name": filename,
                "document_content_length": len(word_content),
                "analysis": analysis,
                "usage": {
                    "prompt_tokens": result.usage.prompt_tokens if hasattr(result, 'usage') and hasattr(result.usage, 'prompt_tokens') else None,
                    "completion_tokens": result.usage.completion_tokens if hasattr(result, 'usage') and hasattr(result.usage, 'completion_tokens') else None,
                    "total_tokens": result.usage.total_tokens if hasattr(result, 'usage') and hasattr(result.usage, 'total_tokens') else None
                } if hasattr(result, 'usage') else None,
                "created": result.created if hasattr(result, 'created') else None
            }
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, ensure_ascii=False, indent=2)
            print(f"\n💾 完整结果已保存到: {output_file}")
            
            # 同时保存纯文本版本
            text_output_file = "doubao_word_analysis_result.txt"
            with open(text_output_file, 'w', encoding='utf-8') as f:
                f.write(f"文档名称: {filename}\n")
                f.write(f"分析模型: {DOUBAO_MODEL}\n")
                f.write(f"文档内容长度: {len(word_content)} 字符\n")
                f.write("=" * 60 + "\n")
                f.write("分析结果:\n")
                f.write("=" * 60 + "\n")
                f.write(analysis)
            print(f"📄 纯文本结果已保存到: {text_output_file}")
            
        else:
            print("❌ 响应格式异常")
            print(str(result))
    else:
        print("❌ 分析失败")

if __name__ == "__main__":
    main()
