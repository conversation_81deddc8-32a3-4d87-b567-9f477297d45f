"""
测试过滤逻辑
"""

def is_personal_contact(contact):
    """判断是否为个人联系人（排除群聊、公众号等）"""
    username = contact.get("UserName", "")
    nickname = contact.get("NickName", "")
    
    # 排除系统账号
    system_accounts = {
        "filehelper", "weixin", "fmessage", "floatbottle", "medianote", 
        "notifymessage", "tmessage", "qmessage", "qqmail", "wxitil",
        "brandcontact", "helper_entry", "pc_share", "newsapp"
    }
    
    if username in system_accounts:
        return False, f"系统账号: {username}"
    
    # 排除明显的企业/服务账号昵称模式
    if nickname:
        # 包含公司、企业等明显标识
        if any(pattern in nickname for pattern in [
            "有限公司", "股份有限", "集团", "企业", "公司-", "工作室", "工作号",
            "行政人事", "管理部", "客服", "服务", "售后", "登报服务",
            "生产商", "制造", "厂家", "供应商", "AIST", "智能"
        ]):
            return False, f"企业标识: {nickname}"
        
        # 包含特殊符号的可能是装饰性昵称，但某些模式明显是企业
        if any(pattern in nickname for pattern in ["【", "】", "®", "™", "©"]):
            # 但是如果只是装饰性的个人昵称，可能需要更细致的判断
            # 这里先保守一些，只过滤明显的企业标识
            if any(corp_word in nickname for corp_word in ["公司", "企业", "管理", "服务"]):
                return False, f"企业特殊符号: {nickname}"
        
    # 排除群聊（通常以@chatroom结尾）
    if username.endswith("@chatroom"):
        return False, f"群聊: @chatroom"
        
    # 排除公众号（通常以gh_开头）
    if username.startswith("gh_"):
        return False, f"公众号: gh_"
        
    # 排除企业微信群聊（通常包含@openim）
    if "@openim" in username:
        return False, f"企业微信: @openim"
    
    # 排除长数字ID（可能是系统生成的）
    if username.isdigit() and len(username) > 8:
        return False, f"长数字ID: {username}"
    
    # 排除包含特殊企业标识的UserName
    if any(pattern in username for pattern in ["@stranger", "v3_020b"]):
        return False, f"特殊标识: {username}"
        
    # 排除一些明显的非个人联系人关键词
    non_personal_keywords = [
        # 基础过滤
        "公众号", "订阅号", "服务号", "小程序", "群聊", "微信群",
        "客服", "官方", "通知", "助手", "机器人", "bot",
        # 企业相关
        "公司", "企业", "工作号", "工作室", "有限公司", "股份", "集团",
        "Corp", "Company", "Ltd", "Inc", "Co.", "Enterprise",
        # 服务相关
        "服务", "支持", "帮助", "售后", "客服", "service", "support",
        # 管理相关
        "管理", "行政", "人事", "admin", "管理部", "行政部",
        # 系统相关
        "系统", "平台", "中心", "智能", "自动", "AI",
        # 其他
        "生产商", "制造", "厂家", "供应商"
    ]
    
    if nickname:  # 确保nickname不为None
        nickname_lower = nickname.lower()
        for keyword in non_personal_keywords:
            if keyword in nickname or keyword in nickname_lower:
                return False, f"关键词过滤: {keyword} in {nickname}"
    
    # 检查别名中的企业标识
    alias = contact.get("Alias", "")
    if alias:
        alias_lower = alias.lower()
        enterprise_alias_keywords = [
            "corp", "company", "ltd", "inc", "admin", "service", "support",
            "工作", "公司", "企业", "管理", "客服", "服务"
        ]
        for keyword in enterprise_alias_keywords:
            if keyword in alias_lower:
                return False, f"别名过滤: {keyword} in {alias}"
                
    return True, "通过过滤"

def test_suspicious_contacts():
    """测试可疑联系人"""
    test_contacts = [
        {"UserName": "notifymessage", "NickName": "服务通知", "Alias": ""},
        {"UserName": "wxid_h8phj59plp6222", "NickName": "浙江千植林有限公司", "Alias": ""},
        {"UserName": "wxid_cqxtohaiz0db11", "NickName": "Qiwei工作室", "Alias": "qiweishms"},
        {"UserName": "wxid_f6btcxz991gj22", "NickName": "高才-行政人事-工作号", "Alias": "TopTalentsPA199"},
        {"UserName": "wxid_esg80y5lqzxa21", "NickName": "智能水电表生产商", "Alias": ""},
        {"UserName": "wxid_ar39pl2444jn22", "NickName": "售后服务", "Alias": ""},
        {"UserName": "wxid_wsnscegpods422", "NickName": "马老师@登报服务", "Alias": "baoshe29"},
        {"UserName": "wxid_s3sga7q5hmxe22", "NickName": "久大 -管理部", "Alias": ""},
        {"UserName": "v3_020b3826fd03010000000000e80822b7eba0ca000000501ea9a3dba12f95f6b60a0536a1adb69565919beb39da6895c30c3b8399f74fe7dc0c3974051a1b3521fe8ec561680b4c0b9695ba79f2b184a8d52b4c2f0072a675cdaa5e869fb08a0bc73169@stranger", "NickName": "陈俊逸 AIST", "Alias": ""},
        {"UserName": "wxid_izva2ct55f5n12", "NickName": "程洁涔Jennifer💦【闭关回复慢】", "Alias": "cjjennifer11"},
        {"UserName": "wxid_xsxuwznodv522", "NickName": "梦醒®皆空", "Alias": "zou_wu_long"},
    ]
    
    print("🧪 测试过滤逻辑...")
    print("=" * 60)
    
    for i, contact in enumerate(test_contacts, 1):
        is_personal, reason = is_personal_contact(contact)
        status = "✅ 个人" if is_personal else "❌ 过滤"
        
        print(f"{i:2d}. {status} - {contact['NickName']}")
        print(f"    UserName: {contact['UserName']}")
        print(f"    原因: {reason}")
        print()

if __name__ == "__main__":
    test_suspicious_contacts()
