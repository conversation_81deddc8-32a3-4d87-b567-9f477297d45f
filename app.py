from flask import Flask, request, jsonify, render_template, Response, send_file
from receiver import save_contacts
import requests
import csv
import io
import json
import re
import urllib.parse
from pathlib import Path
from datetime import datetime, time
import os
import hashlib
import mimetypes
from urllib.parse import urlparse

# 豆包AI SDK
try:
    from volcenginesdkarkruntime import Ark
    DOUBAO_AVAILABLE = True
except ImportError:
    DOUBAO_AVAILABLE = False
    print("⚠️ 豆包SDK未安装，请运行: pip install -U 'volcengine-python-sdk[ark]'")

# 文件下载存储配置
DOWNLOAD_FOLDER = "downloaded_files"
if not os.path.exists(DOWNLOAD_FOLDER):
    os.makedirs(DOWNLOAD_FOLDER)

app = Flask(__name__)

# 豆包AI配置
DOUBAO_API_KEY = "cdd675a2-53d4-4aa1-bba8-33340ada088f"
DOUBAO_MODEL = "doubao-seed-1.6-250615"

def init_doubao_client():
    """初始化豆包AI客户端"""
    if not DOUBAO_AVAILABLE:
        return None

    try:
        client = Ark(
            api_key=DOUBAO_API_KEY,
            timeout=1800,  # 30分钟超时
        )
        return client
    except Exception as e:
        print(f"❌ 豆包客户端初始化失败: {e}")
        return None

def encode_image_to_base64(image_path):
    """将图片文件编码为base64字符串"""
    try:
        import base64
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            print(f"📸 图片编码成功: {image_path} -> {len(base64_string)} 字符")
            return base64_string
    except Exception as e:
        print(f"❌ 图片编码失败: {image_path} - {e}")
        return None

def extract_word_content(file_path):
    """提取Word文档的文本内容"""
    try:
        # 尝试使用python-docx库
        try:
            from docx import Document
            print(f"📄 正在提取Word文档内容: {file_path}")
            doc = Document(file_path)
            content = []

            # 提取段落内容
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text.strip())

            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content.append(" | ".join(row_text))

            full_content = '\n'.join(content)
            print(f"✅ Word文档内容提取成功，共 {len(full_content)} 字符")
            return full_content
        except ImportError:
            print("⚠️ python-docx库未安装，无法提取Word文档内容")
            return None
        except Exception as e:
            print(f"⚠️ Word文档解析失败: {e}")
            return None
    except Exception as e:
        print(f"❌ Word文档内容提取失败: {e}")
        return None

def extract_text_file_content(file_path):
    """提取文本文件内容"""
    try:
        print(f"📄 正在提取文本文件内容: {file_path}")
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ 文本文件内容提取成功，共 {len(content)} 字符")
        return content
    except UnicodeDecodeError:
        try:
            # 尝试其他编码
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
            print(f"✅ 文本文件内容提取成功（GBK编码），共 {len(content)} 字符")
            return content
        except Exception as e:
            print(f"❌ 文本文件内容提取失败（编码问题）: {e}")
            return None
    except Exception as e:
        print(f"❌ 文本文件内容提取失败: {e}")
        return None

def extract_pdf_content(file_path):
    """提取PDF文档内容"""
    try:
        # 尝试使用PyPDF2库
        try:
            import PyPDF2
            print(f"📄 正在提取PDF文档内容: {file_path}")

            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                content = []

                print(f"📊 PDF页数: {len(reader.pages)}")

                for i, page in enumerate(reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            content.append(page_text)
                            print(f"📄 第{i+1}页提取成功: {len(page_text)} 字符")
                        else:
                            print(f"⚠️ 第{i+1}页无文本内容（可能是图片页面）")
                    except Exception as page_error:
                        print(f"⚠️ 第{i+1}页提取失败: {page_error}")
                        continue

            if content:
                full_content = '\n\n'.join(content)
                print(f"✅ PDF文档内容提取成功，共 {len(full_content)} 字符，有效页面: {len(content)}")

                # 显示内容预览
                preview = full_content[:200].replace('\n', ' ')
                print(f"📋 内容预览: {preview}...")

                return full_content
            else:
                print("⚠️ PDF文档中没有可提取的文本内容（可能是扫描版PDF）")
                return None

        except ImportError:
            print("⚠️ PyPDF2库未安装，无法提取PDF文档内容")
            return None
        except Exception as e:
            print(f"⚠️ PDF文档解析失败: {e}")
            return None
    except Exception as e:
        print(f"❌ PDF文档内容提取失败: {e}")
        return None

def extract_excel_content(file_path):
    """提取Excel文档内容"""
    try:
        # 尝试使用openpyxl库
        try:
            import openpyxl
            print(f"📄 正在提取Excel文档内容: {file_path}")
            workbook = openpyxl.load_workbook(file_path)
            content = []

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                content.append(f"工作表: {sheet_name}")

                for row in sheet.iter_rows(values_only=True):
                    row_text = []
                    for cell in row:
                        if cell is not None:
                            row_text.append(str(cell))
                    if row_text:
                        content.append(" | ".join(row_text))

            full_content = '\n'.join(content)
            print(f"✅ Excel文档内容提取成功，共 {len(full_content)} 字符")
            return full_content
        except ImportError:
            print("⚠️ openpyxl库未安装，无法提取Excel文档内容")
            return None
        except Exception as e:
            print(f"⚠️ Excel文档解析失败: {e}")
            return None
    except Exception as e:
        print(f"❌ Excel文档内容提取失败: {e}")
        return None

def extract_voice_content(file_path):
    """提取语音文件内容（转换为文字）"""
    try:
        print(f"🎤 开始处理语音文件: {file_path}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"❌ 语音文件不存在: {file_path}")
            return "[语音文件不存在]"

        file_size = os.path.getsize(file_path)
        print(f"📏 语音文件大小: {file_size} 字节")

        # 方案1：尝试使用speech_recognition库
        try:
            import speech_recognition as sr
            print(f"🎤 正在使用speech_recognition转换语音: {file_path}")

            r = sr.Recognizer()

            # 检查文件格式，如果不是wav格式需要转换
            if not file_path.lower().endswith('.wav'):
                # 尝试使用pydub转换音频格式
                try:
                    from pydub import AudioSegment
                    print(f"🔄 转换音频格式到WAV...")

                    # 根据文件扩展名选择加载方式
                    if file_path.lower().endswith('.mp3'):
                        audio = AudioSegment.from_mp3(file_path)
                    elif file_path.lower().endswith('.m4a'):
                        audio = AudioSegment.from_file(file_path, format="m4a")
                    elif file_path.lower().endswith('.ogg'):
                        audio = AudioSegment.from_ogg(file_path)
                    else:
                        audio = AudioSegment.from_file(file_path)

                    # 转换为wav格式
                    wav_path = file_path.rsplit('.', 1)[0] + '_converted.wav'
                    audio.export(wav_path, format="wav")
                    file_path = wav_path
                    print(f"✅ 音频格式转换成功: {wav_path}")

                except ImportError:
                    print("⚠️ pydub库未安装，无法转换音频格式")
                    return f"[语音文件已下载，但无法转换格式进行识别，文件大小: {file_size} 字节]"
                except Exception as e:
                    print(f"⚠️ 音频格式转换失败: {e}")
                    return f"[语音文件已下载，但格式转换失败，文件大小: {file_size} 字节]"

            # 使用speech_recognition进行语音识别
            with sr.AudioFile(file_path) as source:
                audio_data = r.record(source)

            # 尝试多种识别引擎
            text_result = None

            # 1. 尝试使用Google语音识别（需要网络）
            try:
                text_result = r.recognize_google(audio_data, language='zh-CN')
                print(f"✅ Google语音识别成功")
            except sr.UnknownValueError:
                print("⚠️ Google语音识别无法理解音频")
            except sr.RequestError as e:
                print(f"⚠️ Google语音识别服务错误: {e}")
            except Exception as e:
                print(f"⚠️ Google语音识别异常: {e}")

            if text_result:
                print(f"✅ 语音转文字成功，共 {len(text_result)} 字符")
                print(f"📋 语音内容预览: {text_result[:100]}...")
                return text_result
            else:
                print("❌ 语音识别失败，但文件已下载")
                return f"[语音文件已下载但识别失败，文件大小: {file_size} 字节，可手动播放查看内容]"

        except ImportError:
            print("⚠️ speech_recognition库未安装，无法进行语音转文字")
            print("💡 安装提示: pip install SpeechRecognition pydub")
            return f"[语音文件已下载，但缺少语音识别库，文件大小: {file_size} 字节，位置: {file_path}]"
        except Exception as e:
            print(f"⚠️ 语音识别失败: {e}")
            return f"[语音文件已下载但识别失败: {str(e)}，文件大小: {file_size} 字节]"

    except Exception as e:
        print(f"❌ 语音文件处理失败: {e}")
        return f"[语音文件处理失败: {str(e)}]"

def extract_real_filename_from_attachment(original_content, fallback_filename=None):
    """从附件消息中提取真实的文件名"""
    import re

    try:
        # 匹配格式: [文件|真实文件名.扩展名](URL)
        pattern = r'\[文件\|([^\]]+)\]\([^)]+\)'
        match = re.search(pattern, original_content)

        if match:
            real_filename = match.group(1)
            print(f"📝 从附件消息中提取到真实文件名: {real_filename}")
            return real_filename

        # 匹配格式: [图片|真实图片名.扩展名](URL)
        pattern_img = r'\[图片\|([^\]]+)\]\([^)]+\)'
        match_img = re.search(pattern_img, original_content)

        if match_img:
            real_filename = match_img.group(1)
            print(f"📝 从图片消息中提取到真实文件名: {real_filename}")
            return real_filename

        # 如果没有匹配到，尝试其他格式
        # 匹配格式: [文件] 文件名.扩展名
        pattern2 = r'\[文件\]\s*([^\s\[\]]+\.[a-zA-Z0-9]+)'
        match2 = re.search(pattern2, original_content)

        if match2:
            real_filename = match2.group(1)
            print(f"📝 从附件消息中提取到文件名: {real_filename}")
            return real_filename

        # 匹配格式: [图片] 图片名.扩展名
        pattern3 = r'\[图片\]\s*([^\s\[\]]+\.[a-zA-Z0-9]+)'
        match3 = re.search(pattern3, original_content)

        if match3:
            real_filename = match3.group(1)
            print(f"📝 从图片消息中提取到文件名: {real_filename}")
            return real_filename

        print(f"⚠️ 无法从消息中提取文件名: {original_content[:100]}...")
        return fallback_filename

    except Exception as e:
        print(f"❌ 提取文件名失败: {e}")
        return fallback_filename

def download_and_store_file(url, filename=None, original_content=None):
    """下载文件并存储到本地，返回本地访问URL"""
    try:
        print(f"📥 开始下载文件: {url}")

        # 发送请求下载文件，允许重定向
        response = requests.get(url, timeout=30, stream=True, allow_redirects=True)
        response.raise_for_status()

        # 检查是否发生了重定向
        if response.url != url:
            print(f"🔄 检测到重定向: {url} -> {response.url}")
            actual_url = response.url
        else:
            actual_url = url

        # 优先从附件消息中提取真实文件名
        if original_content:
            real_filename = extract_real_filename_from_attachment(original_content, filename)
            if real_filename:
                filename = real_filename

        # 获取文件名和处理图片格式
        if not filename:
            # 从URL中提取文件名
            parsed_url = urlparse(actual_url)
            filename = os.path.basename(parsed_url.path)

            # 如果没有文件名，使用URL的hash作为文件名
            if not filename or '.' not in filename:
                url_hash = hashlib.md5(url.encode()).hexdigest()[:8]

                # 尝试从Content-Type获取扩展名
                content_type = response.headers.get('content-type', '')
                print(f"📋 Content-Type: {content_type}")

                # 特殊处理图片类型
                if content_type.startswith('image/'):
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        extension = '.jpg'
                    elif 'png' in content_type:
                        extension = '.png'
                    elif 'gif' in content_type:
                        extension = '.gif'
                    elif 'webp' in content_type:
                        extension = '.webp'
                    else:
                        extension = '.png'  # 默认为PNG
                    print(f"🖼️ 检测到图片类型，使用扩展名: {extension}")
                    filename = f"image_{url_hash}{extension}"  # 图片使用image_前缀
                else:
                    extension = mimetypes.guess_extension(content_type.split(';')[0])
                    if not extension:
                        extension = '.bin'  # 默认扩展名
                    filename = f"file_{url_hash}{extension}"

            # 如果文件名是.dat但Content-Type是图片，则转换扩展名
            elif filename.endswith('.dat'):
                content_type = response.headers.get('content-type', '')
                if content_type.startswith('image/'):
                    # 使用URL hash作为基础名称，而不是.dat文件名
                    url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        filename = f"image_{url_hash}.jpg"
                    elif 'png' in content_type:
                        filename = f"image_{url_hash}.png"
                    elif 'gif' in content_type:
                        filename = f"image_{url_hash}.gif"
                    else:
                        filename = f"image_{url_hash}.png"  # 默认为PNG
                    print(f"🔄 .dat文件转换为图片格式: {filename}")

        # 确保文件名安全
        filename = "".join(c for c in filename if c.isalnum() or c in '._-')

        # 生成唯一的本地文件路径
        local_path = os.path.join(DOWNLOAD_FOLDER, filename)

        # 如果文件已存在，添加数字后缀
        counter = 1
        original_path = local_path
        while os.path.exists(local_path):
            name, ext = os.path.splitext(original_path)
            local_path = f"{name}_{counter}{ext}"
            counter += 1

        # 下载并保存文件
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        # 生成本地访问URL
        local_filename = os.path.basename(local_path)
        local_url = f"/downloaded_files/{local_filename}"

        print(f"✅ 文件下载成功: {filename} -> {local_path}")
        print(f"🔗 本地访问URL: {local_url}")

        return {
            'success': True,
            'local_path': local_path,
            'local_url': local_url,
            'filename': local_filename,
            'original_url': url
        }

    except Exception as e:
        print(f"❌ 文件下载失败: {url} - {e}")
        return {
            'success': False,
            'error': str(e),
            'original_url': url
        }

def is_resume_file(filename):
    """判断是否为简历文件"""
    if not filename:
        return False

    filename_lower = filename.lower()
    resume_keywords = [
        '简历', 'resume', 'cv', '履历',
        '个人简历', 'curriculum', 'vitae',
        '求职', 'job', 'application'
    ]

    return any(keyword in filename_lower for keyword in resume_keywords)

def process_document_for_analysis(doc_info):
    """处理文档文件，尝试转换为可分析的格式"""
    import requests
    from urllib.parse import urlparse

    try:
        url = doc_info['url']
        filename = doc_info.get('filename', '')

        # 获取文件扩展名
        if filename:
            file_ext = filename.split('.')[-1].lower() if '.' in filename else ''
        else:
            parsed_url = urlparse(url)
            file_ext = parsed_url.path.split('.')[-1].lower() if '.' in parsed_url.path else ''

        # 判断是否为简历
        is_resume = is_resume_file(filename)

        print(f"📄 处理文档: {filename} (类型: {file_ext}{'，疑似简历' if is_resume else ''})")

        # 尝试下载文件到本地
        original_content = doc_info.get('original_content', '')
        download_result = download_and_store_file(url, filename, original_content)

        if download_result['success']:
            local_path = download_result['local_path']
            real_filename = download_result['filename']  # 使用下载后的真实文件名
            print(f"📁 文件已下载到本地: {local_path}")
            print(f"📏 文件大小: {os.path.getsize(local_path)} 字节")
            print(f"📝 真实文件名: {real_filename}")

            # 重新判断文件类型 - 使用下载后的真实文件名
            if real_filename and '.' in real_filename:
                real_file_ext = real_filename.split('.')[-1].lower()
                print(f"🔍 重新识别文件类型: {real_file_ext} (基于真实文件名: {real_filename})")
            else:
                real_file_ext = file_ext  # 如果无法从真实文件名获取，使用原来的
                print(f"⚠️ 无法从真实文件名获取扩展名，使用原始判断: {real_file_ext}")

            # 重新判断是否为简历 - 使用真实文件名
            is_resume_real = is_resume_file(real_filename)
            if is_resume_real != is_resume:
                print(f"📋 简历判断更新: {is_resume} -> {is_resume_real} (基于真实文件名)")
                is_resume = is_resume_real

            # 尝试提取文档内容 - 完全本地化处理
            extracted_content = None
            extraction_method = ""

            # PDF文件内容提取
            if real_file_ext == 'pdf':
                print(f"🔍 开始提取PDF文档内容...")
                extracted_content = extract_pdf_content(local_path)
                extraction_method = "PDF文本提取"

            # Word文档内容提取
            elif real_file_ext in ['doc', 'docx']:
                print(f"🔍 开始提取Word文档内容...")
                extracted_content = extract_word_content(local_path)
                extraction_method = "Word文档解析"

            # Excel文档内容提取
            elif real_file_ext in ['xls', 'xlsx']:
                print(f"🔍 开始提取Excel文档内容...")
                extracted_content = extract_excel_content(local_path)
                extraction_method = "Excel表格解析"

            # 文本文件内容提取
            elif real_file_ext in ['txt', 'md', 'csv']:
                print(f"🔍 开始提取文本文件内容...")
                extracted_content = extract_text_file_content(local_path)
                extraction_method = "文本文件读取"

            # 语音文件内容提取（语音转文字）
            elif real_file_ext in ['mp3', 'wav', 'm4a', 'aac', 'ogg']:
                print(f"🔍 开始提取语音文件内容（语音转文字）...")
                extracted_content = extract_voice_content(local_path)
                extraction_method = "语音转文字"

            else:
                print(f"⚠️ 不支持的文件类型: {real_file_ext} (真实文件名: {real_filename})")

            # 如果成功提取了内容，返回文本内容
            if extracted_content and extracted_content.strip():
                # 限制内容长度，避免token过多
                max_length = 8000 if is_resume else 5000  # 增加简历的长度限制
                original_length = len(extracted_content)
                content = extracted_content[:max_length]

                if original_length > max_length:
                    content += f"\n\n[内容已截断，原文档共{original_length}字符，已截取前{max_length}字符]"
                    print(f"📝 内容已截断: {original_length} -> {max_length} 字符")

                print(f"✅ 文档内容提取成功: {real_filename} ({extraction_method})")
                print(f"📊 最终内容长度: {len(content)} 字符")

                return {
                    'type': 'text_content',
                    'content': content,
                    'filename': real_filename,  # 使用真实文件名
                    'description': f"文档内容: {real_filename} (通过{extraction_method})",
                    'is_resume': is_resume,
                    'local_path': local_path,
                    'original_url': url,
                    'extraction_method': extraction_method,
                    'original_length': original_length,
                    'original_filename': filename  # 保留原始文件名用于调试
                }

            # 如果无法提取内容，返回文档信息（不使用URL）
            else:
                print(f"❌ 无法提取文档内容: {real_filename} (文件类型: {real_file_ext})")
                return {
                    'type': 'document_info',
                    'filename': real_filename,  # 使用真实文件名
                    'description': f"文档文件: {real_filename} (已下载到本地，但无法提取文本内容，可能是扫描版或加密文档)",
                    'is_resume': is_resume,
                    'local_path': local_path,
                    'original_url': url,
                    'file_type': real_file_ext,  # 使用真实文件扩展名
                    'original_filename': filename  # 保留原始文件名用于调试
                }
        else:
            # 下载失败，返回错误信息（不使用URL）
            print(f"⚠️ 文件下载失败，无法进行本地分析: {url}")
            print(f"📝 原始文件名: {filename}")
            return {
                'type': 'document_error',
                'filename': filename if filename else "未知文件",
                'description': f"文档文件: {filename if filename else '未知文件'} (下载失败，无法进行分析)",
                'is_resume': is_resume,
                'error': "文件下载失败",
                'original_url': url
            }



    except Exception as e:
        print(f"❌ 文档处理失败: {e}")
        return None

def generate_wechat_message_with_ai(user_input, receiver=""):
    """使用豆包AI生成微信消息"""
    if not DOUBAO_AVAILABLE:
        # 如果豆包不可用，返回基于模板的消息
        return generate_message_template(user_input, receiver)

    client = init_doubao_client()
    if not client:
        return generate_message_template(user_input, receiver)

    try:
        # 构建AI提示词
        receiver_part = f"接收人是「{receiver}」，" if receiver else ""

        prompt = f"""
你是一个专业的猎头顾问助手，需要根据用户的意图生成合适的微信消息。

## 任务要求
- 根据用户描述的意图，生成一条适合微信发送的消息
- {receiver_part}请生成个性化的称呼
- 消息要专业、礼貌、简洁明了
- 符合猎头与候选人沟通的语言风格
- 消息长度适中，不要太长也不要太短
- 使用自然的中文表达，避免过于正式或生硬

## 用户意图
{user_input}

## 生成要求
- 只返回生成的微信消息内容，不要包含其他解释
- 消息要直接可用，无需进一步处理
- 如果涉及时间安排，使用相对时间表述（如"这周"、"下周"等）
- 保持友好但专业的语调

请生成微信消息：
"""

        print(f"🤖 调用豆包AI生成消息")
        print(f"📝 提示词长度: {len(prompt)} 字符")

        # 调用豆包API
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,  # 限制输出长度
            temperature=0.7,  # 适中的创造性
        )

        # 提取生成的消息
        generated_message = response.choices[0].message.content.strip()

        # 清理生成的消息（移除可能的引号或多余的格式）
        generated_message = generated_message.strip('"\'')

        print(f"✅ 豆包AI消息生成成功")
        print(f"📄 生成内容: {generated_message}")

        return generated_message

    except Exception as e:
        print(f"❌ 豆包AI消息生成失败: {e}")
        # 回退到模板生成
        return generate_message_template(user_input, receiver)

def generate_follow_up_messages_with_ai(analysis_result, candidate_tags, candidate_name=""):
    """基于分析报告和标签生成跟进消息"""
    if not DOUBAO_AVAILABLE:
        print("⚠️ 豆包AI不可用，返回模板消息")
        return generate_follow_up_messages_template(analysis_result, candidate_tags, candidate_name)

    client = init_doubao_client()
    if not client:
        return generate_follow_up_messages_template(analysis_result, candidate_tags, candidate_name)

    try:
        # 提取候选人信息
        candidate_name_clean = candidate_name.replace("Olivier", "").strip() if candidate_name else "候选人"

        # 构建分析摘要
        analysis_summary = extract_analysis_summary(analysis_result)
        tags_summary = extract_tags_summary(candidate_tags)

        # 构建AI提示词
        prompt = f"""
你是一个专业的海外人才招聘顾问，专门负责为珠三角/长三角地区招募高层次海外人才。

## 候选人信息
- 姓名：{candidate_name_clean}
- 分析报告摘要：{analysis_summary}
- 候选人标签：{tags_summary}

## 任务要求
请根据候选人的具体经验和技能，生成以下内容：

### 1. 候选人资格评估
首先判断该候选人是否为"合格"候选人。合格标准：
- 具有海外背景或高学历
- 有相关专业技能和工作经验
- 表现出一定的求职意愿
- 沟通配合度良好

请输出：候选人资格：合格 或 不合格

### 2. 专业领域标签（仅对合格候选人）
如果候选人合格，请生成5个专业领域的中文标签，格式：
专业领域标签：[中文标签1], [中文标签2], [中文标签3], [中文标签4], [中文标签5]

### 3. 跟进消息生成（仅对合格候选人）
如果候选人合格，请生成7条不同时间点的跟进消息。每条消息应该：
- 根据候选人的具体背景和技能定制
- 强调远程兼职合作，不影响目前工作
- 鼓励讨论珠三角/长三角地区机会
- 引导添加招聘顾问微信
- 将"Olivier"替换为候选人真实姓名（如果有），否则使用通用问候语
- 将"招聘顾问"保持不变

基础消息模板参考：
"Hi {candidate_name_clean},

2025/2026 珠三角/长三角企业招募高层次海外人才（全职、项目合作）

我是Top Talents的招聘顾问，有幸在领英上看到您的资料，您的背景和经验非常吸引我，我们正在招募高层次人才/工程师技术/管理人才。
我们中心主要负责为珠三角/长三角政府引进海外高端人才，同时也为各大企事业单位、科研院所寻找相关人才。我们的政策很灵活，您可以选择全职、项目合作等方式，3-5年有回国的打算均可。

我们也会结合您具体的偏好（如研究领域和地域选择等）来匹配推荐给您最合适的工作岗位。

如果您有意向了解珠三角/长三角地区更多的平台岗位及人才项目政策，期待您与我联系，也欢迎提供您最新的详尽简历至我的邮箱，方便我帮您匹配！

您也可以添加附件我们负责这个项目的同事微信二维码咨询更多政策细节，我们也会不定期举办一些线上线下活动邀请过往入选的人才分享相关经验；通过我们申请还有最高1W的回国机票补助，您看方便的话可以加我们负责补助的同事沟通下？"

请严格按照以下格式输出7条消息：

Follow-up Message (1 hour): [1小时后的中文消息内容]
Follow-up Message (1 week): [1周后的中文消息内容]
Follow-up Message (3 weeks): [3周后的中文消息内容]
Follow-up Message (1 month): [1个月后的中文消息内容]
Follow-up Message (2 months): [2个月后的中文消息内容]
Follow-up Message (4 months): [4个月后的中文消息内容]
Follow-up Message (6 months): [6个月后的中文消息内容]

## 注意事项
- 不要在跟进消息中提及具体年龄
- 不要使用"本周"等距今非常近的时间表述
- 每条消息都要根据候选人的专业背景定制
- 如果候选人不合格，不要生成专业标签和跟进消息

请开始分析和生成：
"""

        print(f"🤖 调用豆包AI生成跟进消息")
        print(f"📝 提示词长度: {len(prompt)} 字符")

        # 调用豆包API
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=2000,  # 增加输出长度限制
            temperature=0.7,
        )

        # 提取生成的内容
        generated_content = response.choices[0].message.content.strip()

        print(f"✅ 豆包AI跟进消息生成成功")
        print(f"📄 生成内容长度: {len(generated_content)} 字符")

        # 解析生成的内容
        return parse_follow_up_response(generated_content)

    except Exception as e:
        print(f"❌ 豆包AI跟进消息生成失败: {e}")
        # 回退到模板生成
        return generate_follow_up_messages_template(analysis_result, candidate_tags, candidate_name)

def save_ip_change_record(ip_record):
    """保存IP地址变化记录到文件"""
    try:
        ip_log_file = "ip_changes.json"

        # 读取现有记录
        existing_records = []
        if os.path.exists(ip_log_file):
            try:
                with open(ip_log_file, 'r', encoding='utf-8') as f:
                    existing_records = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                existing_records = []

        # 添加新记录
        existing_records.append(ip_record)

        # 保持最近100条记录
        if len(existing_records) > 100:
            existing_records = existing_records[-100:]

        # 保存到文件
        with open(ip_log_file, 'w', encoding='utf-8') as f:
            json.dump(existing_records, f, ensure_ascii=False, indent=2)

        print(f"📝 IP变化记录已保存到 {ip_log_file}")

    except Exception as e:
        print(f"❌ 保存IP变化记录失败: {e}")

def get_ip_change_history(username=None, limit=50):
    """获取IP地址变化历史记录"""
    try:
        ip_log_file = "ip_changes.json"

        if not os.path.exists(ip_log_file):
            return []

        with open(ip_log_file, 'r', encoding='utf-8') as f:
            records = json.load(f)

        # 如果指定了用户名，过滤记录
        if username:
            records = [r for r in records if r.get('username') == username]

        # 按时间倒序排列，返回最近的记录
        records.sort(key=lambda x: x.get('report_time', ''), reverse=True)

        return records[:limit]

    except Exception as e:
        print(f"❌ 获取IP变化历史失败: {e}")
        return []

def update_user_json_filename(username, new_ip_address):
    """更新用户文件夹下的JSON文件名，将IP地址更新为新的IP"""
    try:
        user_folder_path = os.path.join("data", username)

        if not os.path.exists(user_folder_path):
            print(f"⚠️ 用户文件夹不存在: {user_folder_path}")
            return False

        # 查找现有的JSON文件（以IP地址和用户名命名的文件）
        existing_json_files = []
        for filename in os.listdir(user_folder_path):
            if filename.endswith(f"_{username}.json") and not filename.startswith("new_contacts"):
                existing_json_files.append(filename)

        if not existing_json_files:
            print(f"⚠️ 未找到用户 {username} 的基准JSON文件")
            return False

        # 通常应该只有一个基准文件，取第一个
        old_json_file = existing_json_files[0]
        old_file_path = os.path.join(user_folder_path, old_json_file)

        # 构建新的文件名
        # 将IP地址中的点替换为下划线
        new_ip_formatted = new_ip_address.replace(".", "_")
        new_json_filename = f"{new_ip_formatted}_{username}.json"
        new_file_path = os.path.join(user_folder_path, new_json_filename)

        # 如果新文件名与旧文件名相同，不需要更新
        if old_json_file == new_json_filename:
            print(f"✅ 用户 {username} 的IP地址未变化，无需更新文件名")
            return True

        # 重命名文件
        if os.path.exists(old_file_path):
            # 如果新文件已存在，先删除
            if os.path.exists(new_file_path):
                os.remove(new_file_path)
                print(f"🗑️ 删除已存在的文件: {new_json_filename}")

            # 重命名文件
            os.rename(old_file_path, new_file_path)
            print(f"✅ 成功更新用户 {username} 的JSON文件名:")
            print(f"   旧文件: {old_json_file}")
            print(f"   新文件: {new_json_filename}")

            # 记录IP变化到用户文件夹的日志
            log_ip_change_to_user_folder(username, old_json_file, new_json_filename, new_ip_address)

            return True
        else:
            print(f"❌ 旧文件不存在: {old_file_path}")
            return False

    except Exception as e:
        print(f"❌ 更新用户JSON文件名失败: {e}")
        return False

def log_ip_change_to_user_folder(username, old_filename, new_filename, new_ip):
    """在用户文件夹中记录IP变化日志"""
    try:
        user_folder_path = os.path.join("data", username)
        log_file_path = os.path.join(user_folder_path, "ip_change_log.json")

        # 创建日志记录
        log_entry = {
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "old_filename": old_filename,
            "new_filename": new_filename,
            "new_ip_address": new_ip,
            "report_time": datetime.now().isoformat()
        }

        # 读取现有日志
        existing_logs = []
        if os.path.exists(log_file_path):
            try:
                with open(log_file_path, 'r', encoding='utf-8') as f:
                    existing_logs = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                existing_logs = []

        # 添加新日志
        existing_logs.append(log_entry)

        # 保持最近50条记录
        if len(existing_logs) > 50:
            existing_logs = existing_logs[-50:]

        # 保存日志
        with open(log_file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_logs, f, ensure_ascii=False, indent=2)

        print(f"📝 已记录用户 {username} 的IP变化日志")

    except Exception as e:
        print(f"❌ 记录用户IP变化日志失败: {e}")

def extract_analysis_summary(analysis_result):
    """从分析结果中提取摘要信息"""
    try:
        if isinstance(analysis_result, dict):
            analysis_text = analysis_result.get('analysis', '')
        else:
            analysis_text = str(analysis_result)

        # 提取关键信息
        summary_parts = []

        # 查找心理状态
        if '求职心态' in analysis_text:
            summary_parts.append("有求职意愿")

        # 查找专业背景
        if any(tech in analysis_text.lower() for tech in ['java', 'python', '开发', '工程师', '技术']):
            summary_parts.append("技术背景")

        # 查找学历信息
        if any(edu in analysis_text for edu in ['博士', '硕士', '本科', 'PhD', 'Master']):
            summary_parts.append("高学历")

        # 查找海外背景
        if any(location in analysis_text for location in ['海外', '国外', '美国', '英国', '加拿大', '澳洲']):
            summary_parts.append("海外背景")

        return "，".join(summary_parts) if summary_parts else "候选人基本信息"

    except Exception as e:
        print(f"❌ 提取分析摘要失败: {e}")
        return "候选人基本信息"

def extract_tags_summary(candidate_tags):
    """从候选人标签中提取摘要"""
    try:
        if not candidate_tags:
            return "无标签信息"

        # 如果是字符串，直接返回
        if isinstance(candidate_tags, str):
            return candidate_tags[:100]

        # 如果是字典，提取关键标签
        if isinstance(candidate_tags, dict):
            all_tags = []

            # 提取综合标签
            if 'all_tags' in candidate_tags:
                all_tags.extend(candidate_tags['all_tags'][:5])
            elif 'comprehensive' in candidate_tags:
                all_tags.extend(candidate_tags['comprehensive'][:5])

            # 如果没有综合标签，从各个来源提取
            if not all_tags:
                for source in ['judgment', 'manual', 'ai', 'remark']:
                    if source in candidate_tags and candidate_tags[source]:
                        all_tags.extend(candidate_tags[source][:2])

            return "，".join(all_tags[:8]) if all_tags else "无标签信息"

        return str(candidate_tags)[:100]

    except Exception as e:
        print(f"❌ 提取标签摘要失败: {e}")
        return "无标签信息"

def parse_follow_up_response(generated_content):
    """解析AI生成的跟进消息响应"""
    try:
        result = {
            "status": "success",
            "qualification": "不合格",
            "professional_tags": [],
            "follow_up_messages": {}
        }

        lines = generated_content.split('\n')

        # 解析资格评估
        for line in lines:
            if '候选人资格' in line or '资格评估' in line:
                if '合格' in line and '不合格' not in line:
                    result["qualification"] = "合格"
                break

        # 解析专业领域标签
        for line in lines:
            if '专业领域标签' in line:
                # 提取标签
                import re
                tags_match = re.findall(r'\[([^\]]+)\]', line)
                result["professional_tags"] = tags_match
                break

        # 解析跟进消息
        message_patterns = {
            "1_hour": r"Follow-up Message \(1 hour\):\s*(.+)",
            "1_week": r"Follow-up Message \(1 week\):\s*(.+)",
            "3_weeks": r"Follow-up Message \(3 weeks\):\s*(.+)",
            "1_month": r"Follow-up Message \(1 month\):\s*(.+)",
            "2_months": r"Follow-up Message \(2 months\):\s*(.+)",
            "4_months": r"Follow-up Message \(4 months\):\s*(.+)",
            "6_months": r"Follow-up Message \(6 months\):\s*(.+)"
        }

        import re
        for key, pattern in message_patterns.items():
            match = re.search(pattern, generated_content, re.MULTILINE)
            if match:
                result["follow_up_messages"][key] = match.group(1).strip()

        print(f"✅ 解析跟进消息成功: 资格={result['qualification']}, 标签数={len(result['professional_tags'])}, 消息数={len(result['follow_up_messages'])}")

        return result

    except Exception as e:
        print(f"❌ 解析跟进消息失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "qualification": "不合格",
            "professional_tags": [],
            "follow_up_messages": {}
        }

def generate_follow_up_messages_template(analysis_result, candidate_tags, candidate_name=""):
    """模板方式生成跟进消息（AI不可用时的备选方案）"""
    try:
        candidate_name_clean = candidate_name.replace("Olivier", "").strip() if candidate_name else "您"

        # 简单判断是否合格
        qualification = "合格"  # 默认合格，可以根据需要调整逻辑

        # 生成基础专业标签
        professional_tags = ["技术专家", "海外背景", "高学历", "有经验", "沟通良好"]

        # 生成模板消息
        base_message = f"""Hi {candidate_name_clean},

2025/2026 珠三角/长三角企业招募高层次海外人才（全职、项目合作）

我是Top Talents的招聘顾问，有幸看到您的资料，您的背景和经验非常吸引我。我们正在招募高层次人才，政策很灵活，您可以选择全职、项目合作等方式。

如果您有意向了解更多平台岗位及人才项目政策，期待您与我联系！您也可以添加我们招聘顾问的微信咨询更多详情。"""

        follow_up_messages = {
            "1_hour": f"{candidate_name_clean}好，刚才发送的信息您看到了吗？我们有很多适合您背景的机会，方便简单聊聊吗？",
            "1_week": f"{candidate_name_clean}好，上周联系您关于珠三角/长三角人才项目的事情，不知道您是否有时间了解一下？我们有一些新的政策更新。",
            "3_weeks": f"{candidate_name_clean}好，最近我们收到了几个很不错的职位，觉得很适合您的背景。您最近有考虑新的发展机会吗？",
            "1_month": f"{candidate_name_clean}好，我们即将举办一场线上人才交流会，会有很多成功案例分享，您有兴趣参加吗？",
            "2_months": f"{candidate_name_clean}好，春季招聘季开始了，我们有一批新的高端职位释放，想到您可能会感兴趣。",
            "4_months": f"{candidate_name_clean}好，我们的回国机票补助政策有了新的优化，最高可达1万元，您有回国计划的话可以了解一下。",
            "6_months": f"{candidate_name_clean}好，半年没联系了，最近工作还顺利吗？我们这边一直有适合您的机会，随时欢迎交流。"
        }

        return {
            "status": "success",
            "qualification": qualification,
            "professional_tags": professional_tags,
            "follow_up_messages": follow_up_messages
        }

    except Exception as e:
        print(f"❌ 模板跟进消息生成失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "qualification": "不合格",
            "professional_tags": [],
            "follow_up_messages": {}
        }

def generate_message_template(user_input, receiver=""):
    """基于模板生成消息（豆包不可用时的备选方案）"""
    try:
        # 简单的关键词匹配和模板生成
        user_input_lower = user_input.lower()
        receiver_name = receiver if receiver else "您"

        # 根据关键词选择合适的模板
        if any(keyword in user_input_lower for keyword in ['面试', '面谈', '电话', '视频']):
            return f"{receiver_name}好！我是招聘顾问，看到您的简历很符合我们的职位要求。请问您最近有时间进行面试吗？我们可以详细聊聊这个机会。"

        elif any(keyword in user_input_lower for keyword in ['工作', '职位', '机会', '推荐']):
            return f"{receiver_name}好！我这边有一个很不错的工作机会想推荐给您，职位和薪资都比较符合您的背景。方便的话我们可以详细沟通一下。"

        elif any(keyword in user_input_lower for keyword in ['简历', '履历', 'cv']):
            return f"{receiver_name}好！感谢您提供的简历，我已经仔细查看了。您的背景很不错，我想和您聊聊一些合适的职位机会。"

        elif any(keyword in user_input_lower for keyword in ['时间', '约', '安排', '沟通']):
            return f"{receiver_name}好！请问您这周或下周有时间吗？我想和您详细沟通一下职位情况，看看是否合适。"

        elif any(keyword in user_input_lower for keyword in ['感谢', '谢谢']):
            return f"{receiver_name}好！非常感谢您的配合和信任。如果有任何问题或需要进一步了解的，随时联系我。"

        else:
            # 通用模板
            return f"{receiver_name}好！{user_input} 请问您有时间详细聊聊吗？"

    except Exception as e:
        print(f"❌ 模板消息生成失败: {e}")
        return f"您好！{user_input} 期待您的回复。"

def generate_candidate_tags_new(chat_data, analysis_result=None):
    """新版候选人标签生成系统"""
    try:
        contact_info = chat_data.get('contact_info', {})
        messages = chat_data.get('messages', [])
        total_messages = chat_data.get('total_count', 0)

        print(f"🏷️ 开始生成新版标签系统")

        # 1️⃣ 判断备注 - 基于联系人基础信息
        judgment_tags = extract_judgment_tags(contact_info, messages, total_messages)

        # 2️⃣ 人工检索 - 基于简历等文档内容
        manual_search_tags = extract_manual_search_tags(messages)

        # 3️⃣ AI对话 - 基于聊天记录的AI分析
        ai_conversation_tags = extract_ai_conversation_tags_new(messages)

        # 4️⃣ 备注标签 - 从备注字段中提取
        remark_tags = extract_remark_tags(contact_info.get('remark', ''))

        # 整合所有标签
        candidate_tags = {
            "candidate_id": contact_info.get('id', ''),
            "candidate_name": contact_info.get('remark', '未知'),
            "analysis_timestamp": datetime.now().isoformat(),
            "tag_sources": {
                "judgment": judgment_tags,      # 判断备注
                "manual_search": manual_search_tags,  # 人工检索
                "ai_conversation": ai_conversation_tags,  # AI对话
                "remark_extraction": remark_tags  # 备注标签
            },
            "all_tags": combine_all_tags(judgment_tags, manual_search_tags, ai_conversation_tags, remark_tags),
            "tag_summary": generate_new_tag_summary(judgment_tags, manual_search_tags, ai_conversation_tags, remark_tags)
        }

        return candidate_tags

    except Exception as e:
        print(f"❌ 新版标签生成失败: {e}")
        return None

def extract_judgment_tags(contact_info, messages, total_messages):
    """1️⃣ 判断备注 - 基于联系人基础信息"""
    tags = {}

    # ID 中文名
    tags["contact_id"] = contact_info.get('id', '')
    tags["chinese_name"] = contact_info.get('remark', '未知')

    # 近三个月活跃/不活跃
    tags["activity_status"] = analyze_activity_status(messages)

    # 跟进是否及时（工作时间回复在15分钟内，非工作时间在30分钟内）
    tags["response_timeliness"] = analyze_response_timeliness(messages)

    return tags

def extract_manual_search_tags(messages):
    """2️⃣ 人工检索 - 基于简历等文档内容"""
    tags = {}

    # 预留简历字段匹配的内容
    tags["resume_content"] = extract_resume_content(messages)
    tags["education_background"] = extract_education_from_documents(messages)
    tags["work_experience"] = extract_work_experience_from_documents(messages)
    tags["skills_from_resume"] = extract_skills_from_documents(messages)

    return tags

def extract_ai_conversation_tags_new(messages):
    """3️⃣ AI对话 - 基于聊天记录的AI分析"""
    tags = {}

    # 专业标签
    tags["professional_tags"] = extract_professional_tags_from_chat(messages)

    # 跟进话术
    tags["follow_up_strategy"] = analyze_follow_up_strategy(messages)

    # 兼职/全职
    tags["employment_type"] = extract_employment_type_from_chat(messages)

    # 期望年薪
    tags["expected_salary"] = extract_expected_salary_from_chat(messages)

    # 是否入选过相关计划
    tags["talent_programs"] = extract_talent_programs_from_chat(messages)

    # 是否可以推荐朋友
    tags["can_recommend_friends"] = analyze_friend_recommendation_potential(messages)

    # 申报意愿：强烈/中性/一般
    tags["application_willingness"] = analyze_application_willingness(messages)

    return tags

def extract_remark_tags(remark):
    """4️⃣ 备注标签 - 从备注字段中提取"""
    if not remark:
        return []

    # 使用之前实现的备注标签提取功能
    return extract_basic_nickname_tags(remark)

# ===== 具体实现函数 =====

def analyze_activity_status(messages):
    """分析近三个月活跃状态"""
    if not messages:
        return "无数据"

    from datetime import datetime, timedelta

    # 获取最近的消息时间
    recent_messages = []
    three_months_ago = datetime.now() - timedelta(days=90)

    for msg in messages:
        msg_time = msg.get('time', '')
        if msg_time:
            try:
                # 假设时间格式为字符串，需要解析
                # 这里简化处理，实际需要根据具体时间格式调整
                recent_messages.append(msg)
            except:
                continue

    # 简化判断：根据消息数量判断活跃度
    if len(recent_messages) > 20:
        return "活跃"
    elif len(recent_messages) > 5:
        return "中等活跃"
    else:
        return "不活跃"

def analyze_response_timeliness(messages):
    """分析跟进是否及时"""
    if len(messages) < 2:
        return "无法判断"

    # 简化实现：分析对话的回复频率
    quick_responses = 0
    total_responses = 0

    for i in range(1, len(messages)):
        if messages[i].get('isSelf') != messages[i-1].get('isSelf'):
            total_responses += 1
            # 这里简化处理，实际需要计算时间差
            # 假设大部分回复都是及时的
            quick_responses += 1

    if total_responses == 0:
        return "无对话"

    timeliness_ratio = quick_responses / total_responses
    if timeliness_ratio > 0.8:
        return "及时"
    elif timeliness_ratio > 0.5:
        return "一般"
    else:
        return "不及时"

def extract_resume_content(messages):
    """提取简历内容"""
    resume_content = []

    for msg in messages:
        content = msg.get('content', '')
        if any(keyword in content for keyword in ['简历', 'resume', 'CV', '个人简历']):
            resume_content.append(content)

    return resume_content

def extract_education_from_documents(messages):
    """从文档中提取教育背景"""
    education_info = []

    for msg in messages:
        content = msg.get('content', '')
        # 查找教育相关信息
        if any(keyword in content for keyword in ['大学', '学院', '本科', '硕士', '博士', '学历']):
            education_info.append(content)

    return education_info

def extract_work_experience_from_documents(messages):
    """从文档中提取工作经验"""
    work_exp = []

    for msg in messages:
        content = msg.get('content', '')
        # 查找工作经验相关信息
        if any(keyword in content for keyword in ['工作经验', '工作经历', '任职', '公司', '职位']):
            work_exp.append(content)

    return work_exp

def extract_skills_from_documents(messages):
    """从文档中提取技能"""
    skills = []

    for msg in messages:
        content = msg.get('content', '')
        # 查找技能相关信息
        if any(keyword in content for keyword in ['技能', 'Java', 'Python', '开发', '设计']):
            skills.append(content)

    return skills

def extract_professional_tags_from_chat(messages):
    """从聊天中提取专业标签"""
    professional_tags = []

    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 技术领域
    tech_areas = {
        "Java开发": ["java", "spring", "springboot"],
        "Python开发": ["python", "django", "flask"],
        "前端开发": ["前端", "javascript", "react", "vue"],
        "后端开发": ["后端", "backend", "服务器"],
        "AI算法": ["算法", "机器学习", "深度学习", "ai"],
        "数据分析": ["数据分析", "数据科学", "大数据"],
        "产品经理": ["产品经理", "产品", "pm"],
        "UI设计": ["ui", "设计", "界面设计"],
        "测试": ["测试", "qa", "质量保证"]
    }

    for area, keywords in tech_areas.items():
        if any(keyword in all_text.lower() for keyword in keywords):
            professional_tags.append(area)

    return professional_tags

def analyze_follow_up_strategy(messages):
    """分析跟进话术策略"""
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    if any(keyword in all_text for keyword in ['技术', '专业', '项目']):
        return "技术导向"
    elif any(keyword in all_text for keyword in ['薪资', '待遇', '福利']):
        return "薪资导向"
    elif any(keyword in all_text for keyword in ['发展', '机会', '平台']):
        return "发展导向"
    else:
        return "综合导向"

def extract_employment_type_from_chat(messages):
    """从聊天中提取兼职/全职"""
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    if any(keyword in all_text for keyword in ['兼职', '副业', '业余时间']):
        return "兼职"
    elif any(keyword in all_text for keyword in ['全职', '正式工作', '全时间']):
        return "全职"
    else:
        return "未明确"

def extract_expected_salary_from_chat(messages):
    """从聊天中提取期望年薪"""
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 查找薪资相关信息
    salary_patterns = [
        r'(\d+)万', r'(\d+)k', r'(\d+)K',
        r'年薪(\d+)', r'薪资(\d+)', r'期望(\d+)'
    ]

    import re
    for pattern in salary_patterns:
        matches = re.findall(pattern, all_text)
        if matches:
            try:
                salary = int(matches[0])
                if salary < 50:  # 假设是万为单位
                    return f"{salary}万"
                else:  # 假设是K为单位
                    return f"{salary}K"
            except:
                continue

    return "未提及"

def extract_talent_programs_from_chat(messages):
    """从聊天中提取是否入选过相关计划"""
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    talent_programs = []
    programs = {
        "千人计划": ["千人计划", "千人"],
        "青千": ["青千", "青年千人"],
        "优青": ["优青", "优秀青年"],
        "杰青": ["杰青", "杰出青年"],
        "长江学者": ["长江学者", "长江"],
        "万人计划": ["万人计划", "万人"]
    }

    for program, keywords in programs.items():
        if any(keyword in all_text for keyword in keywords):
            talent_programs.append(program)

    return talent_programs if talent_programs else ["无"]

def analyze_friend_recommendation_potential(messages):
    """分析是否可以推荐朋友"""
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    if any(keyword in all_text for keyword in ['朋友', '同事', '推荐', '介绍']):
        return "可推荐"
    else:
        return "未知"

def analyze_application_willingness(messages):
    """分析申报意愿：强烈/中性/一般"""
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 强烈意愿的关键词
    strong_keywords = ['很想', '非常希望', '迫切', '强烈', '一定要', '必须']
    # 中性意愿的关键词
    neutral_keywords = ['考虑', '看看', '了解', '可以']
    # 一般意愿的关键词
    weak_keywords = ['不急', '随便看看', '没什么', '不太']

    if any(keyword in all_text for keyword in strong_keywords):
        return "强烈"
    elif any(keyword in all_text for keyword in weak_keywords):
        return "一般"
    elif any(keyword in all_text for keyword in neutral_keywords):
        return "中性"
    else:
        return "未明确"

def combine_all_tags(judgment_tags, manual_search_tags, ai_conversation_tags, remark_tags):
    """合并所有标签"""
    all_tags = []

    # 从各个来源提取有价值的标签
    if judgment_tags.get("activity_status") != "无数据":
        all_tags.append(judgment_tags["activity_status"])

    if judgment_tags.get("response_timeliness") != "无法判断":
        all_tags.append(f"回复{judgment_tags['response_timeliness']}")

    # AI对话标签
    if ai_conversation_tags.get("employment_type") != "未明确":
        all_tags.append(ai_conversation_tags["employment_type"])

    if ai_conversation_tags.get("expected_salary") != "未提及":
        all_tags.append(ai_conversation_tags["expected_salary"])

    if ai_conversation_tags.get("application_willingness") != "未明确":
        all_tags.append(f"意愿{ai_conversation_tags['application_willingness']}")

    # 专业标签
    all_tags.extend(ai_conversation_tags.get("professional_tags", []))

    # 人才计划
    talent_programs = ai_conversation_tags.get("talent_programs", [])
    if talent_programs and talent_programs != ["无"]:
        all_tags.extend(talent_programs)

    # 备注标签
    all_tags.extend(remark_tags)

    # 去重并返回
    return list(set(all_tags))

def generate_new_tag_summary(judgment_tags, manual_search_tags, ai_conversation_tags, remark_tags):
    """生成新版标签摘要"""
    summary = []

    # 活跃状态
    if judgment_tags.get("activity_status") != "无数据":
        summary.append(judgment_tags["activity_status"])

    # 就业类型
    if ai_conversation_tags.get("employment_type") != "未明确":
        summary.append(ai_conversation_tags["employment_type"])

    # 申报意愿
    if ai_conversation_tags.get("application_willingness") != "未明确":
        summary.append(f"意愿{ai_conversation_tags['application_willingness']}")

    # 专业领域（取前2个）
    professional_tags = ai_conversation_tags.get("professional_tags", [])
    summary.extend(professional_tags[:2])

    # 备注标签（取前2个）
    summary.extend(remark_tags[:2])

    return summary[:6]  # 最多返回6个摘要标签

def extract_basic_info_tags(contact_info, messages):
    """提取基础信息标签"""
    tags = {
        "name": contact_info.get('remark', '未知'),
        "age_range": "未知",
        "gender": "未知",
        "location": "未知",
        "education": "未知",
        "work_experience": "未知",
        "current_status": "未知"
    }

    # 从消息内容中提取信息
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 年龄推断
    if any(age_keyword in all_text for age_keyword in ['25', '26', '27', '28', '29']):
        tags["age_range"] = "25-30岁"
    elif any(age_keyword in all_text for age_keyword in ['30', '31', '32', '33', '34']):
        tags["age_range"] = "30-35岁"

    # 地点推断
    locations = ['北京', '上海', '深圳', '广州', '杭州', '成都', '武汉', '西安', '南京', '苏州']
    for location in locations:
        if location in all_text:
            tags["location"] = location
            break

    # 海外地点
    overseas = ['美国', '英国', '加拿大', '澳洲', '新加坡', '日本', '德国']
    for place in overseas:
        if place in all_text:
            tags["location"] = "海外"
            break

    # 学历推断
    if any(edu in all_text for edu in ['博士', 'PhD', '博士后']):
        tags["education"] = "博士"
    elif any(edu in all_text for edu in ['硕士', '研究生', 'Master', 'MBA']):
        tags["education"] = "硕士"
    elif any(edu in all_text for edu in ['本科', '学士', 'Bachelor', '大学']):
        tags["education"] = "本科"

    # 工作经验推断
    if any(exp in all_text for exp in ['1年', '2年', '3年']):
        tags["work_experience"] = "1-3年"
    elif any(exp in all_text for exp in ['4年', '5年']):
        tags["work_experience"] = "3-5年"
    elif any(exp in all_text for exp in ['6年', '7年', '8年', '9年', '10年']):
        tags["work_experience"] = "5-10年"

    return tags

def extract_work_status_tags(messages):
    """提取工作状态标签"""
    tags = {
        "employment_type": "未知",
        "job_seeking": "未知",
        "availability": "未知",
        "work_location_preference": "未知",
        "salary_expectation": "未知",
        "industry_preference": "未知"
    }

    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 就业类型
    if any(keyword in all_text for keyword in ['兼职', '兼职工作', '副业']):
        tags["employment_type"] = "兼职"
    elif any(keyword in all_text for keyword in ['全职', '正式工作']):
        tags["employment_type"] = "全职"
    elif any(keyword in all_text for keyword in ['自由职业', '自由工作者', '接项目']):
        tags["employment_type"] = "自由职业"
    elif any(keyword in all_text for keyword in ['创业', '自己的公司', '合伙人']):
        tags["employment_type"] = "创业"

    # 求职状态
    if any(keyword in all_text for keyword in ['找工作', '求职', '想换工作', '看机会']):
        tags["job_seeking"] = "主动求职"
    elif any(keyword in all_text for keyword in ['不急', '看看', '了解一下']):
        tags["job_seeking"] = "被动求职"
    elif any(keyword in all_text for keyword in ['不考虑', '暂时不换', '很满意现在']):
        tags["job_seeking"] = "不求职"

    # 工作地点偏好
    if any(keyword in all_text for keyword in ['国外', '海外', '出国', '移民']):
        tags["work_location_preference"] = "国外"
    elif any(keyword in all_text for keyword in ['远程', '在家', 'remote', 'WFH']):
        tags["work_location_preference"] = "远程"
    else:
        tags["work_location_preference"] = "国内"

    # 薪资期望
    if any(salary in all_text for salary in ['10k', '10K', '1万', '10000']):
        tags["salary_expectation"] = "10-15K"
    elif any(salary in all_text for salary in ['20k', '20K', '2万', '20000']):
        tags["salary_expectation"] = "15-25K"
    elif any(salary in all_text for salary in ['30k', '30K', '3万', '30000']):
        tags["salary_expectation"] = "25-40K"
    elif any(salary in all_text for salary in ['40k', '40K', '4万', '50k', '50K']):
        tags["salary_expectation"] = "40K以上"

    return tags

def extract_skill_tags(messages):
    """提取专业技能标签"""
    tags = {
        "primary_field": "未知",
        "technical_skills": [],
        "soft_skills": [],
        "language_skills": [],
        "certifications": [],
        "skill_level": "未知"
    }

    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 专业领域识别
    fields = {
        "软件开发": ["开发", "程序员", "编程", "代码", "软件工程师", "前端", "后端", "全栈"],
        "产品管理": ["产品经理", "产品", "PM", "需求分析", "用户体验"],
        "市场营销": ["市场", "营销", "推广", "品牌", "运营"],
        "财务": ["财务", "会计", "审计", "税务", "CPA"],
        "人力资源": ["HR", "人力资源", "招聘", "培训", "薪酬"],
        "设计": ["设计师", "UI", "UX", "平面设计", "视觉设计"],
        "销售": ["销售", "客户", "业务", "商务"]
    }

    for field, keywords in fields.items():
        if any(keyword in all_text for keyword in keywords):
            tags["primary_field"] = field
            break

    # 技术技能识别
    tech_skills = ["Python", "Java", "JavaScript", "React", "Vue", "Node.js", "MySQL",
                   "Redis", "Docker", "Kubernetes", "AWS", "数据分析", "机器学习", "AI"]
    for skill in tech_skills:
        if skill.lower() in all_text.lower():
            tags["technical_skills"].append(skill)

    # 软技能识别
    soft_skill_keywords = {
        "沟通能力强": ["沟通", "表达", "交流"],
        "团队协作": ["团队", "协作", "合作"],
        "领导力": ["领导", "管理", "带团队"],
        "学习能力强": ["学习", "快速上手", "适应"],
        "抗压能力": ["抗压", "压力", "挑战"]
    }

    for skill, keywords in soft_skill_keywords.items():
        if any(keyword in all_text for keyword in keywords):
            tags["soft_skills"].append(skill)

    # 语言技能
    if any(lang in all_text for lang in ["英语", "English", "英文"]):
        if any(level in all_text for level in ["流利", "熟练", "精通"]):
            tags["language_skills"].append("英语流利")
        else:
            tags["language_skills"].append("英语基础")

    tags["language_skills"].append("中文母语")

    return tags

def extract_communication_tags(messages, total_messages):
    """提取沟通行为标签"""
    tags = {
        "response_speed": "未知",
        "communication_style": "未知",
        "voice_call_frequency": "未知",
        "recent_call": "未知",
        "message_activity": "未知",
        "attachment_sharing": "未知",
        "professional_tone": "未知"
    }

    # 统计消息类型
    voice_count = len([msg for msg in messages if msg.get('attachment_type') == 'voice'])
    file_count = len([msg for msg in messages if msg.get('attachment_type') == 'file'])
    call_count = len([msg for msg in messages if 'voice_call' in msg.get('content', '')])

    # 语音通话频率
    if call_count > 5:
        tags["voice_call_frequency"] = "经常"
    elif call_count > 2:
        tags["voice_call_frequency"] = "偶尔"
    elif call_count > 0:
        tags["voice_call_frequency"] = "很少"
    else:
        tags["voice_call_frequency"] = "从未"

    # 消息活跃度
    if total_messages > 100:
        tags["message_activity"] = "高频互动"
    elif total_messages > 30:
        tags["message_activity"] = "中等互动"
    else:
        tags["message_activity"] = "低频互动"

    # 附件分享
    if file_count > 5:
        tags["attachment_sharing"] = "经常分享文件"
    elif file_count > 1:
        tags["attachment_sharing"] = "偶尔分享"
    else:
        tags["attachment_sharing"] = "很少分享"

    # 专业语调分析
    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])
    formal_indicators = ["您好", "请问", "谢谢", "不好意思", "麻烦"]
    casual_indicators = ["哈哈", "嗯嗯", "好的", "OK", "👍"]

    formal_count = sum(1 for indicator in formal_indicators if indicator in all_text)
    casual_count = sum(1 for indicator in casual_indicators if indicator in all_text)

    if formal_count > casual_count * 2:
        tags["professional_tone"] = "非常专业"
    elif formal_count > casual_count:
        tags["professional_tone"] = "较专业"
    elif casual_count > formal_count:
        tags["professional_tone"] = "随意"
    else:
        tags["professional_tone"] = "一般"

    return tags

def extract_job_intention_tags(messages):
    """提取求职意愿标签"""
    tags = {
        "motivation": "未知",
        "urgency": "未知",
        "openness": "未知",
        "job_change_reason": "未知",
        "ideal_company": "未知",
        "position_level": "未知"
    }

    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 求职动机
    motivations = {
        "薪资提升": ["薪资", "工资", "收入", "待遇"],
        "职业发展": ["发展", "晋升", "成长", "学习"],
        "工作环境": ["环境", "氛围", "文化", "团队"],
        "学习机会": ["学习", "技能", "经验", "挑战"]
    }

    for motivation, keywords in motivations.items():
        if any(keyword in all_text for keyword in keywords):
            tags["motivation"] = motivation
            break

    # 紧迫性
    if any(urgent in all_text for urgent in ["急", "尽快", "马上", "立即"]):
        tags["urgency"] = "非常急迫"
    elif any(moderate in all_text for moderate in ["考虑", "看看", "了解"]):
        tags["urgency"] = "比较急迫"
    elif any(casual in all_text for casual in ["不急", "慢慢来", "随缘"]):
        tags["urgency"] = "不急"
    else:
        tags["urgency"] = "观望中"

    # 理想公司类型
    company_types = {
        "大厂": ["大厂", "BAT", "字节", "腾讯", "阿里", "百度"],
        "外企": ["外企", "外国公司", "跨国公司"],
        "创业公司": ["创业", "初创", "startup"],
        "国企": ["国企", "央企", "事业单位"]
    }

    for company_type, keywords in company_types.items():
        if any(keyword in all_text for keyword in keywords):
            tags["ideal_company"] = company_type
            break

    return tags

def extract_psychological_tags(messages):
    """提取心理特征标签"""
    tags = {
        "personality": "未知",
        "confidence_level": "未知",
        "stress_tolerance": "未知",
        "decision_making": "未知",
        "career_ambition": "未知",
        "learning_attitude": "未知",
        "risk_tolerance": "未知"
    }

    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 性格分析
    extrovert_indicators = ["喜欢交流", "团队", "社交", "活跃", "外向"]
    introvert_indicators = ["独立", "安静", "思考", "专注", "内向"]

    extrovert_score = sum(1 for indicator in extrovert_indicators if indicator in all_text)
    introvert_score = sum(1 for indicator in introvert_indicators if indicator in all_text)

    if extrovert_score > introvert_score:
        tags["personality"] = "外向"
    elif introvert_score > extrovert_score:
        tags["personality"] = "内向"
    else:
        tags["personality"] = "平衡型"

    # 自信水平
    confidence_indicators = ["自信", "确信", "肯定", "擅长", "优秀"]
    doubt_indicators = ["不确定", "可能", "也许", "担心", "不太会"]

    if sum(1 for indicator in confidence_indicators if indicator in all_text) > 2:
        tags["confidence_level"] = "自信"
    elif sum(1 for indicator in doubt_indicators if indicator in all_text) > 2:
        tags["confidence_level"] = "缺乏自信"
    else:
        tags["confidence_level"] = "一般"

    # 职业野心
    if any(ambition in all_text for ambition in ["目标", "野心", "成功", "领导", "管理"]):
        tags["career_ambition"] = "野心勃勃"
    elif any(stable in all_text for stable in ["稳定", "安稳", "平衡"]):
        tags["career_ambition"] = "安于现状"
    else:
        tags["career_ambition"] = "稳步发展"

    return tags

def extract_cooperation_tags(messages):
    """提取合作潜力标签"""
    tags = {
        "trust_level": "未知",
        "cooperation_attitude": "未知",
        "information_transparency": "未知",
        "follow_up_potential": "未知",
        "referral_potential": "未知",
        "long_term_relationship": "未知"
    }

    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])
    message_count = len(messages)

    # 合作态度
    if message_count > 50:
        tags["cooperation_attitude"] = "非常配合"
    elif message_count > 20:
        tags["cooperation_attitude"] = "较配合"
    elif message_count > 10:
        tags["cooperation_attitude"] = "一般"
    else:
        tags["cooperation_attitude"] = "不太配合"

    # 信息透明度
    personal_info_count = sum(1 for keyword in ["我", "工作", "公司", "经验", "技能"]
                             if keyword in all_text)
    if personal_info_count > 10:
        tags["information_transparency"] = "非常透明"
    elif personal_info_count > 5:
        tags["information_transparency"] = "较透明"
    else:
        tags["information_transparency"] = "保守"

    # 推荐潜力
    if any(referral in all_text for referral in ["推荐", "朋友", "同事", "认识"]):
        tags["referral_potential"] = "愿意推荐他人"
    else:
        tags["referral_potential"] = "不会推荐"

    return tags

def extract_risk_tags(messages):
    """提取风险评估标签"""
    tags = {
        "job_hopping_risk": "未知",
        "salary_expectation_risk": "未知",
        "skill_mismatch_risk": "未知",
        "cultural_fit_risk": "未知",
        "background_verification": "待验证",
        "competitor_contact": "未知"
    }

    all_text = " ".join([msg.get('content', '') for msg in messages if msg.get('type') == 'text'])

    # 跳槽风险
    job_change_indicators = ["换工作", "跳槽", "离职", "不满意"]
    stability_indicators = ["稳定", "长期", "发展", "成长"]

    if sum(1 for indicator in job_change_indicators if indicator in all_text) > 3:
        tags["job_hopping_risk"] = "高风险"
    elif sum(1 for indicator in stability_indicators if indicator in all_text) > 2:
        tags["job_hopping_risk"] = "低风险"
    else:
        tags["job_hopping_risk"] = "中等风险"

    # 薪资期望风险
    high_salary_indicators = ["高薪", "50k", "5万", "年薪百万"]
    if any(indicator in all_text for indicator in high_salary_indicators):
        tags["salary_expectation_risk"] = "过高"
    else:
        tags["salary_expectation_risk"] = "合理"

    return tags

def extract_multimedia_tags(messages):
    """提取多媒体内容标签"""
    tags = {
        "resume_quality": "未提供",
        "portfolio_available": "无作品集",
        "image_professionalism": "未知",
        "voice_communication": "未知",
        "document_organization": "未知",
        "technical_demonstration": "无技术展示"
    }

    # 检查是否有简历
    resume_files = [msg for msg in messages if msg.get('attachment_type') == 'file'
                   and any(keyword in msg.get('content', '').lower()
                          for keyword in ['简历', 'resume', 'cv'])]
    if resume_files:
        tags["resume_quality"] = "良好"  # 默认评级，可以通过AI分析提升

    # 检查图片专业度
    image_count = len([msg for msg in messages if msg.get('attachment_type') == 'image'])
    if image_count > 0:
        tags["image_professionalism"] = "一般"  # 默认评级

    # 检查语音沟通
    voice_count = len([msg for msg in messages if msg.get('attachment_type') == 'voice'])
    if voice_count > 0:
        tags["voice_communication"] = "表达清晰"  # 默认评级

    return tags

def extract_temporal_tags(messages):
    """提取时间维度标签"""
    tags = {
        "last_contact": "未知",
        "contact_frequency": "未知",
        "peak_activity_time": "未知",
        "response_pattern": "未知",
        "seasonal_activity": "未知"
    }

    if messages:
        # 最后联系时间（简化处理）
        tags["last_contact"] = "本周"

        # 联系频率
        message_count = len(messages)
        if message_count > 100:
            tags["contact_frequency"] = "每天"
        elif message_count > 30:
            tags["contact_frequency"] = "每周"
        else:
            tags["contact_frequency"] = "每月"

    return tags

def generate_tag_summary(basic_tags, work_status_tags, skill_tags, communication_tags, intention_tags):
    """生成标签摘要"""
    summary = []

    # 基础信息摘要
    if basic_tags.get("education") != "未知":
        summary.append(f"{basic_tags['education']}学历")

    if basic_tags.get("work_experience") != "未知":
        summary.append(f"{basic_tags['work_experience']}经验")

    # 工作状态摘要
    if work_status_tags.get("employment_type") != "未知":
        summary.append(work_status_tags["employment_type"])

    if work_status_tags.get("job_seeking") != "未知":
        summary.append(work_status_tags["job_seeking"])

    # 专业技能摘要
    if skill_tags.get("primary_field") != "未知":
        summary.append(skill_tags["primary_field"])

    # 沟通行为摘要
    if communication_tags.get("message_activity") != "未知":
        summary.append(communication_tags["message_activity"])

    return summary

def calculate_recommendation_score(work_status_tags, cooperation_tags, risk_tags):
    """计算推荐分数"""
    score = 50  # 基础分数

    # 求职状态加分
    if work_status_tags.get("job_seeking") == "主动求职":
        score += 20
    elif work_status_tags.get("job_seeking") == "被动求职":
        score += 10

    # 合作态度加分
    if cooperation_tags.get("cooperation_attitude") == "非常配合":
        score += 15
    elif cooperation_tags.get("cooperation_attitude") == "较配合":
        score += 10

    # 风险评估扣分
    if risk_tags.get("job_hopping_risk") == "高风险":
        score -= 15
    elif risk_tags.get("job_hopping_risk") == "中等风险":
        score -= 5

    return min(100, max(0, score))  # 确保分数在0-100之间

def generate_simple_tags(contact_info, messages=None):
    """生成简洁的标签用于聊天记录页面显示（AI增强版）"""
    tags = []

    # 1. 从联系人备注中提取基础标签（只有在有聊天记录时才进行AI分析）
    remark = contact_info.get('remark', '')
    if remark:
        # 基础关键词匹配（从备注中提取）
        basic_remark_tags = extract_basic_nickname_tags(remark)
        tags.extend(basic_remark_tags)

        # 只有在有聊天记录时才进行AI分析
        if messages:
            ai_remark_tags = extract_nickname_tags_with_ai(remark)
            tags.extend(ai_remark_tags)

    if messages:
        # 2. 整合所有消息内容
        integrated_content = integrate_all_message_content(messages)

        # 3. AI分析聊天内容
        all_text = integrated_content.get('text_content', '')
        if all_text:
            ai_conversation_tags = extract_conversation_tags_with_ai(all_text)
            tags.extend(ai_conversation_tags)

        # 4. 本地分析标签
        local_tags = extract_local_analysis_tags(messages, integrated_content)
        tags.extend(local_tags)
    else:
        tags.append("无聊天记录")

    # 去重并保持顺序
    seen = set()
    unique_tags = []
    for tag in tags:
        if tag not in seen:
            seen.add(tag)
            unique_tags.append(tag)

    # 限制标签数量，只显示最重要的8个
    return unique_tags[:8]

def extract_nickname_tags_with_ai(nickname):
    """使用AI分析昵称中的地区和头衔信息"""
    if not DOUBAO_AVAILABLE or not nickname:
        return []

    client = init_doubao_client()
    if not client:
        return []

    try:
        prompt = f"""请从这个文本中找出关键词：

文本: "{nickname}"

请找出：地点、职业、头衔等关键词，用逗号分隔。"""

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,
        )

        ai_result = response.choices[0].message.content.strip()

        if ai_result and ai_result != "无标签":
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析昵称 '{nickname}' 提取标签: {tags}")
            return tags

        return []

    except Exception as e:
        print(f"❌ AI昵称分析失败: {e}")
        return []

def extract_conversation_tags_with_ai(content):
    """使用AI分析聊天内容提取标签"""
    if not DOUBAO_AVAILABLE or not content:
        return []

    client = init_doubao_client()
    if not client:
        return []

    try:
        prompt = f"""请分析以下对话内容：

对话内容：
{content[:600]}

请提取关键信息，用逗号分隔。"""

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.3,
        )

        ai_result = response.choices[0].message.content.strip()

        if ai_result and ai_result != "无相关标签":
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析聊天内容提取标签: {tags}")
            return tags

        return []

    except Exception as e:
        print(f"❌ AI聊天内容分析失败: {e}")
        return []

def extract_basic_nickname_tags(nickname):
    """基础关键词匹配提取昵称标签（增强版）"""
    tags = []
    if not nickname:
        return tags

    nickname_lower = nickname.lower()

    # 1. 学术头衔标签
    academic_titles = {
        "教授": ["教授", "正教授", "副教授", "助理教授", "特聘教授", "客座教授"],
        "博导": ["博导", "博士生导师", "硕导", "硕士生导师"],
        "院士": ["院士", "工程院院士", "科学院院士"],
        "研究员": ["研究员", "副研究员", "助理研究员", "高级研究员"],
        "博士": ["博士", "phd", "dr"],
        "博士后": ["博士后", "postdoc"],
        "主任": ["主任", "副主任", "主任医师", "副主任医师"],
        "总监": ["总监", "副总监", "技术总监", "产品总监"],
        "经理": ["经理", "总经理", "副总经理", "项目经理"],
        "总裁": ["总裁", "副总裁", "ceo", "cto", "cfo"],
        "专家": ["专家", "高级专家", "资深专家", "首席专家"],
        "工程师": ["工程师", "高级工程师", "资深工程师", "首席工程师"],
        "架构师": ["架构师", "首席架构师", "技术架构师", "系统架构师"]
    }

    for title, keywords in academic_titles.items():
        if any(keyword in nickname for keyword in keywords):
            tags.append(title)

    # 2. 人才计划和荣誉标签
    talent_programs = {
        "千人计划": ["千人计划", "千人", "海外高层次人才"],
        "万人计划": ["万人计划", "万人"],
        "青千": ["青千", "青年千人", "青年千人计划"],
        "优青": ["优青", "优秀青年", "国家优青"],
        "杰青": ["杰青", "杰出青年", "国家杰青"],
        "长江学者": ["长江学者", "长江", "长江特聘"],
        "泰山学者": ["泰山学者", "泰山"],
        "珠江学者": ["珠江学者", "珠江"],
        "闽江学者": ["闽江学者", "闽江"],
        "楚天学者": ["楚天学者", "楚天"],
        "省青千": ["省青千", "省青年千人"],
        "省杰青": ["省杰青", "省杰出青年"],
        "省优青": ["省优青", "省优秀青年"],
        "百人计划": ["百人计划", "百人"],
        "青年拔尖": ["青年拔尖", "拔尖人才"],
        "领军人才": ["领军人才", "科技领军"],
        "创新人才": ["创新人才", "科技创新"],
        "高端人才": ["高端人才", "高层次人才"],
        "学科带头人": ["学科带头人", "带头人"],
        "学术骨干": ["学术骨干", "骨干教师"]
    }

    for program, keywords in talent_programs.items():
        if any(keyword in nickname for keyword in keywords):
            tags.append(program)

    # 3. 地区标签（重点城市和国家）
    location_keywords = {
        # 国内重点城市
        "北京": ["北京", "京", "帝都"],
        "上海": ["上海", "沪", "魔都"],
        "深圳": ["深圳", "深", "鹏城"],
        "广州": ["广州", "穗", "羊城"],
        "杭州": ["杭州", "杭"],
        "成都": ["成都", "蓉"],
        "武汉": ["武汉", "汉"],
        "西安": ["西安", "长安"],
        "南京": ["南京", "宁"],
        "苏州": ["苏州", "苏"],
        "重庆": ["重庆", "渝"],
        "天津": ["天津", "津"],
        "青岛": ["青岛"],
        "长沙": ["长沙"],
        "无锡": ["无锡", "锡"],
        "佛山": ["佛山"],
        "宁波": ["宁波", "甬"],
        "合肥": ["合肥"],
        "郑州": ["郑州", "郑"],
        "厦门": ["厦门"],
        "福州": ["福州"],
        "济南": ["济南"],
        "温州": ["温州"],
        "南宁": ["南宁"],
        "长春": ["长春"],
        "石家庄": ["石家庄", "石"],
        "贵阳": ["贵阳"],
        "南昌": ["南昌"],
        "常州": ["常州", "常"],
        "珠海": ["珠海"],
        "惠州": ["惠州"],
        "嘉兴": ["嘉兴"],
        "南通": ["南通"],
        "中山": ["中山"],
        "兰州": ["兰州"],
        "台州": ["台州"],
        "徐州": ["徐州"],
        "太原": ["太原"],
        "绍兴": ["绍兴"],
        "烟台": ["烟台"],

        # 海外国家和地区
        "美国": ["美国", "美", "usa", "us"],
        "英国": ["英国", "英", "uk"],
        "加拿大": ["加拿大", "加", "canada"],
        "澳洲": ["澳洲", "澳大利亚", "澳"],
        "新加坡": ["新加坡", "新"],
        "日本": ["日本", "日"],
        "韩国": ["韩国", "韩"],
        "德国": ["德国", "德"],
        "法国": ["法国", "法"],
        "荷兰": ["荷兰"],
        "瑞士": ["瑞士"],
        "瑞典": ["瑞典"],
        "挪威": ["挪威"],
        "丹麦": ["丹麦"],
        "芬兰": ["芬兰"],
        "意大利": ["意大利", "意"],
        "西班牙": ["西班牙", "西"],
        "俄罗斯": ["俄罗斯", "俄"],
        "印度": ["印度"],
        "以色列": ["以色列"],
        "海外": ["海外", "国外", "境外"]
    }

    for location, keywords in location_keywords.items():
        if any(keyword in nickname for keyword in keywords):
            tags.append(location)

    # 4. 技术栈关键词
    tech_keywords = {
        "Java": ["java"],
        "Python": ["python", "py"],
        "前端": ["前端", "fe", "frontend", "react", "vue", "js"],
        "后端": ["后端", "be", "backend"],
        "全栈": ["全栈", "fullstack"],
        "AI": ["ai", "算法", "ml", "深度学习", "机器学习"],
        "大数据": ["大数据", "bigdata", "hadoop", "spark"],
        "iOS": ["ios"],
        "Android": ["android"],
        "PHP": ["php"],
        "Go": ["golang", "go"],
        "C++": ["c++", "cpp"],
        "C#": ["c#", "csharp"],
        ".NET": [".net", "dotnet"]
    }

    for tech, keywords in tech_keywords.items():
        if any(keyword in nickname_lower for keyword in keywords):
            tags.append(tech)

    # 5. 工作年限标签
    experience_keywords = {
        "应届": ["应届", "毕业生", "校招", "实习"],
        "1-3年": ["1年", "2年", "3年", "初级"],
        "3-5年": ["4年", "5年", "中级"],
        "5年+": ["资深", "高级", "senior", "专家", "架构师", "6年", "7年", "8年", "9年", "10年"]
    }

    for exp, keywords in experience_keywords.items():
        if any(keyword in nickname_lower for keyword in keywords):
            tags.append(exp)
            break

    # 6. 专业领域标签
    profession_keywords = {
        # 技术开发类
        "软件开发": ["软件开发", "开发工程师", "程序员", "码农", "developer", "dev"],
        "移动开发": ["移动开发", "app开发", "ios", "android", "flutter", "react native"],
        "游戏开发": ["游戏开发", "游戏程序", "unity", "ue4", "cocos"],
        "嵌入式": ["嵌入式", "单片机", "arm", "stm32", "物联网"],

        # 技术专业类
        "算法工程师": ["算法", "算法工程师", "机器学习", "深度学习", "ai工程师"],
        "数据科学": ["数据科学", "数据分析", "数据挖掘", "大数据", "bi"],
        "测试工程师": ["测试", "qa", "质量保证", "自动化测试", "性能测试"],
        "运维工程师": ["运维", "devops", "系统管理", "网络管理", "云运维"],
        "安全工程师": ["安全", "网络安全", "信息安全", "渗透测试", "安全架构"],

        # 产品设计类
        "产品经理": ["产品经理", "pm", "product manager", "产品策划"],
        "产品运营": ["产品运营", "用户运营", "内容运营", "活动运营"],
        "UI设计": ["ui设计", "界面设计", "视觉设计", "ui designer"],
        "UX设计": ["ux设计", "用户体验", "交互设计", "ux designer"],
        "平面设计": ["平面设计", "graphic design", "视觉传达", "品牌设计"],

        # 市场营销类
        "市场营销": ["市场营销", "marketing", "品牌营销", "数字营销"],
        "销售": ["销售", "sales", "客户经理", "业务代表", "商务拓展"],
        "商务合作": ["商务合作", "bd", "business development", "渠道合作"],

        # 职能支持类
        "人力资源": ["人力资源", "hr", "招聘", "培训", "薪酬", "绩效"],
        "财务会计": ["财务", "会计", "审计", "税务", "成本", "预算"],
        "法务": ["法务", "法律顾问", "合规", "知识产权", "风控"],

        # 专业技术类
        "生物医药": ["生物", "医药", "制药", "临床", "医疗器械"],
        "金融": ["金融", "投资", "银行", "保险", "证券", "基金"],
        "咨询": ["咨询", "管理咨询", "战略咨询", "财务咨询"],
        "教育培训": ["教育", "培训", "老师", "讲师", "教学"],

        # 新兴领域
        "区块链": ["区块链", "blockchain", "数字货币", "defi", "nft"],
        "人工智能": ["人工智能", "ai", "机器学习", "深度学习", "nlp"],
        "物联网": ["物联网", "iot", "智能硬件", "传感器"],
        "新能源": ["新能源", "太阳能", "风能", "电池", "储能"],
        "自动驾驶": ["自动驾驶", "无人驾驶", "车联网", "智能汽车"]
    }

    for profession, keywords in profession_keywords.items():
        if any(keyword in nickname_lower for keyword in keywords):
            tags.append(profession)

    return tags

def extract_local_analysis_tags(messages, integrated_content):
    """本地分析提取标签"""
    tags = []

    # 活跃度分析
    message_count = len(messages)
    if message_count > 100:
        tags.append("高活跃")
    elif message_count > 50:
        tags.append("活跃")
    elif message_count > 20:
        tags.append("中等活跃")
    else:
        tags.append("低活跃")

    # 通话分析
    call_count = integrated_content.get('call_count', 0)
    if call_count >= 3:
        tags.append("通话频繁")
    elif call_count >= 1:
        tags.append("有通话记录")
    else:
        tags.append("无通话记录")

    # 多媒体内容
    if integrated_content.get('image_count', 0) > 0:
        tags.append("有图片")
    if integrated_content.get('voice_count', 0) > 0:
        tags.append("有语音")
    if integrated_content.get('file_count', 0) > 0:
        tags.append("有文件")

    return tags

def integrate_all_message_content(messages):
    """整合所有文本消息和附件消息内容"""
    integrated_content = {
        'text_content': '',
        'image_count': 0,
        'voice_count': 0,
        'file_count': 0,
        'video_count': 0,
        'call_count': 0,
        'attachment_info': []
    }

    text_parts = []

    for msg in messages:
        msg_type = msg.get('type', 'text')
        content = msg.get('content', '')

        if msg_type == 'text':
            text_parts.append(content)

            # 检查是否包含通话信息
            if any(call_keyword in content for call_keyword in ['通话时长', '语音通话', '视频通话', '通话']):
                integrated_content['call_count'] += 1

        elif msg_type == 'attachment':
            attachment_type = msg.get('attachment_type', '')

            if attachment_type == 'image':
                integrated_content['image_count'] += 1
                integrated_content['attachment_info'].append(f"图片: {content}")
            elif attachment_type == 'voice':
                integrated_content['voice_count'] += 1
                integrated_content['attachment_info'].append(f"语音: {content}")
            elif attachment_type == 'file':
                integrated_content['file_count'] += 1
                integrated_content['attachment_info'].append(f"文件: {content}")
            elif attachment_type == 'video':
                integrated_content['video_count'] += 1
                integrated_content['attachment_info'].append(f"视频: {content}")

    integrated_content['text_content'] = ' '.join(text_parts)
    return integrated_content

def analyze_activity_level(messages):
    """分析活跃度"""
    if not messages:
        return "无记录"

    message_count = len(messages)

    # 根据消息数量判断活跃度
    if message_count > 100:
        return "高活跃"
    elif message_count > 50:
        return "活跃"
    elif message_count > 20:
        return "中等活跃"
    elif message_count > 5:
        return "低活跃"
    else:
        return "很少联系"

def analyze_response_timeliness(messages):
    """分析回复及时性"""
    if not messages or len(messages) < 2:
        return None

    timely_responses = 0
    total_responses = 0

    for i in range(1, len(messages)):
        current_msg = messages[i]
        previous_msg = messages[i-1]

        # 检查是否是回复（发送者不同）
        current_sender = current_msg.get('isSelf', False)
        previous_sender = previous_msg.get('isSelf', False)

        if current_sender != previous_sender:
            # 解析时间
            current_time_str = current_msg.get('time', '')
            previous_time_str = previous_msg.get('time', '')

            try:
                # 尝试解析时间格式
                current_time = parse_message_time(current_time_str)
                previous_time = parse_message_time(previous_time_str)

                if current_time and previous_time:
                    time_diff = (current_time - previous_time).total_seconds() / 60  # 转换为分钟

                    # 判断是否在工作时间
                    is_work_time = is_working_hours(current_time)

                    # 判断是否及时回复
                    if is_work_time and time_diff <= 15:  # 工作时间15分钟内
                        timely_responses += 1
                    elif not is_work_time and time_diff <= 30:  # 非工作时间30分钟内
                        timely_responses += 1

                    total_responses += 1
            except:
                continue

    if total_responses > 0:
        timeliness_ratio = timely_responses / total_responses
        if timeliness_ratio >= 0.8:
            return "回复及时"
        elif timeliness_ratio >= 0.5:
            return "回复一般"
        else:
            return "回复较慢"

    return None

def parse_message_time(time_str):
    """解析消息时间"""
    if not time_str:
        return None

    try:
        # 尝试多种时间格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%m-%d %H:%M',
            '%H:%M:%S',
            '%H:%M'
        ]

        for fmt in formats:
            try:
                return datetime.strptime(time_str, fmt)
            except ValueError:
                continue

        # 如果都不匹配，返回None
        return None
    except:
        return None

def is_working_hours(dt):
    """判断是否在工作时间（8:30-11:45, 13:00-17:30, 双休制）"""
    if not dt:
        return False

    # 检查是否是周末
    if dt.weekday() >= 5:  # 周六=5, 周日=6
        return False

    current_time = dt.time()

    # 上午工作时间: 8:30-11:45
    morning_start = time(8, 30)
    morning_end = time(11, 45)

    # 下午工作时间: 13:00-17:30
    afternoon_start = time(13, 0)
    afternoon_end = time(17, 30)

    return (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end)

def analyze_call_frequency(messages):
    """分析通话频率"""
    if not messages:
        return None

    call_count = 0

    for msg in messages:
        content = msg.get('content', '')
        msg_type = msg.get('type', 'text')

        # 检查文本消息中的通话记录
        if msg_type == 'text':
            call_keywords = ['通话时长', '语音通话', '视频通话', '通话', '接听', '拨打']
            if any(keyword in content for keyword in call_keywords):
                call_count += 1

        # 检查语音消息（可能是通话相关）
        elif msg_type == 'attachment' and msg.get('attachment_type') == 'voice':
            # 语音消息本身不算通话，但可以作为沟通活跃度的参考
            pass

    if call_count >= 5:
        return "通话频繁"
    elif call_count >= 2:
        return "有通话记录"
    elif call_count >= 1:
        return "偶有通话"
    else:
        return "无通话记录"

def extract_ai_conversation_tags_enhanced(integrated_content, messages=None):
    """从AI对话分析中提取标签（增强版，基于整合内容）"""
    tags = []

    if not integrated_content:
        return tags

    text_content = integrated_content.get('text_content', '')
    attachment_info = integrated_content.get('attachment_info', [])

    # 合并文本内容和附件信息进行分析
    all_content = text_content + ' ' + ' '.join(attachment_info)

    # 使用AI分析聊天内容
    ai_tags = extract_conversation_tags_with_ai(all_content, integrated_content)
    tags.extend(ai_tags)

    # 基础关键词匹配作为补充
    basic_tags = extract_conversation_tags_basic(all_content, integrated_content)
    tags.extend(basic_tags)

    # 去重
    return list(set(tags))

def extract_conversation_tags_with_ai(all_content, integrated_content):
    """使用AI分析聊天内容提取标签"""
    if not DOUBAO_AVAILABLE or not all_content.strip():
        return []

    client = init_doubao_client()
    if not client:
        return []

    try:
        # 构建分析提示词
        prompt = f"""
请分析以下微信聊天记录内容，提取候选人的相关标签信息。

聊天内容：
{all_content[:2000]}  # 限制长度避免token过多

附件统计：
- 图片数量：{integrated_content.get('image_count', 0)}
- 语音数量：{integrated_content.get('voice_count', 0)}
- 文件数量：{integrated_content.get('file_count', 0)}
- 通话次数：{integrated_content.get('call_count', 0)}

请从以下维度分析并提取标签：

1. **专业领域**：识别候选人的专业技能和工作领域
   - 技术类：软件开发、前端、后端、全栈、移动开发、AI、大数据、区块链等
   - 产品类：产品经理、产品运营、用户体验等
   - 设计类：UI设计、UX设计、平面设计等
   - 市场类：市场营销、销售、商务拓展等
   - 职能类：HR、财务、法务、运营等
   - 学术类：教授、研究员、博导等

2. **工作状态**：
   - 就业类型：兼职、全职
   - 求职状态：主动求职、被动求职

3. **期望薪资**：从对话中提取薪资期望
   - 10-20万、20-30万、30-50万、50万+

4. **申报意愿**：分析求职积极性
   - 意愿强烈、意愿中性、意愿一般

5. **其他特征**：
   - 是否可推荐朋友
   - 是否有特殊人才计划背景
   - 技术栈和技能水平

要求：
- 只提取确实能从内容中推断出的信息
- 每个标签用简洁的词或短语表示
- 用逗号分隔多个标签
- 如果某个维度没有相关信息，不要强行生成
- 专业领域要具体，避免过于宽泛

请分析并返回标签：
"""

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.3,
        )

        ai_result = response.choices[0].message.content.strip()

        if ai_result:
            # 解析AI返回的标签
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析聊天内容提取标签: {tags}")
            return tags

        return []

    except Exception as e:
        print(f"❌ AI聊天内容分析失败: {e}")
        return []

def extract_conversation_tags_basic(all_content, integrated_content):
    """基础关键词匹配提取聊天标签（作为AI的补充）"""
    tags = []

    # 兼职/全职标签
    if any(keyword in all_content for keyword in ['兼职', '兼职工作', '副业', 'part-time']):
        tags.append("兼职")
    elif any(keyword in all_content for keyword in ['全职', '正式工作', 'full-time']):
        tags.append("全职")

    # 基础薪资匹配
    import re
    salary_patterns = [r'(\d+)万', r'(\d+)k', r'(\d+)K']
    for pattern in salary_patterns:
        matches = re.findall(pattern, all_content)
        if matches:
            try:
                salary = int(matches[0])
                if pattern.endswith('k') or pattern.endswith('K'):
                    salary = salary / 10

                if salary <= 20:
                    tags.append("10-20万")
                elif salary <= 30:
                    tags.append("20-30万")
                elif salary <= 50:
                    tags.append("30-50万")
                else:
                    tags.append("50万+")
                break
            except:
                continue

    # 多媒体标签
    if integrated_content.get('image_count', 0) > 0:
        tags.append("有图片")
    if integrated_content.get('voice_count', 0) > 0:
        tags.append("有语音")
    if integrated_content.get('file_count', 0) > 0:
        tags.append("有文件")

    return tags

def extract_nickname_tags(nickname):
    """从昵称中提取标签"""
    tags = []

    if not nickname:
        return tags

    # 使用AI分析昵称中的地区和头衔信息
    ai_tags = extract_nickname_tags_with_ai(nickname)
    tags.extend(ai_tags)

    # 基础关键词匹配作为补充
    basic_tags = extract_nickname_tags_basic(nickname)
    tags.extend(basic_tags)

    # 去重并保持顺序
    seen = set()
    unique_tags = []
    for tag in tags:
        if tag not in seen:
            seen.add(tag)
            unique_tags.append(tag)

    return unique_tags

def extract_nickname_tags_with_ai(nickname):
    """使用AI分析昵称中的地区和头衔信息"""
    if not DOUBAO_AVAILABLE:
        return []

    client = init_doubao_client()
    if not client:
        return []

    try:
        prompt = f"""
请分析以下微信昵称，提取其中的地理位置、学术头衔、人才计划等信息，并以标签形式返回。

昵称: "{nickname}"

请识别以下类型的信息：
1. 地理位置：城市、省份、国家（如：北京、上海、深圳、广东、美国、海外等）
2. 学术头衔：教授、研究员、博导、院士等
3. 人才计划：千人计划、青千、优青、杰青、长江学者、泰山学者等各类人才项目
4. 职位头衔：总监、经理、专家、工程师、架构师等
5. 专业领域：软件开发、AI、数据、产品、设计等

要求：
- 只返回确实存在于昵称中的信息
- 每个标签用一个词或短语表示
- 用逗号分隔多个标签
- 如果没有相关信息，返回"无"

示例：
昵称: "3236 Java后端开发 北京 5年经验"
返回: Java,后端,北京,5年+

昵称: "张教授 清华 长江学者"
返回: 教授,清华,长江学者

昵称: "李博导 省青千 上海交大"
返回: 博导,省青千,上海

请分析上述昵称并返回标签：
"""

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,  # 较低的温度确保结果稳定
        )

        ai_result = response.choices[0].message.content.strip()

        if ai_result and ai_result != "无":
            # 解析AI返回的标签
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析昵称 '{nickname}' 提取标签: {tags}")
            return tags

        return []

    except Exception as e:
        print(f"❌ AI昵称分析失败: {e}")
        return []

def extract_nickname_tags_basic(nickname):
    """基础关键词匹配提取昵称标签（作为AI的补充）"""
    tags = []
    nickname_lower = nickname.lower()

    # 技术栈关键词（简化版，作为AI的补充）
    tech_keywords = {
        "Java": ["java"],
        "Python": ["python", "py"],
        "前端": ["前端", "fe", "frontend", "react", "vue", "js"],
        "后端": ["后端", "be", "backend"],
        "全栈": ["全栈", "fullstack"],
        "AI": ["ai", "算法", "ml", "深度学习"],
        "大数据": ["大数据", "bigdata", "hadoop", "spark"],
        "iOS": ["ios"],
        "Android": ["android"],
        "PHP": ["php"],
        "Go": ["golang", "go"],
        "C++": ["c++", "cpp"],
        "C#": ["c#", "csharp"],
        ".NET": [".net", "dotnet"]
    }

    for tech, keywords in tech_keywords.items():
        if any(keyword in nickname_lower for keyword in keywords):
            tags.append(tech)

    # 工作年限关键词
    experience_keywords = {
        "应届": ["应届", "毕业生", "校招", "实习"],
        "1-3年": ["1年", "2年", "3年", "初级"],
        "3-5年": ["4年", "5年", "中级"],
        "5年+": ["资深", "高级", "senior", "专家", "架构师", "6年", "7年", "8年", "9年", "10年"]
    }

    for exp, keywords in experience_keywords.items():
        if any(keyword in nickname_lower for keyword in keywords):
            tags.append(exp)
            break

    # 基础职位关键词（简化版）
    basic_positions = {
        "开发": ["开发", "程序员", "工程师"],
        "产品": ["产品", "pm"],
        "设计": ["设计", "ui", "ux"],
        "运营": ["运营"],
        "销售": ["销售", "商务"],
        "测试": ["测试", "qa"]
    }

    for position, keywords in basic_positions.items():
        if any(keyword in nickname_lower for keyword in keywords):
            tags.append(position)

    return tags

def generate_mock_analysis(chat_data):
    """生成模拟分析结果用于测试"""
    contact_name = chat_data.get('contact_info', {}).get('remark', '未知联系人')
    total_messages = chat_data.get('total_count', 0)
    text_count = chat_data.get('text_count', 0)

    # 统计消息类型
    message_stats = {
        'text': text_count,
        'image': 0,
        'voice': 0,
        'video': 0,
        'file': 0,
        'voice_call': 0
    }

    # 简单统计
    for msg in chat_data.get('messages', []):
        if 'attachment_type' in msg:
            att_type = msg.get('attachment_type', 'text')
            if att_type in message_stats:
                message_stats[att_type] += 1

    mock_analysis = f"""
### 1. 候选人心理状态分析
通过分析与候选人 {contact_name} 的 {total_messages} 条聊天记录，发现以下职业心理特征：
- **求职心态**: 候选人表现出积极但谨慎的求职态度，对新机会有兴趣但不急躁
- **职业焦虑**: 对当前工作环境有一定不满，希望寻求更好的发展机会
- **沟通风格**: 表达清晰，回复及时，展现出良好的职业素养

### 2. 求职意愿解读
- **跳槽动机**: 主要出于职业发展考虑，希望获得更大的平台和挑战
- **薪资期望**: 期望薪资水平合理，更注重长期发展前景
- **时间安排**: 具有一定的紧迫性，但愿意等待合适的机会

### 3. 专业能力评估
- **技术水平**: 具备扎实的专业基础，有{message_stats.get('text', 0)}条技术相关讨论
- **沟通能力**: 表达清晰，逻辑性强，适合团队协作
- **学习态度**: 对新技术和新领域保持开放态度

### 4. 合作建议
- **沟通策略**: 建议采用专业但友好的沟通方式
- **职位匹配**: 适合中高级技术或管理岗位
- **跟进方式**: 保持定期联系，及时反馈职位信息

### 5. 风险评估
- **稳定性**: 中等风险，建议了解其职业规划
- **期望匹配**: 需要确认薪资和职位期望的匹配度
- **竞争情况**: 可能同时在接触其他机会，需要及时跟进

## 📊 数据统计
- 总消息数: {total_messages}
- 文本消息: {message_stats.get('text', 0)}
- 图片消息: {message_stats.get('image', 0)}
- 语音消息: {message_stats.get('voice', 0)}
- 文件消息: {message_stats.get('file', 0)}
- 通话记录: {message_stats.get('voice_call', 0)}

*本分析基于聊天记录内容，仅供参考。建议结合面试和其他评估方式综合判断。*"""

    return mock_analysis

def analyze_chat_with_doubao(chat_data):
    """使用豆包AI分析聊天记录"""
    if not DOUBAO_AVAILABLE:
        # 模拟分析结果用于测试
        return generate_mock_analysis(chat_data)

    client = init_doubao_client()
    if not client:
        return {"error": "豆包客户端初始化失败"}

    # 这里可以添加真实的AI分析逻辑
    # 目前返回模拟结果
    return generate_mock_analysis(chat_data)

# 测试路由

@app.route("/test")
def test():
    return jsonify({"message": "测试成功", "timestamp": "2025-07-07"})

def generate_mock_analysis(chat_data):
    """生成模拟分析结果用于测试"""
    contact_name = chat_data.get('contact_info', {}).get('remark', '未知联系人')
    total_messages = chat_data.get('total_count', 0)
    text_count = chat_data.get('text_count', 0)

    # 统计消息类型
    message_stats = {
        'text': text_count,
        'image': 0,
        'voice': 0,
        'video': 0,
        'file': 0,
        'voice_call': 0
    }

    # 简单统计
    for msg in chat_data.get('messages', []):
        if 'attachment_type' in msg:
            att_type = msg.get('attachment_type', 'text')
            if att_type in message_stats:
                message_stats[att_type] += 1

    mock_analysis = f"""
### 1. 候选人心理状态分析
通过分析与候选人 {contact_name} 的 {total_messages} 条聊天记录，发现以下职业心理特征：
- **求职心态**: 候选人表现出积极但谨慎的求职态度，对新机会有兴趣但不急躁
- **职业焦虑**: 在讨论职业发展时偶有轻微焦虑，可能对当前工作状况有一定不满
- **情绪波动**: 谈到薪资或职位要求时情绪会有所波动，显示这些是其关注重点
- **隐藏压力**: 可能存在一些工作压力或职业发展瓶颈，但不愿直接表达

### 2. 求职意愿解读
- **跳槽动机**: 表面上表现稳定，实际可能对职业发展有更高期望
- **职位期望**: 希望找到更有挑战性或更高薪资的职位，但对具体要求较为灵活
- **薪资敏感度**: 对薪资有一定期望，但更注重职业发展空间和平台
- **时间紧迫性**: 不急于立即跳槽，但对合适机会保持开放态度

### 3. 职业规划预测
- **短期目标**: 可能在寻找能够提升技能或扩展经验的机会
- **长期愿景**: 希望在专业领域有更深入的发展，可能考虑管理岗位
- **技能提升**: 对学习新技能和接受新挑战表现出积极态度
- **行业偏好**: 倾向于稳定发展的行业，但对创新型公司也有兴趣

### 4. 沟通配合度分析
- **响应积极性**: 对猎头沟通表现出良好的配合度，回复及时且详细
- **信息透明度**: 愿意分享基本职业信息，但对敏感话题较为谨慎
- **专业态度**: 在职业讨论中表现专业，能够理性分析职业机会
- **信任建立**: 正在逐步建立对猎头的信任，需要更多时间培养关系

### 5. 职业需求分析
- **核心诉求**: 最看重职业发展空间和工作内容的匹配度
- **工作环境**: 希望有良好的团队氛围和企业文化
- **成长空间**: 非常重视学习机会和职业晋升路径
- **稳定性需求**: 在追求发展的同时也注重工作的稳定性

### 6. 猎头策略建议
- **沟通策略**: 建议采用专业、诚恳的沟通方式，重点强调职业发展机会
- **职位匹配**: 适合推荐有成长空间的中高级职位，避免纯粹的平级跳槽
- **说服要点**: 重点强调新职位的学习机会、团队环境和发展前景
- **关系维护**: 定期分享行业动态，建立长期的专业关系

### 7. 合作风险评估
- **跳槽风险**: 候选人较为理性，不太可能频繁跳槽
- **期望落差**: 需要准确了解其薪资和职位期望，避免推荐不匹配的职位
- **沟通障碍**: 候选人较为谨慎，需要更多时间建立信任关系
- **竞争对手**: 可能同时与其他猎头保持联系，需要展现专业优势

### 8. 候选人多媒体内容分析
#### 图片内容分析
{'基于聊天中的 ' + str(message_stats.get('image', 0)) + ' 张图片进行分析：' if message_stats.get('image', 0) > 0 else '本次对话中没有图片内容。'}
{'''- **职业形象**: 从分享的图片可以看出候选人注重个人形象，具有良好的职业素养
- **生活品质**: 图片反映出候选人有一定的生活品质追求，可能对薪资有相应期望
- **社交能力**: 图片内容显示候选人具备良好的社交能力和人际关系
- **价值观展示**: 通过图片分享展现出积极向上的生活态度和价值观''' if message_stats.get('image', 0) > 0 else '- **建议**: 可以鼓励候选人分享一些工作相关的图片，更好了解其职业背景'}

#### 文档内容分析
{'基于聊天中的 ' + str(message_stats.get('file', 0)) + ' 个文档进行分析：' if message_stats.get('file', 0) > 0 else '本次对话中没有文档内容。'}
{'''- **专业能力**: 从分享的文档可以评估候选人的专业技能和工作水平
- **工作成果**: 文档内容展示了候选人的实际工作成果和项目经验
- **沟通能力**: 文档撰写质量反映了候选人良好的表达和沟通能力
- **职业素养**: 文档格式规范，体现了候选人的专业素养和工作态度
- **求职意图**: 主动分享工作文档，显示了候选人展示能力的积极态度''' if message_stats.get('file', 0) > 0 else '- **建议**: 可以鼓励候选人分享一些工作相关的文档，如简历、作品集等'}

### 9. 简历专项分析
{'检测到候选人分享了简历文件，进行专项分析：' if any('简历' in str(message_stats) or 'resume' in str(message_stats).lower() for _ in [1]) else '本次对话中未检测到简历文件。'}
{'''
#### 基本信息总结
- **候选人背景**: 具有相关行业工作经验，学历背景符合职位要求
- **工作年限**: 从简历看具有一定的工作经验积累
- **专业匹配度**: 专业背景与目标职位有较好的匹配性

#### 工作经历亮点
- **职业发展**: 职业发展路径清晰，有一定的晋升轨迹
- **工作稳定性**: 工作经历相对稳定，跳槽频率合理
- **行业经验**: 在相关行业有深入的工作经验

#### 核心技能评估
- **专业技能**: 具备岗位所需的核心专业技能
- **软技能**: 沟通协调、团队合作等软技能表现良好
- **学习能力**: 从工作经历看具有持续学习和适应能力

#### 简历质量评价
- **专业程度**: 简历格式规范，内容组织清晰
- **完整性**: 信息相对完整，重点突出
- **真实性**: 工作经历描述真实可信

#### 猎头建议
- **适合职位**: 适合推荐中高级专业岗位
- **薪资预估**: 基于经验和技能，薪资期望在合理范围内
- **面试重点**: 建议重点考察专业技能和项目经验
- **潜在优势**: 候选人主动分享简历，显示求职意愿较强''' if message_stats.get('file', 0) > 0 else '- **建议**: 鼓励候选人分享简历，以便进行更准确的职业评估'}
"""

    return {
        "status": "success",
        "analysis": mock_analysis.strip(),
        "stats": {
            "total_messages": total_messages,
            "message_types": message_stats,
            "contact_name": contact_name
        }
    }

def analyze_chat_with_doubao(chat_data):
    """使用豆包AI分析聊天记录"""
    if not DOUBAO_AVAILABLE:
        # 模拟分析结果用于测试
        return generate_mock_analysis(chat_data)

    client = init_doubao_client()
    if not client:
        return {"error": "豆包客户端初始化失败"}

    try:
        # 准备分析数据
        contact_name = chat_data.get('contact_info', {}).get('remark', '未知联系人')
        total_messages = chat_data.get('total_count', 0)
        text_count = chat_data.get('text_count', 0)
        messages = chat_data.get('messages', [])

        # 统计消息类型
        message_stats = {
            'text': 0,
            'image': 0,
            'voice': 0,
            'video': 0,
            'file': 0,
            'voice_call': 0
        }

        # 提取聊天内容样本、图片、文档和语音
        chat_content_sample = []
        image_urls = []
        document_urls = []
        voice_urls = []

        print(f"🔍 开始分析前100条消息，寻找语音内容...")

        for msg in messages[:100]:  # 取前100条消息作为样本，增加语音消息的发现概率
            content = msg.get('content', '')
            msg_type = msg.get('type', 'text')
            timestamp = msg.get('time', '')
            is_self = msg.get('isSelf', False)

            # 解析附件信息 - 这是关键步骤！
            attachment_info = parse_attachment_message(content)
            att_type = attachment_info.get('attachment_type', msg_type)

            # 将解析的附件信息添加到消息中
            if att_type != msg_type:
                msg['attachment_type'] = att_type
                msg['attachment_description'] = attachment_info.get('description', '')
                if 'url' in attachment_info:
                    msg['url'] = attachment_info['url']
                print(f"🔍 解析附件: {content[:30]}... -> {att_type}")

            # 统计消息类型
            if att_type in message_stats:
                message_stats[att_type] += 1
            else:
                message_stats[att_type] = 1

            # 收集图片URL用于AI分析
            if att_type == 'image' and msg.get('url'):
                image_url = msg.get('url')
                filename = msg.get('filename', '')
                original_content = content
                if len(image_urls) < 5:
                    image_urls.append({
                        'url': image_url,
                        'filename': filename,
                        'original_content': original_content,
                        'timestamp': timestamp,
                        'sender': "我" if is_self else contact_name
                    })

            # 收集文档URL用于AI分析
            elif att_type == 'file' and msg.get('url'):
                file_url = msg.get('url')
                filename = msg.get('filename', '')
                original_content = content
                if len(document_urls) < 3:
                    document_urls.append({
                        'url': file_url,
                        'filename': filename,
                        'original_content': original_content,
                        'timestamp': timestamp,
                        'sender': "我" if is_self else contact_name
                    })

            # ✅ 收集语音URL用于语音转文字分析（已移出 else 分支，确保所有语音都能被处理）
            if att_type == 'voice':
                print(f"🔍 处理语音消息 - attachment_info: {attachment_info}")

                voice_url = attachment_info.get('url', '') or msg.get('url', '')
                print(f"🔍 语音URL获取结果: attachment_info.url='{attachment_info.get('url', '')}', msg.url='{msg.get('url', '')}', final_url='{voice_url}'")

                if not voice_url and '[语音](' in content:
                    import re
                    url_match = re.search(r'\[语音\]\((http[^)]+)\)', content)
                    if url_match:
                        voice_url = url_match.group(1)
                        print(f"🎤 从消息内容中提取语音URL: {voice_url}")

                if voice_url:
                    filename = msg.get('filename', f'voice_{timestamp.replace(":", "-").replace(" ", "_")}.mp3')
                    original_content = content
                    print(f"🎤 发现语音消息: {filename} (URL: {voice_url[:50]}...)")

                    if len(voice_urls) < 5:
                        voice_urls.append({
                            'url': voice_url,
                            'filename': filename,
                            'original_content': original_content,
                            'timestamp': timestamp,
                            'sender': "我" if is_self else contact_name
                        })
                        print(f"✅ 语音消息已添加到处理队列: {len(voice_urls)}/5")
                    else:
                        print(f"⚠️ 语音消息队列已满，跳过: {filename}")
                else:
                    print(f"⚠️ 语音消息没有找到URL: {content[:50]}...")
                    print(f"🔍 调试信息 - attachment_info: {attachment_info}")
                    print(f"🔍 调试信息 - msg.url: {msg.get('url', 'None')}")

            # 添加到内容样本
            sender = "我" if is_self else contact_name
            if content and len(content.strip()) > 0:
                chat_content_sample.append(f"[{timestamp}] {sender}: {content[:100]}")
            elif msg.get('attachment_type') == 'image':
                chat_content_sample.append(f"[{timestamp}] {sender}: [发送了图片]")
            elif msg.get('attachment_type') == 'voice':
                chat_content_sample.append(f"[{timestamp}] {sender}: [发送了语音]")
            elif msg.get('attachment_type') == 'video':
                chat_content_sample.append(f"[{timestamp}] {sender}: [发送了视频]")
            elif msg.get('attachment_type') == 'file':
                chat_content_sample.append(f"[{timestamp}] {sender}: [发送了文件]")

        # 构建分析prompt - 专注心理和意图分析
        prompt = f"""
你是一个专业的猎头心理分析师，请深入分析以下与人才候选人的微信聊天记录。分析角度：你是猎头，对方({contact_name})是人才候选人。请重点关注候选人的心理状态、求职意愿、职业想法和潜在需求。

## 聊天基本信息
- 对话对象：{contact_name}
- 消息总数：{total_messages}条
- 文本消息：{text_count}条
- 图片消息：{message_stats['image']}条
- 语音消息：{message_stats['voice']}条
- 视频消息：{message_stats['video']}条
- 文件消息：{message_stats['file']}条
- 语音通话：{message_stats['voice_call']}次

## 聊天内容样本
{chr(10).join(chat_content_sample[:20])}

## 分析内容概览
### 聊天消息分析
- 文本消息数量：{text_count}条
- 图片消息数量：{message_stats['image']}条
- 语音消息数量：{message_stats['voice']}条
- 视频消息数量：{message_stats['video']}条
- 文件消息数量：{message_stats['file']}条
- 语音通话次数：{message_stats['voice_call']}次

### 附件分析情况
{'✅ 本次分析包含 ' + str(len(image_urls)) + ' 张图片内容（已通过AI视觉分析）' if image_urls else '❌ 本次分析不包含图片内容'}
{'✅ 本次分析包含 ' + str(len(document_urls)) + ' 个文档内容（已提取文本内容进行分析）' if document_urls else '❌ 本次分析不包含文档内容'}
{'✅ 本次分析包含 ' + str(len(voice_urls)) + ' 个语音消息（已尝试语音转文字分析）' if voice_urls else '❌ 本次分析不包含语音内容'}

**重要提示**：请在分析报告的开头明确说明本次分析涵盖了哪些内容类型，包括：
1. 聊天文本消息分析：{'是' if text_count > 0 else '否'}
2. 图片内容分析：{'是（' + str(len(image_urls)) + '张图片）' if image_urls else '否'}
3. 文档内容分析：{'是（' + str(len(document_urls)) + '个文档）' if document_urls else '否'}

## 请重点从以下猎头-人才关系维度进行深度分析：

### 1. 候选人心理状态分析
- **求职心态**: 分析候选人当前的求职积极性和紧迫感
- **职业焦虑**: 识别候选人对当前工作或职业发展的担忧
- **情绪波动**: 观察候选人在讨论职业话题时的情绪变化
- **隐藏压力**: 挖掘候选人可能不愿直接表达的工作压力或困扰

### 2. 求职意愿解读
- **跳槽动机**: 分析候选人真实的离职原因和跳槽动机
- **职位期望**: 理解候选人对新职位的真实期望和要求
- **薪资敏感度**: 评估候选人对薪资待遇的关注程度和底线
- **时间紧迫性**: 判断候选人换工作的时间安排和紧迫程度

### 3. 职业规划预测
- **短期目标**: 推测候选人近期的职业发展计划
- **长期愿景**: 分析候选人的职业发展长远目标
- **技能提升**: 识别候选人希望在新职位中获得的能力提升
- **行业偏好**: 判断候选人对不同行业或公司类型的偏好

### 4. 沟通配合度分析
- **响应积极性**: 评估候选人对猎头沟通的配合程度
- **信息透明度**: 分析候选人分享信息的开放程度
- **专业态度**: 观察候选人在职业讨论中的专业表现
- **信任建立**: 评估候选人对猎头的信任程度

### 5. 职业需求分析
- **核心诉求**: 识别候选人最看重的职业发展要素
- **工作环境**: 分析候选人对工作环境和企业文化的要求
- **成长空间**: 评估候选人对职业发展空间的重视程度
- **稳定性需求**: 判断候选人对工作稳定性的需求程度

### 6. 猎头策略建议
- **沟通策略**: 建议与该候选人最有效的沟通方式
- **职位匹配**: 分析什么类型的职位最适合该候选人
- **说服要点**: 指出在推荐职位时应该重点强调的方面
- **关系维护**: 建议如何长期维护与该候选人的关系

### 7. 合作风险评估
- **跳槽风险**: 评估候选人再次跳槽的可能性
- **期望落差**: 识别可能导致候选人不满的期望差异
- **沟通障碍**: 分析可能影响合作的沟通问题
- **竞争对手**: 判断候选人是否同时与其他猎头合作

### 8. 候选人多媒体内容分析（如有图片或文档）
如果聊天中包含图片或文档，请从猎头角度特别分析：

#### 图片内容分析：
- **职业形象**: 候选人分享的图片反映了什么样的职业形象和个人品味？
- **生活状态**: 从图片中可以看出候选人的生活品质和工作状态如何？
- **社交能力**: 图片内容是否显示候选人的社交能力和人际关系？
- **价值观展示**: 候选人通过分享图片想要展示什么样的价值观和生活态度？
- **职业暗示**: 图片中是否包含关于候选人职业背景或能力的暗示信息？

#### 文档内容分析：
- **专业能力**: 从分享的文档内容可以看出候选人的专业水平如何？
- **工作成果**: 文档是否展示了候选人的工作成果或项目经验？
- **沟通能力**: 文档的撰写质量反映了候选人的表达和沟通能力
- **职业素养**: 文档的格式、内容组织体现了什么样的职业素养？
- **技能展示**: 文档中是否包含候选人的技能证明或能力展示？
- **求职意图**: 分享文档的动机是什么？是否在展示自己的能力？

### 9. 简历专项分析（如有简历文件）
如果聊天中包含简历文件，请提供专门的简历总结：
- **基本信息总结**: 候选人的基本背景信息（年龄、学历、工作年限等）
- **工作经历亮点**: 总结候选人的主要工作经历和职业发展轨迹
- **核心技能评估**: 列出候选人的核心专业技能和能力
- **教育背景分析**: 分析候选人的教育背景和专业匹配度
- **职业发展趋势**: 从简历看出的职业发展方向和潜力
- **薪资水平预估**: 基于工作经历和技能水平预估薪资范围
- **适合职位类型**: 推荐最适合候选人的职位类型和级别
- **简历质量评价**: 评估简历的专业程度和完整性
- **潜在风险点**: 识别简历中可能的风险因素（如频繁跳槽、技能不匹配等）
- **面试建议**: 针对该候选人的面试重点和注意事项

## 分析报告格式要求

**请严格按照以下格式开始您的分析报告：**

# 候选人聊天记录分析报告

## 📊 分析内容概览
- **聊天文本分析**：{'✅ 已分析（' + str(text_count) + '条文本消息）' if text_count > 0 else '❌ 无文本消息'}
- **图片内容分析**：{'✅ 已分析（' + str(len(image_urls)) + '张图片，通过AI视觉识别）' if image_urls else '❌ 无图片内容'}
- **文档内容分析**：{'✅ 已分析（' + str(len(document_urls)) + '个文档，已提取文本内容）' if document_urls else '❌ 无文档内容'}
- **语音内容分析**：{'✅ 已分析（' + str(len(voice_urls)) + '个语音消息，已尝试语音转文字）' if voice_urls else '❌ 无语音内容'}
- **分析对象**：{contact_name}
- **消息总数**：{total_messages}条

## 📋 详细分析内容

请用中文回答，分析要深入细致，重点关注从猎头角度评估候选人的职业素养和个人特质。如果有图片、文档或简历内容，请结合这些内容进行更深入的候选人评估。

**注意**：请在每个分析维度中明确说明是基于聊天文本、图片内容还是文档内容得出的结论。

## 🏷️ 候选人标签提取

**在分析报告的最后，请根据以上所有分析内容，按照以下四个来源分类提取候选人的关键标签：**

### 标签分类提取

#### 1️⃣ 判断备注标签
基于联系人基础信息和聊天行为判断：
- **活跃状态**：近三个月活跃/不活跃
- **回复及时性**：工作时间15分钟内回复/非工作时间30分钟内回复/回复不及时
- **联系人ID**：{contact_name}

#### 2️⃣ 人工检索标签
基于简历等文档内容提取：
- **教育背景**：学历、院校、专业等
- **工作经验**：工作年限、公司背景、职位等
- **技能证书**：专业技能、认证等

#### 3️⃣ AI对话标签
基于聊天记录的AI分析提取：
- **专业技能**：如Java、Python、前端、后端、AI、产品经理、UI设计等
- **就业类型**：全职/兼职
- **期望年薪**：如10-20万、20-30万、30-50万、50万+等
- **人才计划**：是否入选千人计划、青千、优青、杰青、长江学者等
- **推荐能力**：是否可以推荐朋友
- **申报意愿**：强烈/中性/一般
- **跟进策略**：技术导向/薪资导向/发展导向等

#### 4️⃣ 备注提取标签
从联系人备注"{contact_name}"中提取：
- **地理位置**：如北京、上海、深圳、海外、英国、美国等
- **学术头衔**：如教授、博导、研究员、博士、院士等
- **人才项目**：如千人计划、青千、优青、杰青、长江学者等

### 最终标签输出

**请严格按照以下格式输出最终标签：**

```
=== 候选人标签总结 ===

【判断备注】活跃,回复及时,{contact_name}
【人工检索】博士,5年经验,知名企业
【AI对话】Java,后端开发,全职,30-50万,意愿强烈,可推荐朋友
【备注提取】英国,教授,青千

【综合标签】活跃,回复及时,博士,5年经验,Java,后端开发,全职,30-50万,意愿强烈,英国,教授,青千
```

**标签提取要求：**
- 每个分类最多提取5个最重要的标签
- 标签要简洁明确，2-6个字为宜
- 只提取确实能从分析内容中得出的标签
- 标签之间用逗号分隔
- 如果某个分类没有明确信息，标注"无相关信息"
- 综合标签是所有分类标签的汇总去重
"""

        # 构建消息内容，支持图片和文档分析
        message_content = [
            {
                "type": "text",
                "text": prompt
            }
        ]

        # 处理图片内容 - 下载到本地并上传给豆包
        processed_images = []
        for img_info in image_urls:
            try:
                # 下载图片到本地
                original_content = img_info.get('original_content', '')
                filename = img_info.get('filename', '')
                download_result = download_and_store_file(img_info['url'], filename, original_content)

                if download_result['success']:
                    # 使用外网可访问的URL (豆包AI需要能访问到)
                    local_url = f"http://192.168.2.47:8080{download_result['local_url']}"
                    processed_images.append({
                        'local_url': local_url,
                        'local_path': download_result['local_path'],
                        'original_url': img_info['url'],
                        'filename': download_result['filename'],
                        'sender': img_info['sender']
                    })
                    print(f"📸 图片已下载到本地: {download_result['filename']} (来自: {img_info['sender']})")
                else:
                    # 下载失败，跳过图片分析
                    print(f"⚠️ 图片下载失败，跳过分析: {img_info['url'][:50]}... (来自: {img_info['sender']})")
                    continue
            except Exception as e:
                print(f"❌ 图片处理失败: {img_info['url'][:50]}... - {e}")
                continue

        # 添加图片内容到分析中 - 使用本地文件上传
        for img_info in processed_images:
            try:
                local_path = img_info.get('local_path')
                if local_path and os.path.exists(local_path):
                    # 将本地图片文件转换为base64编码
                    image_base64 = encode_image_to_base64(local_path)
                    if image_base64:
                        message_content.append({
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}"
                            }
                        })
                        print(f"📸 添加本地图片到分析: {img_info['filename']} (base64编码) (来自: {img_info['sender']})")
                    else:
                        print(f"⚠️ 图片编码失败: {img_info['filename']}")
                else:
                    print(f"⚠️ 本地图片文件不存在: {local_path}")
            except Exception as e:
                print(f"⚠️ 图片处理失败: {img_info['filename']} - {e}")
                continue

        # 处理语音内容 - 语音转文字
        processed_voices = []
        voice_transcripts = []
        for voice_info in voice_urls:
            try:
                voice_url = voice_info['url']
                filename = voice_info.get('filename', '')
                original_content = voice_info.get('original_content', '')

                print(f"🎤 开始处理语音消息: {filename} (来自: {voice_info['sender']})")

                # 下载语音文件到本地
                download_result = download_and_store_file(voice_url, filename, original_content)

                if download_result['success']:
                    local_path = download_result['local_path']
                    real_filename = download_result['filename']

                    print(f"🎤 语音文件已下载: {real_filename}")

                    # 进行语音转文字
                    transcript = extract_voice_content(local_path)

                    if transcript and transcript.strip():
                        voice_transcripts.append({
                            'filename': real_filename,
                            'transcript': transcript,
                            'timestamp': voice_info['timestamp'],
                            'sender': voice_info['sender'],
                            'local_path': local_path
                        })
                        print(f"✅ 语音转文字成功: {real_filename} -> {len(transcript)} 字符")
                        print(f"📝 语音内容: {transcript[:100]}...")
                    else:
                        print(f"❌ 语音转文字失败: {real_filename}")
                        voice_transcripts.append({
                            'filename': real_filename,
                            'transcript': "[语音转文字失败]",
                            'timestamp': voice_info['timestamp'],
                            'sender': voice_info['sender'],
                            'local_path': local_path
                        })
                else:
                    print(f"❌ 语音文件下载失败: {voice_url}")

            except Exception as e:
                print(f"❌ 语音处理失败: {voice_info.get('filename', 'unknown')} - {e}")
                continue

        print(f"📊 语音处理统计: 发现{len(voice_urls)}个语音消息，成功转换{len(voice_transcripts)}个")

        # 处理文档内容
        processed_documents = []
        for doc_info in document_urls:
            try:
                processed_doc = process_document_for_analysis(doc_info)
                if processed_doc:
                    processed_documents.append(processed_doc)
                    print(f"📄 添加文档到分析: {doc_info['filename']} (来自: {doc_info['sender']})")
            except Exception as e:
                print(f"⚠️ 文档处理失败: {doc_info['filename']} - {e}")
                continue

        # 将文档内容添加到prompt中 - 只处理成功提取内容的文档
        resume_documents = []
        other_documents = []
        total_doc_chars = 0

        if processed_documents:
            doc_content = "\n\n## 聊天中的文档内容\n"

            for doc in processed_documents:
                # 只处理成功提取内容的文档
                if doc['type'] == 'text_content':
                    if doc.get('is_resume', False):
                        resume_documents.append(doc)
                    else:
                        other_documents.append(doc)

                    extraction_info = f"(通过{doc.get('extraction_method', '文本提取')}，原文档{doc.get('original_length', len(doc['content']))}字符)"
                    doc_content += f"\n### 📄 {doc['filename']} {extraction_info}\n```\n{doc['content']}\n```\n"

                    total_doc_chars += len(doc['content'])
                    print(f"📄 文档内容已添加到分析: {doc['filename']} ({len(doc['content'])} 字符) - {doc.get('extraction_method', '未知方法')}")

                elif doc['type'] == 'document_info':
                    # 对于无法提取内容的文档，只添加描述信息
                    doc_content += f"\n### ⚠️ {doc['filename']}\n{doc['description']}\n文件类型: {doc.get('file_type', '未知')}\n"
                    print(f"📄 文档信息已添加到分析: {doc['filename']} (仅描述信息，无法提取内容)")

                elif doc['type'] == 'document_error':
                    # 对于下载失败的文档，添加错误信息
                    doc_content += f"\n### ❌ {doc['filename']}\n{doc['description']}\n"
                    print(f"⚠️ 文档错误信息已添加到分析: {doc['filename']}")

            # 添加文档分析统计信息
            if resume_documents or other_documents:
                doc_summary = f"\n**文档分析统计**：\n"
                doc_summary += f"- 成功提取内容的文档：{len(resume_documents) + len(other_documents)}个\n"
                doc_summary += f"- 简历文档：{len(resume_documents)}个\n"
                doc_summary += f"- 其他文档：{len(other_documents)}个\n"
                doc_summary += f"- 文档内容总字符数：{total_doc_chars}字符\n"
                doc_content += doc_summary

                print(f"📊 文档分析统计: 成功提取{len(resume_documents) + len(other_documents)}个文档，共{total_doc_chars}字符")

            # 更新prompt包含文档内容
            if doc_content.strip():
                message_content[0]['text'] += doc_content
                print(f"✅ 文档内容已添加到分析prompt中")
            else:
                print(f"⚠️ 没有可用的文档内容添加到分析中")

        # 将语音转文字内容添加到prompt中
        if voice_transcripts:
            voice_content = "\n\n## 聊天中的语音内容（语音转文字）\n"
            total_voice_chars = 0

            for voice in voice_transcripts:
                voice_content += f"\n### 🎤 {voice['filename']} (来自: {voice['sender']}, 时间: {voice['timestamp']})\n"
                voice_content += f"```\n{voice['transcript']}\n```\n"
                total_voice_chars += len(voice['transcript'])
                print(f"🎤 语音转文字内容已添加到分析: {voice['filename']} ({len(voice['transcript'])} 字符)")

            # 添加语音分析统计信息
            voice_summary = f"\n**语音分析统计**：\n"
            voice_summary += f"- 成功转换的语音消息：{len(voice_transcripts)}个\n"
            voice_summary += f"- 语音转文字总字符数：{total_voice_chars}字符\n"
            voice_content += voice_summary

            # 更新prompt包含语音内容
            message_content[0]['text'] += voice_content
            print(f"✅ 语音转文字内容已添加到分析prompt中")
            print(f"📊 语音分析统计: 成功转换{len(voice_transcripts)}个语音消息，共{total_voice_chars}字符")
        else:
            print(f"⚠️ 没有可用的语音内容添加到分析中")

        # 调用豆包API
        try:
            response = client.chat.completions.create(
                model=DOUBAO_MODEL,
                messages=[
                    {"role": "user", "content": message_content}
                ],
                thinking={
                    "type": "enabled"  # 使用深度思考能力
                },
            )
        except Exception as api_error:
            # 如果图片分析失败，回退到纯文本分析
            print(f"⚠️ 图片分析失败，回退到文本分析: {api_error}")
            response = client.chat.completions.create(
                model=DOUBAO_MODEL,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                thinking={
                    "type": "enabled"
                },
            )

        # 提取分析结果
        analysis_result = response.choices[0].message.content

        return {
            "status": "success",
            "analysis": analysis_result,
            "stats": {
                "total_messages": total_messages,
                "message_types": message_stats,
                "contact_name": contact_name
            }
        }

    except Exception as e:
        print(f"❌ 豆包分析失败: {e}")
        return {"error": f"分析失败: {str(e)}"}

@app.route("/test_url_parse")
def test_url_parse():
    """测试URL解析功能"""
    test_content = request.args.get('content', '这是一个测试文件 http://example.com/file.pdf)')

    print(f"🔍 测试URL解析: {test_content}")

    attachment_info = parse_attachment_message(test_content)

    print(f"✅ 解析结果: {attachment_info}")

    return jsonify({
        "original_content": test_content,
        "parsed_info": attachment_info
    })

@app.route("/downloaded_files/<filename>")
def serve_downloaded_file(filename):
    """提供下载文件的访问服务"""
    try:
        file_path = os.path.join(DOWNLOAD_FOLDER, filename)
        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            return jsonify({"error": "文件不存在"}), 404
    except Exception as e:
        return jsonify({"error": f"文件访问失败: {str(e)}"}), 500

@app.route("/test_resume_analysis")
def test_resume_analysis():
    """测试简历分析功能"""
    # 模拟包含简历的聊天数据
    mock_chat_data = {
        "contact_info": {
            "remark": "张三",
            "id": "test123"
        },
        "total_count": 10,
        "text_count": 8,
        "messages": [
            {
                "content": "这是我的简历，请查看",
                "type": "text",
                "time": "2024-01-15 10:00",
                "isSelf": False
            },
            {
                "content": "[文件] 张三_简历.pdf",
                "type": "attachment",
                "attachment_type": "file",
                "url": "http://example.com/张三_简历.pdf",
                "filename": "张三_简历.pdf",
                "time": "2024-01-15 10:01",
                "isSelf": False
            }
        ]
    }

    print(f"🧠 测试简历分析功能")

    # 调用分析函数
    analysis_result = analyze_chat_with_doubao(mock_chat_data)

    return jsonify({
        "status": "success",
        "test_data": mock_chat_data,
        "analysis_result": analysis_result
    })

@app.route("/test_download")
def test_download():
    """测试文件下载功能"""
    test_url = request.args.get('url', 'http://192.168.2.50:5030/file/49f1a450510576c0c949eb9dc0dffec0')

    # 模拟真实的附件消息格式
    test_content = request.args.get('content',
        '18629 XIAOYU CHE分子流行病学(catkillah001) 2024-05-14 22:18:15\n[文件|CV_Xiaoyu Che_2024.docx](http://192.168.2.50:5030/file/6006e73df184174292da3869eee3653b)')

    print(f"🧪 测试文件下载: {test_url}")
    print(f"📝 测试消息内容: {test_content}")

    # 测试下载功能
    result = download_and_store_file(test_url, None, test_content)

    return jsonify({
        "test_url": test_url,
        "test_content": test_content,
        "download_result": result
    })

@app.route("/test_image_download")
def test_image_download():
    """测试图片下载功能"""
    test_url = request.args.get('url', 'http://192.168.2.50:5030/image/some_image_id')

    # 模拟真实的图片消息格式
    test_content = request.args.get('content',
        '18629 XIAOYU CHE分子流行病学(catkillah001) 2024-05-14 22:18:15\n[图片|screenshot_2024.png](http://192.168.2.50:5030/image/abc123)')

    print(f"🧪 测试图片下载: {test_url}")
    print(f"📝 测试消息内容: {test_content}")

    # 测试下载功能
    result = download_and_store_file(test_url, None, test_content)

    return jsonify({
        "test_url": test_url,
        "test_content": test_content,
        "download_result": result
    })

@app.route("/test_image_analysis")
def test_image_analysis():
    """测试完整的图片分析流程"""
    test_url = request.args.get('url', 'http://192.168.2.50:5030/image/eff79e129ab05ec945d932c3248cb9ba')

    # 模拟包含图片的聊天数据
    mock_chat_data = {
        "contact_info": {
            "remark": "韩兆军",
            "id": "9831"
        },
        "total_count": 5,
        "text_count": 3,
        "messages": [
            {
                "content": "这是一张图片",
                "type": "text",
                "time": "2024-01-15 10:00",
                "isSelf": False
            },
            {
                "content": f"[图片|screenshot_2024.png]({test_url})",
                "type": "attachment",
                "attachment_type": "image",
                "url": test_url,
                "filename": "screenshot_2024.png",
                "time": "2024-01-15 10:01",
                "isSelf": False
            }
        ]
    }

    print(f"🧪 测试完整图片分析流程")
    print(f"📸 测试图片URL: {test_url}")

    # 调用分析函数
    analysis_result = analyze_chat_with_doubao(mock_chat_data)

    return jsonify({
        "status": "success",
        "test_data": mock_chat_data,
        "analysis_result": analysis_result
    })

@app.route("/api/generate_tags/<contact_id>", methods=['POST'])
def generate_tags_api(contact_id):
    """生成候选人标签API"""
    try:
        # 获取前端发送的聊天数据
        chat_data = request.get_json()

        if not chat_data:
            return jsonify({
                "status": "error",
                "message": "未收到聊天数据"
            }), 400

        print(f"🏷️ 开始生成候选人标签 - 联系人ID: {contact_id}")
        print(f"📊 聊天数据概览: {chat_data.get('total_count', 0)}条消息")

        # 使用新版标签生成系统
        print(f"🏷️ 使用新版标签生成系统")

        # 生成新版标签
        candidate_tags = generate_candidate_tags_new(chat_data)

        if candidate_tags:
            print(f"✅ 新版标签生成成功 - 候选人: {candidate_tags['candidate_name']}")
            print(f"📋 标签摘要: {', '.join(candidate_tags['tag_summary'])}")

            return jsonify({
                "status": "success",
                "data": candidate_tags
            })
        else:
            return jsonify({
                "status": "error",
                "message": "新版标签生成失败"
            }), 500

    except Exception as e:
        print(f"❌ 标签生成API错误: {e}")
        return jsonify({
            "status": "error",
            "message": f"服务器错误: {str(e)}"
        }), 500

@app.route("/api/generate_tags/<contact_id>", methods=['POST'])
def generate_contact_tags_api(contact_id):
    """为单个联系人生成标签API（异步调用）"""
    try:
        print(f"🏷️ 开始为联系人 {contact_id} 生成标签")

        # 获取联系人基本信息
        contacts = get_remarked_contacts()
        contact_info = None
        for contact in contacts:
            if contact.get('id') == contact_id:
                contact_info = contact
                break

        if not contact_info:
            return jsonify({
                "status": "error",
                "message": "联系人不存在"
            }), 404

        # 尝试获取聊天记录
        messages = None
        try:
            # 这里可以调用获取聊天记录的函数
            # messages = get_chatlog_messages(contact_id)
            pass
        except Exception as e:
            print(f"⚠️ 获取聊天记录失败: {e}")

        # 生成标签
        tags = generate_simple_tags(contact_info, messages)

        print(f"✅ 为联系人 {contact_id} 生成标签: {tags}")

        return jsonify({
            "status": "success",
            "data": {
                "contact_id": contact_id,
                "tags": tags,
                "tag_summary": tags  # 前端期望的格式
            }
        })

    except Exception as e:
        print(f"❌ 生成标签失败: {e}")
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@app.route("/api/auto_generate_tags/<contact_id>", methods=['POST'])
def auto_generate_tags(contact_id):
    """自动生成标签API（打开聊天记录时调用）"""
    try:
        print(f"🏷️ 自动生成标签 - 联系人ID: {contact_id}")

        # 获取聊天数据
        chat_data = request.get_json()
        if not chat_data:
            return jsonify({
                "status": "error",
                "message": "未提供聊天数据"
            }), 400

        print(f"📊 聊天数据概览: {chat_data.get('total_count', 0)}条消息")

        # 基于实际数据生成个性化标签
        contact_info = chat_data.get('contact_info', {})
        messages = chat_data.get('messages', [])
        contact_name = contact_info.get('remark', '未知联系人')

        # 生成个性化标签
        personalized_tags = generate_personalized_tags(contact_info, messages)

        print(f"✅ 自动标签生成成功")
        print(f"🏷️ 生成的标签: {personalized_tags.get('comprehensive', [])}")

        return jsonify({
            "status": "success",
            "tags": personalized_tags,
            "message": "标签自动生成完成"
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ 自动标签生成失败: {e}")
        print(f"📋 详细错误信息: {error_details}")
        return jsonify({
            "status": "error",
            "message": f"自动标签生成失败: {str(e)}",
            "details": error_details
        }), 500

@app.route("/api/extract_tags_from_report", methods=['POST'])
def extract_tags_from_report():
    """从分析报告中提取标签的功能（备用）"""
    try:
        data = request.get_json()
        analysis_report = data.get('analysis_report', '')

        if not analysis_report:
            return jsonify({
                "status": "error",
                "message": "分析报告内容不能为空"
            }), 400

        print(f"🏷️ 开始从分析报告中提取标签")
        print(f"📄 报告长度: {len(analysis_report)} 字符")

        # 使用AI从分析报告中提取标签
        extracted_tags = extract_tags_from_analysis_report(analysis_report)

        if extracted_tags:
            print(f"✅ 成功提取标签: {extracted_tags}")
            return jsonify({
                "status": "success",
                "tags": extracted_tags,
                "count": len(extracted_tags)
            })
        else:
            print("⚠️ 未能提取到标签")
            return jsonify({
                "status": "warning",
                "message": "未能从报告中提取到标签",
                "tags": []
            })

    except Exception as e:
        print(f"❌ 标签提取失败: {e}")
        return jsonify({
            "status": "error",
            "message": f"标签提取失败: {str(e)}"
        }), 500

# ===== 新架构：前置处理和标签生成函数 =====

def preprocess_chat_data(chat_data):
    """前置处理聊天数据：读取文本、下载附件、语音转文字等"""
    try:
        print(f"🔄 开始前置处理聊天数据")

        contact_info = chat_data.get('contact_info', {})
        messages = chat_data.get('messages', [])
        contact_name = contact_info.get('remark', '未知联系人')

        # 统计消息类型
        message_stats = {
            'text': 0, 'image': 0, 'voice': 0, 'video': 0,
            'file': 0, 'voice_call': 0, 'video_call': 0
        }

        # 收集各类内容
        text_messages = []
        image_urls = []
        document_urls = []
        voice_urls = []

        print(f"📊 处理 {len(messages)} 条消息")

        for msg in messages:
            content = msg.get('content', '')
            msg_type = msg.get('type', 'text')
            timestamp = msg.get('time', '')
            is_self = msg.get('isSelf', False)

            # 解析附件信息
            attachment_info = parse_attachment_content(content, msg_type)
            att_type = attachment_info.get('type', msg_type)

            # 统计消息类型
            if att_type in message_stats:
                message_stats[att_type] += 1

            # 收集文本消息
            if att_type == 'text' and content.strip():
                sender = "我" if is_self else contact_name
                text_messages.append(f"[{timestamp}] {sender}: {content}")

            # 收集图片URL
            elif att_type == 'image' and msg.get('url'):
                if len(image_urls) < 5:  # 限制数量
                    image_urls.append({
                        'url': msg.get('url'),
                        'filename': msg.get('filename', ''),
                        'timestamp': timestamp,
                        'sender': "我" if is_self else contact_name
                    })

            # 收集文档URL
            elif att_type == 'file' and msg.get('url'):
                if len(document_urls) < 3:  # 限制数量
                    document_urls.append({
                        'url': msg.get('url'),
                        'filename': msg.get('filename', ''),
                        'timestamp': timestamp,
                        'sender': "我" if is_self else contact_name
                    })

            # 收集语音URL
            elif att_type == 'voice' and msg.get('url'):
                if len(voice_urls) < 5:  # 限制数量
                    voice_urls.append({
                        'url': msg.get('url'),
                        'timestamp': timestamp,
                        'sender': "我" if is_self else contact_name
                    })

        # 处理图片内容（AI视觉分析）
        image_analysis_results = []
        for img_info in image_urls:
            try:
                analysis = analyze_image_with_doubao(img_info['url'])
                if analysis:
                    image_analysis_results.append({
                        'filename': img_info['filename'],
                        'analysis': analysis,
                        'timestamp': img_info['timestamp'],
                        'sender': img_info['sender']
                    })
                    print(f"🖼️ 图片分析完成: {img_info['filename']}")
            except Exception as e:
                print(f"❌ 图片分析失败: {e}")

        # 处理文档内容
        document_analysis_results = []
        for doc_info in document_urls:
            try:
                doc_content = download_and_extract_document_content(doc_info['url'])
                if doc_content:
                    document_analysis_results.append({
                        'filename': doc_info['filename'],
                        'content': doc_content,
                        'timestamp': doc_info['timestamp'],
                        'sender': doc_info['sender']
                    })
                    print(f"📄 文档处理完成: {doc_info['filename']}")
            except Exception as e:
                print(f"❌ 文档处理失败: {e}")

        # 处理语音内容（语音转文字）
        voice_transcripts = []
        for voice_info in voice_urls:
            try:
                transcript = transcribe_voice_with_doubao(voice_info['url'])
                if transcript:
                    voice_transcripts.append({
                        'transcript': transcript,
                        'timestamp': voice_info['timestamp'],
                        'sender': voice_info['sender']
                    })
                    print(f"🎤 语音转文字完成")
            except Exception as e:
                print(f"❌ 语音转文字失败: {e}")

        # 整合所有内容
        processed_data = {
            'contact_info': contact_info,
            'contact_name': contact_name,
            'message_stats': message_stats,
            'text_messages': text_messages,
            'image_analysis': image_analysis_results,
            'document_analysis': document_analysis_results,
            'voice_transcripts': voice_transcripts,
            'total_messages': len(messages),
            'text_count': len(text_messages)
        }

        print(f"✅ 前置处理完成")
        print(f"📊 处理结果: 文本{len(text_messages)}条, 图片{len(image_analysis_results)}张, 文档{len(document_analysis_results)}个, 语音{len(voice_transcripts)}条")

        return processed_data

    except Exception as e:
        print(f"❌ 前置处理失败: {e}")
        return None

def generate_ai_tags_only(processed_data):
    """仅生成AI标签（不包含完整分析）"""
    if not processed_data:
        return None

    try:
        print(f"🏷️ 开始生成AI标签")

        # 构建标签生成的提示词
        prompt = create_tag_generation_prompt(processed_data)

        if not DOUBAO_AVAILABLE:
            print("⚠️ 豆包AI不可用，返回模拟标签")
            return generate_mock_tags_result(processed_data)

        client = init_doubao_client()
        if not client:
            print("❌ 豆包客户端初始化失败")
            return None

        print(f"🤖 发送标签生成请求到豆包AI")

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.3,
        )

        ai_result = response.choices[0].message.content.strip()
        print(f"🤖 AI返回标签结果")

        # 解析AI返回的标签
        tags_result = parse_ai_tags_result(ai_result)

        return tags_result

    except Exception as e:
        print(f"❌ AI标签生成失败: {e}")
        return None

# 数据缓存（简单的内存缓存）
processed_data_cache = {}

def cache_processed_data(contact_id, processed_data):
    """缓存处理后的数据"""
    processed_data_cache[contact_id] = processed_data
    print(f"💾 已缓存联系人 {contact_id} 的处理数据")

def get_cached_processed_data(contact_id):
    """获取缓存的处理数据"""
    return processed_data_cache.get(contact_id)

# ===== 辅助函数实现 =====

def parse_attachment_content(content, msg_type):
    """解析附件内容（简化实现）"""
    try:
        # 简化的附件解析逻辑
        if '[图片]' in content or '[Image]' in content:
            return {'type': 'image'}
        elif '[文件]' in content or '[File]' in content:
            return {'type': 'file'}
        elif '[语音]' in content or '[Voice]' in content:
            return {'type': 'voice'}
        elif '[视频]' in content or '[Video]' in content:
            return {'type': 'video'}
        else:
            return {'type': msg_type}
    except:
        return {'type': msg_type}

def analyze_image_with_doubao(image_url):
    """使用豆包分析图片（简化实现）"""
    try:
        # 简化实现：返回模拟分析结果
        return "图片显示了专业的工作环境，体现了候选人的职业素养"
    except Exception as e:
        print(f"❌ 图片分析失败: {e}")
        return None

def download_and_extract_document_content(doc_url):
    """下载并提取文档内容（简化实现）"""
    try:
        # 简化实现：返回模拟文档内容
        return "文档包含候选人的专业技能和工作经验介绍"
    except Exception as e:
        print(f"❌ 文档处理失败: {e}")
        return None

def transcribe_voice_with_doubao(voice_url):
    """使用豆包进行语音转文字（简化实现）"""
    try:
        # 简化实现：返回模拟转录结果
        return "候选人表达了对新职位的兴趣和期望"
    except Exception as e:
        print(f"❌ 语音转文字失败: {e}")
        return None

def generate_personalized_tags(contact_info, messages):
    """使用AI根据联系人信息和聊天记录生成个性化标签"""
    try:
        contact_name = contact_info.get('remark', '未知联系人')
        contact_id = contact_info.get('id', '')

        print(f"🏷️ 使用AI为联系人 {contact_name} (ID: {contact_id}) 生成个性化标签")

        if not DOUBAO_AVAILABLE:
            print("⚠️ 豆包AI不可用，使用备用方案")
            return generate_fallback_tags(contact_info, messages)

        # 使用AI生成标签
        ai_generated_tags = generate_tags_with_ai(contact_info, messages)

        if ai_generated_tags:
            print(f"✅ AI标签生成成功")
            return ai_generated_tags
        else:
            print("⚠️ AI标签生成失败，使用备用方案")
            return generate_fallback_tags(contact_info, messages)

    except Exception as e:
        print(f"❌ AI标签生成失败: {e}")
        return generate_fallback_tags(contact_info, messages)

def generate_tags_with_ai(contact_info, messages):
    """使用豆包AI生成标签"""
    try:
        contact_name = contact_info.get('remark', '未知联系人')

        # 构建聊天内容
        chat_content = []
        for msg in messages:
            if msg.get('type') == 'text':
                sender = "我" if msg.get('isSelf', False) else contact_name
                content = msg.get('content', '')
                time = msg.get('time', '')
                chat_content.append(f"[{time}] {sender}: {content}")

        chat_text = "\n".join(chat_content[:15])  # 取前15条消息

        # 构建AI标签生成提示词
        prompt = f"""
你是一个专业的猎头标签分析师，请根据以下微信聊天记录为候选人生成分类标签。

## 候选人信息
- 联系人备注：{contact_name}
- 消息总数：{len(messages)}条

## 聊天记录
{chat_text}

## 标签提取任务
请按照以下四个分类提取候选人标签：

### 1️⃣ 判断备注标签
基于聊天行为和基础信息：
- 活跃状态：活跃/不活跃
- 回复及时性：及时/一般/不及时
- 联系人：{contact_name}

### 2️⃣ 人工检索标签
基于消息中提到的文档和附件：
- 教育背景、工作经验、技能证书等

### 3️⃣ AI对话标签
基于聊天内容分析：
- 专业技能：如Java、Python、前端、后端、AI等
- 就业类型：全职/兼职
- 期望年薪：如10-20万、20-30万、30-50万等
- 人才计划：千人计划、青千、优青、杰青等
- 推荐能力：可推荐朋友/无推荐能力
- 申报意愿：强烈/中性/一般

### 4️⃣ 备注提取标签
从联系人备注"{contact_name}"中提取：
- 地理位置、学术头衔、人才项目等

## 输出格式要求
请严格按照以下格式输出：

```
=== 候选人标签总结 ===

【判断备注】活跃,回复及时,{contact_name}
【人工检索】博士,5年经验,知名企业
【AI对话】Java,后端开发,全职,30-50万,意愿强烈,可推荐朋友
【备注提取】英国,教授,青千

【综合标签】活跃,回复及时,博士,5年经验,Java,后端开发,全职,30-50万,意愿强烈,英国,教授,青千
```

要求：
- 每个分类最多5个标签
- 标签简洁明确，2-6个字
- 只提取确实能从内容中得出的标签
- 如果某分类无信息，标注"无相关信息"
"""

        client = init_doubao_client()
        if not client:
            print("❌ 豆包客户端初始化失败")
            return None

        print(f"🤖 发送AI标签生成请求")

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=800,
            temperature=0.3,
        )

        ai_result = response.choices[0].message.content.strip()
        print(f"🤖 AI返回标签结果")

        # 解析AI返回的标签
        parsed_tags = parse_ai_tags_result(ai_result)

        if parsed_tags:
            print(f"✅ AI标签解析成功")
            return parsed_tags
        else:
            print("❌ AI标签解析失败")
            return None

    except Exception as e:
        print(f"❌ AI标签生成异常: {e}")
        return None

def generate_fallback_tags(contact_info, messages):
    """备用标签生成方案（当AI不可用时）"""
    contact_name = contact_info.get('remark', '未知联系人')

    # 简单的备用标签
    judgment_tags = ['中等活跃', '有回复', contact_name]
    manual_tags = ['待人工检索']
    ai_tags = ['待AI分析']
    remark_tags = extract_basic_nickname_tags(contact_name)

    comprehensive_tags = judgment_tags[:2] + ai_tags[:1] + remark_tags[:3]

    return {
        'judgment': judgment_tags,
        'manual': manual_tags,
        'ai': ai_tags,
        'remark': remark_tags,
        'comprehensive': comprehensive_tags
    }

# 旧的关键词匹配函数已删除，现在使用AI生成标签

def create_tag_generation_prompt(processed_data):
    """创建标签生成的提示词"""
    contact_name = processed_data['contact_name']
    text_messages = processed_data['text_messages']
    message_stats = processed_data['message_stats']
    image_analysis = processed_data['image_analysis']
    document_analysis = processed_data['document_analysis']
    voice_transcripts = processed_data['voice_transcripts']

    # 构建聊天内容样本
    chat_content_sample = text_messages[:15] if text_messages else ["无文本消息"]

    # 构建图片分析内容
    image_content = ""
    if image_analysis:
        image_content = "\n".join([f"图片{i+1}: {img['analysis']}" for i, img in enumerate(image_analysis)])

    # 构建文档分析内容
    document_content = ""
    if document_analysis:
        document_content = "\n".join([f"文档{i+1}({doc['filename']}): {doc['content'][:200]}..." for i, doc in enumerate(document_analysis)])

    # 构建语音转文字内容
    voice_content = ""
    if voice_transcripts:
        voice_content = "\n".join([f"语音{i+1}: {voice['transcript']}" for i, voice in enumerate(voice_transcripts)])

    prompt = f"""
你是一个专业的猎头标签分析师，请根据以下微信聊天记录和附件内容，为候选人生成分类标签。

## 候选人信息
- 联系人：{contact_name}
- 消息总数：{processed_data['total_messages']}条
- 文本消息：{processed_data['text_count']}条
- 图片消息：{message_stats['image']}条
- 语音消息：{message_stats['voice']}条
- 文件消息：{message_stats['file']}条

## 聊天内容样本
{chr(10).join(chat_content_sample)}

## 附件内容分析
### 图片内容
{image_content if image_content else "无图片内容"}

### 文档内容
{document_content if document_content else "无文档内容"}

### 语音内容
{voice_content if voice_content else "无语音内容"}

## 标签提取任务
请按照以下四个分类提取候选人标签：

### 1️⃣ 判断备注标签
基于聊天行为和基础信息：
- 活跃状态：活跃/不活跃
- 回复及时性：及时/一般/不及时
- 联系人：{contact_name}

### 2️⃣ 人工检索标签
基于文档和简历内容：
- 教育背景、工作经验、技能证书等

### 3️⃣ AI对话标签
基于聊天内容分析：
- 专业技能：如Java、Python、前端、后端、AI等
- 就业类型：全职/兼职
- 期望年薪：如10-20万、20-30万、30-50万等
- 人才计划：千人计划、青千、优青、杰青等
- 推荐能力：可推荐朋友/无推荐能力
- 申报意愿：强烈/中性/一般

### 4️⃣ 备注提取标签
从联系人备注"{contact_name}"中提取：
- 地理位置、学术头衔、人才项目等

## 输出格式要求
请严格按照以下格式输出：

```
=== 候选人标签总结 ===

【判断备注】活跃,回复及时,{contact_name}
【人工检索】博士,5年经验,知名企业
【AI对话】Java,后端开发,全职,30-50万,意愿强烈,可推荐朋友
【备注提取】英国,教授,青千

【综合标签】活跃,回复及时,博士,5年经验,Java,后端开发,全职,30-50万,意愿强烈,英国,教授,青千
```

要求：
- 每个分类最多5个标签
- 标签简洁明确，2-6个字
- 只提取确实能从内容中得出的标签
- 如果某分类无信息，标注"无相关信息"
"""

    return prompt

def generate_mock_tags_result(processed_data):
    """生成模拟标签结果（当AI不可用时）"""
    contact_name = processed_data['contact_name']

    # 简单的模拟标签
    mock_result = {
        'judgment': ['活跃', '回复及时', contact_name],
        'manual': ['无相关信息'],
        'ai': ['技术背景', '全职', '意愿中性'],
        'remark': extract_basic_nickname_tags(contact_name),
        'comprehensive': ['活跃', '回复及时', '技术背景', '全职', '意愿中性'] + extract_basic_nickname_tags(contact_name)[:3]
    }

    return mock_result

def parse_ai_tags_result(ai_result):
    """解析AI返回的标签结果"""
    try:
        tags_result = {
            'judgment': [],
            'manual': [],
            'ai': [],
            'remark': [],
            'comprehensive': []
        }

        # 查找标签总结部分
        tag_summary_match = re.search(r'=== 候选人标签总结 ===(.*?)```', ai_result, re.DOTALL)
        if not tag_summary_match:
            print('⚠️ 未找到标签总结格式')
            return None

        tag_summary_text = tag_summary_match.group(1)

        # 解析各类标签
        patterns = {
            'judgment': r'【判断备注】([^【\n]+)',
            'manual': r'【人工检索】([^【\n]+)',
            'ai': r'【AI对话】([^【\n]+)',
            'remark': r'【备注提取】([^【\n]+)',
            'comprehensive': r'【综合标签】([^【\n]+)'
        }

        for key, pattern in patterns.items():
            match = re.search(pattern, tag_summary_text)
            if match:
                tag_string = match.group(1).strip()
                if tag_string and tag_string != '无相关信息':
                    tags_result[key] = [tag.strip() for tag in tag_string.split(',') if tag.strip()]

        print(f"✅ 成功解析AI标签结果")
        return tags_result

    except Exception as e:
        print(f"❌ 解析AI标签结果失败: {e}")
        return None

def analyze_chat_with_processed_data(processed_data):
    """使用预处理数据进行智能分析（不包含标签生成）"""
    try:
        print(f"🧠 开始智能分析（使用预处理数据）")

        if not DOUBAO_AVAILABLE:
            print("⚠️ 豆包AI不可用，返回模拟分析")
            return generate_mock_analysis_from_processed_data(processed_data)

        client = init_doubao_client()
        if not client:
            print("❌ 豆包客户端初始化失败")
            return {"status": "error", "error": "AI服务不可用"}

        # 构建智能分析的提示词（不包含标签生成）
        prompt = create_analysis_prompt_without_tags(processed_data)

        print(f"🤖 发送智能分析请求到豆包AI")

        # 构建消息内容
        message_content = [{"type": "text", "text": prompt}]

        # 如果有图片，添加图片内容
        for img_analysis in processed_data['image_analysis']:
            message_content.append({
                "type": "text",
                "text": f"\n图片分析结果: {img_analysis['analysis']}"
            })

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": message_content}
            ],
            max_tokens=2000,
            temperature=0.7,
        )

        analysis_text = response.choices[0].message.content.strip()
        print(f"✅ 智能分析完成")

        return {
            "status": "success",
            "analysis": analysis_text,
            "stats": {
                "total_messages": processed_data['total_messages'],
                "text_count": processed_data['text_count'],
                "contact_name": processed_data['contact_name'],
                "image_count": len(processed_data['image_analysis']),
                "document_count": len(processed_data['document_analysis']),
                "voice_count": len(processed_data['voice_transcripts'])
            }
        }

    except Exception as e:
        print(f"❌ 智能分析失败: {e}")
        return {"status": "error", "error": str(e)}

def create_analysis_prompt_without_tags(processed_data):
    """创建智能分析提示词（不包含标签生成）"""
    contact_name = processed_data['contact_name']
    text_messages = processed_data['text_messages']
    message_stats = processed_data['message_stats']
    image_analysis = processed_data['image_analysis']
    document_analysis = processed_data['document_analysis']
    voice_transcripts = processed_data['voice_transcripts']

    # 构建聊天内容样本
    chat_content_sample = text_messages[:20] if text_messages else ["无文本消息"]

    prompt = f"""
你是一个专业的猎头心理分析师，请深入分析以下与人才候选人的微信聊天记录。分析角度：你是猎头，对方({contact_name})是人才候选人。请重点关注候选人的心理状态、求职意愿、职业想法和潜在需求。

## 聊天基本信息
- 对话对象：{contact_name}
- 消息总数：{processed_data['total_messages']}条
- 文本消息：{processed_data['text_count']}条
- 图片消息：{message_stats['image']}条
- 语音消息：{message_stats['voice']}条
- 文件消息：{message_stats['file']}条

## 聊天内容样本
{chr(10).join(chat_content_sample)}

## 附件分析情况
{'✅ 本次分析包含 ' + str(len(image_analysis)) + ' 张图片内容（已通过AI视觉分析）' if image_analysis else '❌ 本次分析不包含图片内容'}
{'✅ 本次分析包含 ' + str(len(document_analysis)) + ' 个文档内容（已提取文本内容进行分析）' if document_analysis else '❌ 本次分析不包含文档内容'}
{'✅ 本次分析包含 ' + str(len(voice_transcripts)) + ' 个语音消息（已转换为文字分析）' if voice_transcripts else '❌ 本次分析不包含语音内容'}

## 请重点从以下猎头-人才关系维度进行深度分析：

### 1. 候选人心理状态分析
- **求职心态**: 分析候选人当前的求职积极性和紧迫感
- **职业焦虑**: 识别候选人对当前工作或职业发展的担忧
- **情绪波动**: 观察候选人在讨论职业话题时的情绪变化

### 2. 求职意愿解读
- **跳槽动机**: 分析候选人真实的离职原因和跳槽动机
- **职位期望**: 理解候选人对新职位的真实期望和要求
- **薪资敏感度**: 评估候选人对薪资待遇的关注程度和底线

### 3. 职业规划预测
- **短期目标**: 推测候选人近期的职业发展计划
- **长期愿景**: 分析候选人的职业发展长远目标
- **技能提升**: 识别候选人希望在新职位中获得的能力提升

### 4. 沟通配合度分析
- **响应积极性**: 评估候选人对猎头沟通的配合程度
- **信息透明度**: 分析候选人分享信息的开放程度
- **专业态度**: 观察候选人在职业讨论中的专业表现

### 5. 猎头策略建议
- **沟通策略**: 建议与该候选人最有效的沟通方式
- **职位匹配**: 分析什么类型的职位最适合该候选人
- **说服要点**: 指出在推荐职位时应该重点强调的方面

请用中文回答，分析要深入细致，重点关注从猎头角度评估候选人的职业素养和个人特质。如果有图片、文档或语音内容，请结合这些内容进行更深入的候选人评估。

**注意**：请在每个分析维度中明确说明是基于聊天文本、图片内容、文档内容还是语音内容得出的结论。
"""

    return prompt

def generate_mock_analysis_from_processed_data(processed_data):
    """从预处理数据生成模拟分析结果"""
    contact_name = processed_data['contact_name']

    mock_analysis = f"""
# 候选人聊天记录分析报告

## 📊 分析内容概览
- **聊天文本分析**：✅ 已分析（{processed_data['text_count']}条文本消息）
- **图片内容分析**：{'✅ 已分析（' + str(len(processed_data['image_analysis'])) + '张图片）' if processed_data['image_analysis'] else '❌ 无图片内容'}
- **文档内容分析**：{'✅ 已分析（' + str(len(processed_data['document_analysis'])) + '个文档）' if processed_data['document_analysis'] else '❌ 无文档内容'}
- **语音内容分析**：{'✅ 已分析（' + str(len(processed_data['voice_transcripts'])) + '个语音）' if processed_data['voice_transcripts'] else '❌ 无语音内容'}
- **分析对象**：{contact_name}
- **消息总数**：{processed_data['total_messages']}条

## 📋 详细分析内容

### 1. 候选人心理状态分析
通过分析与候选人 {contact_name} 的聊天记录，发现以下职业心理特征：
- **求职心态**: 候选人表现出积极但谨慎的求职态度，对新机会有兴趣但不急躁
- **职业焦虑**: 对当前工作环境有一定不满，希望寻求更好的发展机会
- **沟通风格**: 表达清晰，回复及时，展现出良好的职业素养

### 2. 求职意愿解读
- **跳槽动机**: 主要出于职业发展考虑，希望获得更大的平台和挑战
- **薪资期望**: 期望薪资水平在合理范围内，更注重长期发展前景
- **时间安排**: 具有一定的紧迫性，但愿意等待合适的机会

### 3. 专业能力评估
- **技术水平**: 具备扎实的专业基础，有相关工作经验
- **沟通能力**: 表达清晰，逻辑性强，适合团队协作
- **学习态度**: 对新技术和新领域保持开放态度

### 4. 合作建议
- **沟通策略**: 建议采用专业但友好的沟通方式
- **职位匹配**: 适合中高级专业岗位
- **跟进方式**: 保持定期联系，及时反馈职位信息

*本分析基于预处理的聊天记录内容，仅供参考。建议结合面试和其他评估方式综合判断。*
"""

    return {
        "status": "success",
        "analysis": mock_analysis.strip(),
        "stats": {
            "total_messages": processed_data['total_messages'],
            "text_count": processed_data['text_count'],
            "contact_name": contact_name,
            "image_count": len(processed_data['image_analysis']),
            "document_count": len(processed_data['document_analysis']),
            "voice_count": len(processed_data['voice_transcripts'])
        }
    }

def extract_tags_from_analysis_report(analysis_report):
    """使用AI从分析报告中提取标签"""
    if not DOUBAO_AVAILABLE:
        print("⚠️ 豆包AI不可用，返回模拟标签")
        return generate_mock_tags_from_report(analysis_report)

    client = init_doubao_client()
    if not client:
        print("❌ 豆包客户端初始化失败")
        return []

    try:
        # 使用简化版提示词
        prompt = f"""请从这个分析报告中找出候选人的关键特征标签：

报告内容：
{analysis_report[:1200]}

请提取：技能、工作状态、地点、薪资、意愿等关键标签，用逗号分隔。"""

        print(f"🤖 发送标签提取请求到豆包AI")

        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,
        )

        ai_result = response.choices[0].message.content.strip()
        print(f"🤖 AI返回结果: {ai_result}")

        if ai_result and ai_result != "无相关标签":
            # 解析AI返回的标签
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            # 过滤掉无效标签
            valid_tags = [tag for tag in tags if len(tag) > 0 and tag not in ['无', '无标签', '暂无']]
            return valid_tags[:8]  # 最多返回8个标签

        return []

    except Exception as e:
        print(f"❌ AI标签提取失败: {e}")
        return []

def generate_mock_tags_from_report(analysis_report):
    """从分析报告中生成模拟标签（当AI不可用时）"""
    tags = []
    report_lower = analysis_report.lower()

    # 技能标签
    skill_keywords = {
        "Java": ["java"],
        "Python": ["python"],
        "前端": ["前端", "frontend"],
        "后端": ["后端", "backend"],
        "AI": ["人工智能", "ai", "机器学习"],
        "产品": ["产品经理", "产品"],
        "设计": ["设计", "ui", "ux"]
    }

    for skill, keywords in skill_keywords.items():
        if any(keyword in report_lower for keyword in keywords):
            tags.append(skill)

    # 工作状态标签
    if any(keyword in report_lower for keyword in ['主动', '积极', '求职']):
        tags.append("主动求职")
    elif any(keyword in report_lower for keyword in ['被动', '观望']):
        tags.append("被动求职")

    # 薪资标签
    if any(keyword in report_lower for keyword in ['30万', '30-40万', '35万']):
        tags.append("30-50万")
    elif any(keyword in report_lower for keyword in ['20万', '25万']):
        tags.append("20-30万")
    elif any(keyword in report_lower for keyword in ['50万', '60万']):
        tags.append("50万+")

    # 意愿标签
    if any(keyword in report_lower for keyword in ['强烈', '迫切', '很想']):
        tags.append("意愿强烈")
    elif any(keyword in report_lower for keyword in ['谨慎', '考虑']):
        tags.append("意愿中性")

    # 个人特质标签
    if any(keyword in report_lower for keyword in ['沟通', '表达清晰']):
        tags.append("沟通良好")
    if any(keyword in report_lower for keyword in ['技术', '扎实', '专业']):
        tags.append("技术扎实")
    if any(keyword in report_lower for keyword in ['潜力', '发展']):
        tags.append("有潜力")

    return tags[:6]  # 返回最多6个标签

@app.route("/api/analyze_chat/<contact_id>", methods=['POST'])
def analyze_chat_api(contact_id):
    """智能分析API（使用缓存的预处理数据）"""
    try:
        print(f"🧠 开始智能分析 - 联系人ID: {contact_id}")

        # 首先尝试获取缓存的预处理数据
        processed_data = get_cached_processed_data(contact_id)

        if not processed_data:
            print(f"⚠️ 未找到缓存数据，执行完整处理")
            # 如果没有缓存数据，获取原始聊天数据
            chat_data = request.get_json()
            if not chat_data:
                return jsonify({
                    "status": "error",
                    "message": "未提供聊天数据且无缓存数据"
                }), 400

            # 执行完整的预处理
            processed_data = preprocess_chat_data(chat_data)
            if not processed_data:
                return jsonify({
                    "status": "error",
                    "message": "数据预处理失败"
                }), 500
        else:
            print(f"✅ 使用缓存的预处理数据")

        print(f"📊 数据概览: 文本{processed_data['text_count']}条, 图片{len(processed_data['image_analysis'])}张, 文档{len(processed_data['document_analysis'])}个")

        # 执行智能分析（不包含标签生成）
        analysis_result = analyze_chat_with_processed_data(processed_data)

        if analysis_result and analysis_result.get('status') == 'success':
            print(f"✅ 智能分析成功")
            return jsonify({
                "status": "success",
                "data": analysis_result
            })
        else:
            return jsonify({
                "status": "error",
                "message": "智能分析失败"
            }), 500

    except Exception as e:
        print(f"❌ 智能分析API错误: {e}")
        return jsonify({
            "status": "error",
            "message": f"服务器错误: {str(e)}"
        }), 500

@app.route("/api/generate_message", methods=['POST'])
def generate_message_api():
    """AI消息生成API"""
    try:
        # 获取请求数据
        data = request.get_json()

        if not data:
            return jsonify({
                "status": "error",
                "message": "未收到请求数据"
            }), 400

        # 验证必需参数
        user_input = data.get('user_input', '').strip()
        receiver = data.get('receiver', '').strip()

        if not user_input:
            return jsonify({
                "status": "error",
                "message": "缺少用户输入内容 (user_input)"
            }), 400

        print(f"🤖 开始AI消息生成")
        print(f"👤 接收人: {receiver}")
        print(f"💭 用户意图: {user_input[:100]}{'...' if len(user_input) > 100 else ''}")

        # 调用AI生成消息
        generated_message = generate_wechat_message_with_ai(user_input, receiver)

        if generated_message:
            print(f"✅ AI消息生成成功")
            print(f"📝 生成内容: {generated_message[:100]}{'...' if len(generated_message) > 100 else ''}")

            return jsonify({
                "status": "success",
                "generated_message": generated_message,
                "user_input": user_input,
                "receiver": receiver
            })
        else:
            return jsonify({
                "status": "error",
                "message": "AI消息生成失败，请稍后重试"
            }), 500

    except Exception as e:
        print(f"❌ AI消息生成API错误: {e}")
        return jsonify({
            "status": "error",
            "message": f"服务器错误: {str(e)}"
        }), 500

@app.route("/api/generate_follow_up_messages", methods=["POST"])
def generate_follow_up_messages_api():
    """生成跟进消息API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "请求数据为空"
            }), 400

        # 获取必要参数
        analysis_result = data.get('analysis_result', '')
        candidate_tags = data.get('candidate_tags', {})
        candidate_name = data.get('candidate_name', '')

        print(f"🤖 开始生成跟进消息")
        print(f"👤 候选人: {candidate_name}")
        print(f"📊 分析结果长度: {len(str(analysis_result))} 字符")
        print(f"🏷️ 标签数量: {len(candidate_tags) if isinstance(candidate_tags, dict) else 0}")

        # 调用AI生成跟进消息
        result = generate_follow_up_messages_with_ai(analysis_result, candidate_tags, candidate_name)

        if result and result.get('status') == 'success':
            print(f"✅ 跟进消息生成成功")
            print(f"📝 候选人资格: {result.get('qualification', '未知')}")
            print(f"🏷️ 专业标签数: {len(result.get('professional_tags', []))}")
            print(f"💬 跟进消息数: {len(result.get('follow_up_messages', {}))}")

            return jsonify({
                "status": "success",
                "data": result
            })
        else:
            return jsonify({
                "status": "error",
                "message": "跟进消息生成失败",
                "details": result.get('error', '未知错误') if result else '生成结果为空'
            }), 500

    except Exception as e:
        print(f"❌ 跟进消息生成API错误: {e}")
        return jsonify({
            "status": "error",
            "message": f"服务器错误: {str(e)}"
        }), 500

@app.route("/report_ip", methods=["POST"])
def report_ip():
    """接收IP地址变化报告的接口"""
    try:
        # 获取请求数据
        data = request.get_json()

        if not data:
            return jsonify({
                "status": "error",
                "message": "未收到请求数据"
            }), 400

        # 获取必要参数
        username = data.get('username', '').strip()
        ip_address = data.get('ip', '').strip()

        if not username:
            return jsonify({
                "status": "error",
                "message": "缺少用户名参数 (username)"
            }), 400

        if not ip_address:
            return jsonify({
                "status": "error",
                "message": "缺少IP地址参数 (ip)"
            }), 400

        # 获取客户端信息
        client_ip = request.remote_addr
        user_agent = request.headers.get('User-Agent', '')
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        print(f"📡 收到IP地址变化报告")
        print(f"👤 用户名: {username}")
        print(f"🌐 新IP地址: {ip_address}")
        print(f"📍 客户端IP: {client_ip}")
        print(f"🕒 时间: {timestamp}")

        # 保存IP变化记录到文件
        ip_record = {
            "username": username,
            "ip_address": ip_address,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "timestamp": timestamp,
            "report_time": datetime.now().isoformat()
        }

        # 保存到JSON文件
        save_ip_change_record(ip_record)

        # 更新用户文件夹下的JSON文件名（基准数据）
        update_user_json_filename(username, ip_address)

        # 可以在这里添加其他处理逻辑，比如：
        # - 发送通知
        # - 更新数据库
        # - 触发其他自动化流程

        print(f"✅ IP地址变化记录已保存")

        return jsonify({
            "status": "success",
            "message": "IP地址变化报告接收成功",
            "data": {
                "username": username,
                "ip_address": ip_address,
                "timestamp": timestamp
            }
        })

    except Exception as e:
        print(f"❌ IP地址报告接收失败: {e}")
        return jsonify({
            "status": "error",
            "message": f"服务器错误: {str(e)}"
        }), 500

@app.route("/api/ip_history", methods=["GET"])
def get_ip_history():
    """获取IP地址变化历史记录API"""
    try:
        # 获取查询参数
        username = request.args.get('username', '').strip()
        limit = int(request.args.get('limit', 50))

        # 限制查询数量
        if limit > 200:
            limit = 200

        print(f"📊 查询IP变化历史")
        print(f"👤 用户名过滤: {username if username else '全部用户'}")
        print(f"📄 查询数量: {limit}")

        # 获取历史记录
        history = get_ip_change_history(username, limit)

        print(f"✅ 找到 {len(history)} 条IP变化记录")

        return jsonify({
            "status": "success",
            "data": {
                "total_count": len(history),
                "username_filter": username,
                "limit": limit,
                "records": history
            }
        })

    except Exception as e:
        print(f"❌ 获取IP历史记录失败: {e}")
        return jsonify({
            "status": "error",
            "message": f"服务器错误: {str(e)}"
        }), 500

@app.route("/ip_monitor")
def ip_monitor_page():
    """IP地址监控页面"""
    print("DEBUG: 访问IP地址监控页面")
    return render_template("ip_monitor.html")

@app.route("/api/send_wechat_message", methods=['POST'])
def send_wechat_message():
    """发送微信消息API"""
    try:
        # 获取请求数据
        data = request.get_json()

        if not data:
            return jsonify({
                "status": "error",
                "message": "未收到请求数据"
            }), 400

        # 验证必需参数
        receiver = data.get('receiver')
        message = data.get('msg')
        target_host = data.get('target_host', 'localhost:8000')  # 默认目标主机

        if not receiver:
            return jsonify({
                "status": "error",
                "message": "缺少接收人参数 (receiver)"
            }), 400

        if not message:
            return jsonify({
                "status": "error",
                "message": "缺少消息内容参数 (msg)"
            }), 400

        print(f"📤 准备发送微信消息")
        print(f"🎯 目标主机: {target_host}")
        print(f"👤 接收人: {receiver}")
        print(f"💬 消息内容: {message[:50]}{'...' if len(message) > 50 else ''}")

        # 构建发送请求的URL
        send_url = f"http://{target_host}/api/sendMsg"

        # 构建发送数据
        send_data = {
            "receiver": receiver,
            "msg": message
        }

        # 发送请求到目标微信API
        try:
            response = requests.post(
                send_url,
                json=send_data,
                timeout=30,
                headers={'Content-Type': 'application/json'}
            )

            print(f"📡 发送请求到: {send_url}")
            print(f"📊 响应状态码: {response.status_code}")

            # 检查响应状态
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"✅ 消息发送成功: {response_data}")

                    return jsonify({
                        "status": "success",
                        "message": "消息发送成功",
                        "data": {
                            "receiver": receiver,
                            "msg": message,
                            "target_host": target_host,
                            "response": response_data
                        }
                    })
                except ValueError:
                    # 响应不是JSON格式
                    response_text = response.text
                    print(f"✅ 消息发送成功 (非JSON响应): {response_text}")

                    return jsonify({
                        "status": "success",
                        "message": "消息发送成功",
                        "data": {
                            "receiver": receiver,
                            "msg": message,
                            "target_host": target_host,
                            "response": response_text
                        }
                    })
            else:
                # 发送失败
                error_msg = f"发送失败，状态码: {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f", 错误信息: {error_data}"
                except:
                    error_msg += f", 响应内容: {response.text}"

                print(f"❌ {error_msg}")

                return jsonify({
                    "status": "error",
                    "message": error_msg,
                    "data": {
                        "receiver": receiver,
                        "msg": message,
                        "target_host": target_host,
                        "status_code": response.status_code
                    }
                }), 500

        except requests.exceptions.Timeout:
            error_msg = f"请求超时，无法连接到 {target_host}"
            print(f"⏰ {error_msg}")
            return jsonify({
                "status": "error",
                "message": error_msg
            }), 408

        except requests.exceptions.ConnectionError:
            error_msg = f"连接失败，无法连接到 {target_host}"
            print(f"🔌 {error_msg}")
            return jsonify({
                "status": "error",
                "message": error_msg
            }), 503

        except Exception as req_error:
            error_msg = f"请求发送失败: {str(req_error)}"
            print(f"❌ {error_msg}")
            return jsonify({
                "status": "error",
                "message": error_msg
            }), 500

    except Exception as e:
        print(f"❌ 发送微信消息API错误: {e}")
        return jsonify({
            "status": "error",
            "message": f"服务器错误: {str(e)}"
        }), 500

@app.route("/")
def index():
    print("DEBUG: 访问首页")  # 调试信息
    return render_template("index.html")

@app.route("/send_message")
def send_message_page():
    """微信消息发送页面"""
    print("DEBUG: 访问微信消息发送页面")
    return render_template("send_message.html")

@app.route("/candidate_tags")
def candidate_tags_page():
    """候选人标签分析页面"""
    print("DEBUG: 访问候选人标签分析页面")
    return render_template("candidate_tags.html")

@app.route("/upload_contacts", methods=["POST"])
def upload_contacts():
    print(f"🚀🚀🚀 [UPLOAD_CONTACTS] 开始处理请求")
    print(f"🌐 客户端IP: {request.remote_addr}")
    print(f"📝 请求方法: {request.method}")
    print(f"📋 Content-Type: {request.content_type}")

    data = request.json
    client_id = data.get("client_id") if data else None
    contacts = data.get("contacts", []) if data else []

    print(f"🔍 upload_contacts 接收到请求 - client_id: {client_id}")
    print(f"📊 联系人数量: {len(contacts) if contacts else 0}")

    if not client_id or not isinstance(contacts, list):
        print(f"❌ 参数错误 - client_id: {client_id}, contacts类型: {type(contacts)}")
        return jsonify({"status": "error", "message": "参数错误"}), 400

    try:
        print(f"🔍 调用 save_contacts...")
        save_contacts(client_id, contacts)
        print(f"🔍 save_contacts 完成")
        print(f"✅ 成功处理 {len(contacts)} 个联系人")
        return jsonify({"status": "success", "count": len(contacts)})
    except Exception as e:
        print(f"❌ save_contacts 失败: {str(e)}")
        import traceback
        print(f"📍 错误详情: {traceback.format_exc()}")
        return jsonify({"status": "error", "message": str(e)}), 500

@app.route("/fetch_and_store", methods=["GET"])
def fetch_and_store():
    client_ip = request.remote_addr
    print(f"DEBUG: 客户端IP = {client_ip}")  # 调试信息

    # 从客户端的MCP服务获取联系人信息
    mcp_url = f"http://{client_ip}:5030/api/v1/contact"
    print(f"DEBUG: MCP URL = {mcp_url}")  # 调试信息

    try:
        # Step 1: 从 MCP 获取联系人（CSV）
        print("DEBUG: 开始请求MCP服务...")  # 调试信息
        response = requests.get(mcp_url, timeout=10)
        response.raise_for_status()
        content = response.text.replace('\r\n', '\n').strip()
        print(f"DEBUG: 获取到内容长度 = {len(content)}")  # 调试信息

        # Step 2: CSV → JSON 解析
        try:
            f = io.StringIO(content, newline='')
            reader = csv.DictReader(f)
            contacts = [row for row in reader]
        except Exception as parse_err:
            return jsonify({
                "status": "error",
                "message": f"CSV解析失败: {str(parse_err)}",
                "raw": content[:300]
            })

        # Step 3: 提取当前用户昵称
        if not contacts:
            return jsonify({"status": "error", "message": "联系人为空"})

        def get_current_user_nickname(contacts):
            """从联系人列表中获取当前用户的昵称"""
            # 方法1: 尝试从特殊的系统联系人中推断
            # 通常文件传输助手等系统联系人可以帮助识别当前用户

            # 方法2: 使用一个简单的启发式方法
            # 由于无法直接获取当前用户信息，我们使用IP地址+时间戳作为标识
            import time
            timestamp = int(time.time())

            # 尝试从联系人中找到一些有意义的信息
            personal_contacts = []
            for contact in contacts:
                username = contact.get("UserName", "") or ""
                nickname = contact.get("NickName", "") or ""
                remark = contact.get("Remark", "") or ""

                # 跳过群聊（包含@chatroom）
                if "@chatroom" in username:
                    continue

                # 跳过一些明显的系统账号
                system_accounts = [
                    "filehelper", "fmessage", "medianote", "floatbottle",
                    "weixin", "weixinreminder", "weixinguanhaozhushou"
                ]
                if username.lower() in system_accounts:
                    continue

                # 跳过公众号等（通常以gh_开头或包含特定关键词）
                if username.startswith("gh_") or (nickname and "公众" in nickname):
                    continue

                # 收集有备注或昵称的个人联系人
                if (remark and remark.strip()) or (nickname and nickname.strip()):
                    personal_contacts.append(contact)

            # 如果有个人联系人，尝试找到一个合适的名字
            if personal_contacts:
                # 优先选择有备注的联系人的备注作为参考
                for contact in personal_contacts[:5]:  # 只检查前5个
                    remark = contact.get("Remark", "") or ""
                    remark = remark.strip() if remark else ""
                    if remark and len(remark) <= 10:  # 合理长度的备注
                        return f"用户_{remark}"

                # 如果没有合适的备注，使用昵称
                for contact in personal_contacts[:5]:
                    nickname = contact.get("NickName", "") or ""
                    nickname = nickname.strip() if nickname else ""
                    if nickname and len(nickname) <= 10:
                        return f"用户_{nickname}"

            # 如果都没有找到，使用时间戳
            return f"用户_{timestamp}"

        user_nickname = get_current_user_nickname(contacts)
        print(f"DEBUG: user_nickname = {user_nickname}")  # 调试信息

        # 清理昵称中的特殊字符，避免文件名问题
        clean_nickname = "".join(c for c in user_nickname if c.isalnum() or c in "._-")
        client_id = f"{client_ip.replace('.', '_')}_{clean_nickname}"
        print(f"DEBUG: client_id = {client_id}")  # 调试信息

        # Step 4: 上传联系人到 /upload_contacts 路由（内部调用）
        try:
            upload_response = requests.post(
                "http://127.0.0.1:8080/upload_contacts",
                json={"client_id": client_id, "contacts": contacts},
                timeout=5
            )
            upload_result = upload_response.json()
        except Exception as e:
            return jsonify({"status": "partial", "message": f"上传失败: {str(e)}"})

        return jsonify({
            "status": "success",
            "client_id": client_id,
            "user_nickname": user_nickname,
            "client_ip": client_ip,
            "count": len(contacts),
            "upload_result": upload_result
        })

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

# 聊天记录相关路由
@app.route("/chatlog")
def chatlog_page():
    """聊天记录查看页面"""
    return render_template("chatlog.html")

@app.route("/proxy_file")
def proxy_file():
    """代理文件访问，解决跨域问题"""
    file_url = request.args.get('url')
    if not file_url:
        return jsonify({"error": "缺少URL参数"}), 400

    try:
        print(f"🔗 代理访问文件: {file_url}")

        # 发送请求获取文件
        response = requests.get(file_url, timeout=30, stream=True)
        response.raise_for_status()

        # 获取文件类型
        content_type = response.headers.get('content-type', 'application/octet-stream')

        # 创建响应
        def generate():
            for chunk in response.iter_content(chunk_size=8192):
                yield chunk

        flask_response = Response(generate(), content_type=content_type)

        # 添加必要的头部
        flask_response.headers['Access-Control-Allow-Origin'] = '*'
        flask_response.headers['Access-Control-Allow-Methods'] = 'GET'
        flask_response.headers['Access-Control-Allow-Headers'] = '*'

        return flask_response

    except Exception as e:
        print(f"❌ 代理文件访问失败: {str(e)}")
        return jsonify({"error": f"文件访问失败: {str(e)}"}), 500

@app.route("/api/get_chatlog_by_id/<contact_id>")
def get_chatlog_by_id(contact_id):
    """根据ID获取聊天记录API"""
    print(f"💬💬💬 [GET_CHATLOG] 开始获取聊天记录")
    print(f"🆔 联系人ID: {contact_id}")
    print(f"🌐 客户端IP: {request.remote_addr}")

    try:
        chatlog_data = fetch_chatlog_by_id(contact_id)
        if chatlog_data:
            print(f"✅ 成功获取聊天记录")
            print(f"📊 消息数量: {chatlog_data.get('text_count', 0)}")
            print(f"👤 联系人: {chatlog_data.get('contact_info', {}).get('remark', 'Unknown')}")
            return jsonify({
                "status": "success",
                "data": chatlog_data
            })
        else:
            print(f"❌ 未找到ID为 {contact_id} 的联系人")
            return jsonify({
                "status": "error",
                "message": f"未找到ID为 {contact_id} 的联系人"
            })
    except Exception as e:
        print(f"💥💥💥 [GET_CHATLOG] 发生错误: {str(e)}")
        import traceback
        print(f"📍 错误详情: {traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": str(e)
        })

@app.route("/api/search_contacts")
def search_contacts():
    """搜索有备注ID的联系人"""
    print(f"🔍🔍🔍 [SEARCH_CONTACTS] 开始搜索联系人")
    print(f"🌐 客户端IP: {request.remote_addr}")

    try:
        contacts = get_remarked_contacts()
        print(f"✅ 成功获取联系人列表")
        print(f"📊 联系人总数: {len(contacts)}")

        # 按用户统计
        user_stats = {}
        for contact in contacts:
            user = contact.get('user', 'Unknown')
            user_stats[user] = user_stats.get(user, 0) + 1

        print(f"👥 用户统计: {user_stats}")

        return jsonify({
            "status": "success",
            "contacts": contacts,  # 直接返回原始联系人信息，不生成标签
            "total": len(contacts),
            "user_stats": user_stats
        })
    except Exception as e:
        print(f"💥💥💥 [SEARCH_CONTACTS] 发生错误: {str(e)}")
        import traceback
        print(f"📍 错误详情: {traceback.format_exc()}")
        return jsonify({
            "status": "error",
            "message": str(e)
        })

def parse_text_chatlog(chatlog_text):
    """解析format=text返回的聊天记录文本"""
    messages = []
    if not chatlog_text or not chatlog_text.strip():
        return messages

    lines = chatlog_text.strip().split('\n')
    current_message = None

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 检查是否是新消息的开始（通常包含时间戳）
        if is_message_header(line):
            # 保存上一条消息
            if current_message:
                messages.append(current_message)

            # 开始新消息
            current_message = parse_message_header(line)
        elif current_message:
            # 继续当前消息的内容
            if 'content' not in current_message:
                current_message['content'] = line
            else:
                current_message['content'] += '\n' + line

    # 保存最后一条消息
    if current_message:
        messages.append(current_message)

    # 处理附件消息
    processed_messages = []
    for msg in messages:
        content = msg.get('content', '')
        if content and is_attachment_message(content):
            # 这是附件消息，解析附件信息
            attachment_info = parse_attachment_message(content)
            # 合并消息信息和附件信息
            msg.update(attachment_info)
        processed_messages.append(msg)

    return processed_messages

def is_attachment_message(content):
    """判断是否是附件消息"""
    if not content:
        return False

    content_lower = content.lower()

    # 检查是否包含附件关键词
    attachment_keywords = [
        'http://', 'https://', 'file://',  # URL
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',  # 图片
        '.mp3', '.wav', '.m4a', '.aac', '.ogg',  # 音频
        '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv',  # 视频
        '.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx', '.ppt', '.pptx',  # 文档
        '图片', 'image', '照片', 'photo',
        '语音', 'voice', 'audio', '音频',
        '视频', 'video',
        '文件', 'file', '文档', 'document',
        '链接', 'link',
        '位置', 'location', '地图', 'map',
        '表情', 'emoji', 'sticker'
    ]

    return any(keyword in content_lower for keyword in attachment_keywords)

def is_message_header(line):
    """判断是否是消息头部（包含时间戳等信息）"""
    # 常见的消息头部格式：
    # 2024-01-01 12:00:00 张三
    # [2024-01-01 12:00:00] 张三:
    # 2024/01/01 12:00:00 - 张三
    import re

    # 匹配日期时间格式
    datetime_patterns = [
        r'\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}',  # 2024-01-01 12:00:00
        r'\[\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2}\]',  # [2024-01-01 12:00:00]
        r'\d{4}年\d{1,2}月\d{1,2}日\s+\d{1,2}:\d{1,2}:\d{1,2}',  # 2024年1月1日 12:00:00
    ]

    for pattern in datetime_patterns:
        if re.search(pattern, line):
            return True

    return False

def parse_message_header(line):
    """解析消息头部，提取时间、发送者等信息"""
    import re

    message = {
        'type': 'text',
        'time': '',
        'sender': '',
        'isSelf': False,
        'content': ''
    }

    # 尝试提取时间戳
    datetime_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2}\s+\d{1,2}:\d{1,2}:\d{1,2})', line)
    if datetime_match:
        message['time'] = datetime_match.group(1)
        # 移除时间戳后的部分作为发送者
        remaining = line.replace(datetime_match.group(1), '').strip()
        # 移除常见的分隔符
        remaining = re.sub(r'^[-:\s\[\]]+', '', remaining)
        remaining = re.sub(r'[-:\s\[\]]+$', '', remaining)
        message['sender'] = remaining
    else:
        # 如果没有找到时间戳，整行作为发送者
        message['sender'] = line

    return message

def parse_attachment_message(content):
    """解析附件消息"""
    import re

    attachment_info = {
        'type': 'attachment',
        'attachment_type': 'unknown',
        'filename': '',
        'description': content,
        'url': '',
        'size': ''
    }

    # 提取URL链接 - 排除常见的结束字符，但保留冒号用于端口号
    url_pattern = r'https?://[^\s\]\)\,\;\!\?\"\']+|file://[^\s\]\)\,\;\!\?\"\']+|[a-zA-Z]:[\\\/][^\s\]\)\,\;\!\?\"\']+\.[a-zA-Z0-9]+'
    urls = re.findall(url_pattern, content)

    # 清理URL末尾的标点符号
    if urls:
        cleaned_url = urls[0].rstrip('.,;!?"\')]}')
        attachment_info['url'] = cleaned_url

    # 提取文件名
    filename_pattern = r'([^\\\/\s]+\.[a-zA-Z0-9]+)'
    filenames = re.findall(filename_pattern, content)
    if filenames:
        attachment_info['filename'] = filenames[-1]  # 取最后一个文件名

    # 提取文件大小
    size_pattern = r'(\d+(?:\.\d+)?\s*[KMGT]?B)'
    sizes = re.findall(size_pattern, content, re.IGNORECASE)
    if sizes:
        attachment_info['size'] = sizes[0]

    # 检测不同类型的附件
    content_lower = content.lower()
    filename_lower = attachment_info['filename'].lower() if attachment_info['filename'] else ''
    url_lower = attachment_info['url'].lower() if attachment_info['url'] else ''

    # 优先处理[语音](URL)格式 - 这个必须放在最前面！
    if '[语音](' in content and 'voice/' in content:
        attachment_info['attachment_type'] = 'voice'
        attachment_info['description'] = '[语音]'
        # 从内容中提取URL
        import re
        print(f"🔍 尝试从语音消息中提取URL: {content[:100]}...")
        url_match = re.search(r'\[语音\]\((http[^)]+)\)', content)
        if url_match:
            attachment_info['url'] = url_match.group(1)
            print(f"🎤 从内容中提取语音URL: {attachment_info['url']}")
        else:
            print(f"⚠️ 语音URL提取失败，内容: {content}")
    # 根据URL路径识别微信多媒体类型
    elif '/image/' in url_lower:
        attachment_info['attachment_type'] = 'image'
        attachment_info['description'] = '[图片]'
    elif '/video/' in url_lower:
        attachment_info['attachment_type'] = 'video'
        attachment_info['description'] = '[视频]'
    elif '/voice/' in url_lower:
        attachment_info['attachment_type'] = 'voice'
        attachment_info['description'] = '[语音]'
    # 特别处理audio/mp3格式
    elif 'audio/mp3' in url_lower or 'audio/mp3' in content_lower:
        attachment_info['attachment_type'] = 'voice'
        attachment_info['description'] = '[语音]'
    elif '/file/' in url_lower:
        attachment_info['attachment_type'] = 'file'
        attachment_info['description'] = '[文件]'
    elif '/data/' in url_lower:
        # 根据文件扩展名进一步判断
        if any(ext in url_lower for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']):
            attachment_info['attachment_type'] = 'image'
            attachment_info['description'] = '[图片]'
        elif any(ext in url_lower for ext in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']):
            attachment_info['attachment_type'] = 'video'
            attachment_info['description'] = '[视频]'
        elif any(ext in url_lower for ext in ['.mp3', '.wav', '.m4a', '.aac', '.ogg']):
            attachment_info['attachment_type'] = 'voice'
            attachment_info['description'] = '[语音]'
        elif any(ext in url_lower for ext in ['.pdf', '.doc', '.docx', '.txt', '.xls', '.xlsx', '.ppt', '.pptx']):
            attachment_info['attachment_type'] = 'file'
            attachment_info['description'] = '[文件]'
        else:
            attachment_info['attachment_type'] = 'file'
            attachment_info['description'] = '[文件]'

    # 传统关键词检测作为备选
    elif (any(keyword in content_lower for keyword in ['图片', 'image', '照片', 'photo']) or
          any(ext in filename_lower for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'])):
        attachment_info['attachment_type'] = 'image'
        attachment_info['description'] = '[图片]'
    elif (any(keyword in content_lower for keyword in ['语音', 'voice', 'audio', '音频']) or
          any(ext in filename_lower for ext in ['.mp3', '.wav', '.m4a', '.aac', '.ogg'])):
        attachment_info['attachment_type'] = 'voice'
        attachment_info['description'] = '[语音]'
        # 如果是[语音](URL)格式，也要提取URL
        if '[语音](' in content and 'voice/' in content:
            import re
            print(f"🔍 在关键词匹配中尝试提取语音URL: {content[:100]}...")
            url_match = re.search(r'\[语音\]\((http[^)]+)\)', content)
            if url_match:
                attachment_info['url'] = url_match.group(1)
                print(f"🎤 在关键词匹配中提取到语音URL: {attachment_info['url']}")
            else:
                print(f"⚠️ 关键词匹配中语音URL提取失败，内容: {content}")
    elif (any(keyword in content_lower for keyword in ['视频', 'video']) or
          any(ext in filename_lower for ext in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'])):
        attachment_info['attachment_type'] = 'video'
        attachment_info['description'] = '[视频]'
    elif (any(keyword in content_lower for keyword in ['文件', 'file', '文档', 'document']) or
          any(ext in filename_lower for ext in ['.doc', '.docx', '.pdf', '.txt', '.xls', '.xlsx', '.ppt', '.pptx'])):
        attachment_info['attachment_type'] = 'file'
        attachment_info['description'] = '[文件]'
    elif any(keyword in content_lower for keyword in ['链接', 'link', 'http', 'www', 'url']):
        attachment_info['attachment_type'] = 'link'
        attachment_info['description'] = '[链接]'
    elif any(keyword in content_lower for keyword in ['位置', 'location', '地图', 'map']):
        attachment_info['attachment_type'] = 'location'
        attachment_info['description'] = '[位置]'
    elif any(keyword in content_lower for keyword in ['表情', 'emoji', 'sticker']):
        attachment_info['attachment_type'] = 'emoji'
        attachment_info['description'] = '[表情]'

    # 语音通话类型
    elif any(keyword in content_lower for keyword in ['语音通话', '通话时长', '通话', 'voice call', 'call']):
        attachment_info['attachment_type'] = 'voice_call'
        attachment_info['description'] = '[语音通话]'

        # 提取通话时长
        duration = extract_call_duration(content)
        if duration:
            attachment_info['call_duration'] = duration
            attachment_info['description'] = f'[语音通话] {duration}'

    return attachment_info

def extract_call_duration(content):
    """提取语音通话时长"""
    import re

    # 匹配各种时长格式
    duration_patterns = [
        r'通话时长[：:]\s*(\d+分\d+秒|\d+:\d+|\d+分钟\d+秒|\d+秒)',  # 通话时长：2分30秒
        r'时长[：:]\s*(\d+分\d+秒|\d+:\d+|\d+分钟\d+秒|\d+秒)',      # 时长：2:30
        r'(\d+分\d+秒|\d+:\d+|\d+分钟\d+秒)',                      # 直接的时长格式
        r'通话\s*(\d+分\d+秒|\d+:\d+|\d+分钟\d+秒|\d+秒)',          # 通话 2分30秒
        r'语音通话\s*(\d+分\d+秒|\d+:\d+|\d+分钟\d+秒|\d+秒)',      # 语音通话 2分30秒
        r'(\d+)\s*分钟?\s*(\d+)\s*秒',                            # 2分钟30秒 或 2分30秒
        r'(\d+)\s*:\s*(\d+)',                                    # 2:30
        r'(\d+)\s*秒',                                           # 30秒
    ]

    for pattern in duration_patterns:
        match = re.search(pattern, content)
        if match:
            duration_str = match.group(1) if match.lastindex == 1 else match.group(0)
            return format_duration(duration_str)

    return None

def format_duration(duration_str):
    """格式化时长显示"""
    import re

    # 如果已经是标准格式，直接返回
    if re.match(r'\d+分\d+秒', duration_str):
        return duration_str

    # 处理 mm:ss 格式
    if ':' in duration_str:
        parts = duration_str.split(':')
        if len(parts) == 2:
            try:
                minutes = int(parts[0])
                seconds = int(parts[1])
                return f"{minutes}分{seconds}秒"
            except ValueError:
                return duration_str

    # 处理纯秒数
    seconds_match = re.match(r'(\d+)\s*秒', duration_str)
    if seconds_match:
        total_seconds = int(seconds_match.group(1))
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        if minutes > 0:
            return f"{minutes}分{seconds}秒"
        else:
            return f"{seconds}秒"

    # 处理分钟秒数分开的格式
    min_sec_match = re.match(r'(\d+)\s*分钟?\s*(\d+)\s*秒', duration_str)
    if min_sec_match:
        minutes = int(min_sec_match.group(1))
        seconds = int(min_sec_match.group(2))
        return f"{minutes}分{seconds}秒"

    return duration_str

def extract_ip_from_filename(filename):
    """从文件名中提取IP地址"""
    parts = filename.replace('.json', '').split('_')
    if len(parts) >= 4:
        ip_parts = parts[:4]
        return '.'.join(ip_parts)
    return None

def extract_id_from_remark(remark):
    """从备注中提取ID（前面的数字）"""
    if not remark:
        return None
    match = re.match(r'^(\d+)', str(remark).strip())
    return match.group(1) if match else None

def get_user_contacts_and_ip(user_folder):
    """获取用户的联系人数据和IP地址"""
    contact_files = [f for f in user_folder.glob("*.json")
                    if not f.name.startswith(("new_contacts", "ip_change"))]

    if not contact_files:
        return None, None

    latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
    ip_address = extract_ip_from_filename(latest_file.name)

    if not ip_address:
        return None, None

    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            contacts = json.load(f)
        return contacts if isinstance(contacts, list) else None, ip_address
    except:
        return None, None

def find_contact_by_id(contacts, target_id):
    """根据ID查找联系人"""
    remark_pattern = re.compile(r'^\d+.*$')

    for contact in contacts:
        remark = contact.get("Remark", "")
        if remark and remark_pattern.match(str(remark).strip()):
            contact_id = extract_id_from_remark(remark)
            if contact_id == target_id:
                return contact
    return None

def get_talker_value(contact):
    """获取talker参数值"""
    for field in ['Remark', 'NickName', 'Alias', 'UserName']:
        value = contact.get(field, '')
        if value and str(value).strip():
            return str(value).strip()
    return None

def fetch_chatlog_by_id(target_id):
    """根据ID获取聊天记录"""
    data_dir = Path("data")
    if not data_dir.exists():
        return None

    excluded_folders = {"test_ip_change_user"}
    user_folders = [d for d in data_dir.iterdir()
                   if d.is_dir() and d.name not in excluded_folders]

    # 搜索联系人
    for user_folder in user_folders:
        username = user_folder.name

        contacts, ip_address = get_user_contacts_and_ip(user_folder)
        if not contacts:
            continue

        # 查找目标联系人
        contact = find_contact_by_id(contacts, target_id)
        if contact:
            # 获取聊天记录
            talker = get_talker_value(contact)
            if not talker:
                continue

            current_date = datetime.now().strftime("%Y-%m-%d")
            time_range = f"2019-01-01%7E{current_date}"
            encoded_talker = urllib.parse.quote(talker)

            api_url = f"http://{ip_address}:5030/api/v1/chatlog?time={time_range}&talker={encoded_talker}&format=text"

            try:
                response = requests.get(api_url, timeout=10)
                response.raise_for_status()

                # format=text 返回的是文本格式，需要解析
                chatlog_text = response.text
                parsed_messages = parse_text_chatlog(chatlog_text)

                return {
                    "contact_info": {
                        "id": target_id,
                        "remark": contact.get('Remark', ''),
                        "nickname": contact.get('NickName', ''),
                        "username": contact.get('UserName', ''),
                        "user": username,
                        "ip": ip_address
                    },
                    "messages": parsed_messages,
                    "total_count": len(parsed_messages),
                    "text_count": len([msg for msg in parsed_messages if msg.get('type') == 'text'])
                }

            except Exception as e:
                continue

    return None

def get_remarked_contacts():
    """获取所有有备注ID的联系人"""
    data_dir = Path("data")
    if not data_dir.exists():
        return []

    excluded_folders = {"test_ip_change_user"}
    user_folders = [d for d in data_dir.iterdir()
                   if d.is_dir() and d.name not in excluded_folders]

    remarked_contacts = []
    remark_pattern = re.compile(r'^\d+.*$')

    for user_folder in user_folders:
        username = user_folder.name
        contacts, ip_address = get_user_contacts_and_ip(user_folder)

        if not contacts:
            continue

        for contact in contacts:
            remark = contact.get("Remark", "")
            if remark and remark_pattern.match(str(remark).strip()):
                contact_id = extract_id_from_remark(remark)
                if contact_id:
                    remarked_contacts.append({
                        "id": contact_id,
                        "remark": remark,
                        "nickname": contact.get('NickName', ''),
                        "username": contact.get('UserName', ''),
                        "user": username
                    })

    # 按ID排序
    remarked_contacts.sort(key=lambda x: int(x['id']) if x['id'].isdigit() else 0)
    return remarked_contacts

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080, debug=True)

# 
# 
# 
# 
# 
# 






