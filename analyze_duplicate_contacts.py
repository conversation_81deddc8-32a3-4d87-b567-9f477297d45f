"""
分析全部文件夹中的重复联系人
"""

import json
from pathlib import Path
from collections import defaultdict
import re

class DuplicateContactAnalyzer:
    def __init__(self):
        self.user_contacts = {}  # {用户名: [联系人列表]}
        self.contact_index = defaultdict(list)  # {联系人标识: [(用户名, 联系人数据)]}
        
    def is_real_personal_contact(self, contact):
        """判断是否为真实个人联系人"""
        username = contact.get("UserName", "")
        nickname = contact.get("NickName", "") or ""
        alias = contact.get("Alias", "") or ""
        remark = contact.get("Remark", "") or ""
        
        # 排除群聊（包括@chatroom和@im.chatroom）
        if username.endswith("@chatroom") or username.endswith("@im.chatroom"):
            return False
        
        # 排除公众号
        if username.startswith("gh_"):
            return False
        
        # 排除企业微信
        if "@openim" in username:
            return False
        
        # 排除系统账号
        system_accounts = {
            "filehelper", "weixin", "fmessage", "floatbottle", "medianote", 
            "notifymessage", "tmessage", "qmessage", "qqmail", "wxitil",
            "brandcontact", "helper_entry", "pc_share", "newsapp"
        }
        if username in system_accounts:
            return False
        
        # 排除明显的企业/服务账号
        enterprise_keywords = [
            "有限公司", "股份有限", "集团", "工作室", "工作号",
            "客服", "售后", "支持", "帮助", "咨询", "顾问",
            "系统", "平台", "中心", "智能", "自动", "机器人", "bot",
            "生产商", "制造商", "厂家", "供应商", "经销商", "代理商",
            "官方", "认证", "验证", "审核", "管理员"
        ]
        
        for keyword in enterprise_keywords:
            if keyword in nickname:
                return False
        
        return True
    
    def generate_contact_key(self, contact):
        """生成联系人的唯一标识符"""
        username = contact.get("UserName", "")
        if username:
            return username
        
        # 如果没有UserName，使用其他字段组合
        nickname = contact.get("NickName", "")
        alias = contact.get("Alias", "")
        
        if nickname:
            return f"nick:{nickname}"
        elif alias:
            return f"alias:{alias}"
        
        return None
    
    def scan_all_users(self):
        """扫描所有用户的联系人"""
        print("🔍 开始扫描所有用户的联系人...")
        
        data_dir = Path("data")
        if not data_dir.exists():
            print("❌ data目录不存在")
            return False
        
        # 排除测试文件夹
        excluded_folders = {"test_ip_change_user"}
        user_folders = [d for d in data_dir.iterdir() 
                       if d.is_dir() and d.name not in excluded_folders]
        
        if not user_folders:
            print("❌ 没有找到用户文件夹")
            return False
        
        print(f"📊 找到 {len(user_folders)} 个用户文件夹")
        
        for user_folder in user_folders:
            username = user_folder.name
            print(f"  处理用户: {username}")
            
            # 查找联系人文件
            contact_files = [f for f in user_folder.glob("*.json") 
                           if not f.name.startswith(("new_contacts", "ip_change"))]
            
            user_contacts = []
            
            for contact_file in contact_files:
                try:
                    with open(contact_file, 'r', encoding='utf-8') as f:
                        contacts = json.load(f)
                    
                    if isinstance(contacts, list):
                        for contact in contacts:
                            # 只处理真实个人联系人
                            if self.is_real_personal_contact(contact):
                                user_contacts.append(contact)
                                
                                # 建立联系人索引
                                contact_key = self.generate_contact_key(contact)
                                if contact_key:
                                    self.contact_index[contact_key].append((username, contact))
                
                except Exception as e:
                    print(f"    ❌ 读取失败: {contact_file} - {str(e)}")
            
            self.user_contacts[username] = user_contacts
            print(f"    📊 个人联系人: {len(user_contacts)} 个")
        
        return True
    
    def analyze_duplicates(self):
        """分析重复联系人"""
        print(f"\n🔍 开始分析重复联系人...")
        
        duplicate_contacts = {}
        
        for contact_key, appearances in self.contact_index.items():
            if len(appearances) > 1:  # 出现在多个用户中
                users = list(set([username for username, contact in appearances]))
                
                # 取第一个作为代表联系人
                representative_contact = appearances[0][1]
                
                duplicate_contacts[contact_key] = {
                    'contact_data': representative_contact,
                    'user_count': len(users),
                    'users': sorted(users),
                    'appearances': appearances
                }
        
        print(f"📊 发现 {len(duplicate_contacts)} 个重复联系人")
        return duplicate_contacts
    
    def display_duplicate_contacts(self, duplicate_contacts):
        """显示重复联系人"""
        print(f"\n" + "=" * 80)
        print("重复出现的联系人:")
        print("=" * 80)
        
        # 按出现用户数量排序
        sorted_duplicates = sorted(
            duplicate_contacts.items(),
            key=lambda x: x[1]['user_count'],
            reverse=True
        )
        
        for i, (contact_key, duplicate_info) in enumerate(sorted_duplicates, 1):
            contact_data = duplicate_info['contact_data']
            nickname = contact_data.get("NickName", "") or "无昵称"
            username = contact_data.get("UserName", "")
            users = duplicate_info['users']
            user_count = duplicate_info['user_count']
            
            print(f"   {i:2d}. {nickname} ({username})")
            print(f"      出现在 {user_count} 个用户中")
            print(f"      用户: {', '.join(users)}")
            print()
        
        return sorted_duplicates
    
    def generate_detailed_report(self, duplicate_contacts):
        """生成详细报告"""
        # 创建analysis_reports目录
        reports_dir = Path("analysis_reports")
        reports_dir.mkdir(exist_ok=True)

        report_file = reports_dir / "duplicate_contacts_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("重复联系人分析报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"总用户数: {len(self.user_contacts)}\n")
            f.write(f"重复联系人数: {len(duplicate_contacts)}\n\n")
            
            # 用户统计
            f.write("各用户联系人统计:\n")
            for username, contacts in self.user_contacts.items():
                f.write(f"  {username}: {len(contacts)} 个个人联系人\n")
            f.write("\n")
            
            # 重复联系人详情
            f.write("重复联系人详情:\n\n")
            
            sorted_duplicates = sorted(
                duplicate_contacts.items(),
                key=lambda x: x[1]['user_count'],
                reverse=True
            )
            
            for i, (contact_key, duplicate_info) in enumerate(sorted_duplicates, 1):
                contact_data = duplicate_info['contact_data']
                nickname = contact_data.get("NickName", "") or "无昵称"
                username = contact_data.get("UserName", "")
                alias = contact_data.get("Alias", "") or ""
                remark = contact_data.get("Remark", "") or ""
                users = duplicate_info['users']
                user_count = duplicate_info['user_count']
                
                f.write(f"{i:3d}. {nickname} ({username})\n")
                f.write(f"     出现在 {user_count} 个用户中\n")
                f.write(f"     用户: {', '.join(users)}\n")
                
                if alias:
                    f.write(f"     别名: {alias}\n")
                if remark:
                    f.write(f"     备注: {remark}\n")
                
                f.write("\n")
        
        print(f"📄 详细报告已保存到: {report_file}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🚀 开始重复联系人分析...")
        print("=" * 80)
        
        # 1. 扫描所有用户
        if not self.scan_all_users():
            return False
        
        # 2. 分析重复联系人
        duplicate_contacts = self.analyze_duplicates()
        
        if not duplicate_contacts:
            print("📊 没有发现重复联系人")
            return True
        
        # 3. 显示重复联系人
        sorted_duplicates = self.display_duplicate_contacts(duplicate_contacts)
        
        # 4. 生成详细报告
        self.generate_detailed_report(duplicate_contacts)
        
        # 5. 显示统计摘要
        print("📊 统计摘要:")
        print(f"   总用户数: {len(self.user_contacts)}")
        print(f"   重复联系人数: {len(duplicate_contacts)}")
        
        # 按出现次数分组统计
        user_count_stats = defaultdict(int)
        for _, duplicate_info in duplicate_contacts.items():
            user_count_stats[duplicate_info['user_count']] += 1
        
        print(f"   重复分布:")
        for user_count in sorted(user_count_stats.keys(), reverse=True):
            count = user_count_stats[user_count]
            print(f"     出现在 {user_count} 个用户中: {count} 个联系人")
        
        return True

def main():
    analyzer = DuplicateContactAnalyzer()
    
    try:
        success = analyzer.run_analysis()
        if success:
            print(f"\n🎉 分析完成！")
        else:
            print(f"\n❌ 分析失败！")
    except KeyboardInterrupt:
        print(f"\n\n用户中断操作")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
