#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API图片分析测试脚本
测试本地图片上传和分析功能
"""

import base64
import json
import requests
import os
from pathlib import Path

# 尝试导入豆包SDK
try:
    from volcenginesdkarkruntime import Ark
    DOUBAO_AVAILABLE = True
except ImportError:
    print("❌ 豆包SDK未安装，请运行: pip install volcengine-python-sdk[ark]")
    DOUBAO_AVAILABLE = False

# 豆包API配置
DOUBAO_API_KEY = "cdd675a2-53d4-4aa1-bba8-33340ada088f"
DOUBAO_MODEL = "doubao-seed-1.6-250615"

def encode_image_to_base64(image_path):
    """将图片编码为base64格式"""
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
            print(f"✅ 图片编码成功: {image_path}")
            print(f"📏 Base64长度: {len(encoded_string)} 字符")
            return encoded_string
    except Exception as e:
        print(f"❌ 图片编码失败: {e}")
        return None

def init_doubao_client():
    """初始化豆包AI客户端"""
    if not DOUBAO_AVAILABLE:
        return None

    try:
        client = Ark(
            api_key=DOUBAO_API_KEY,
            timeout=1800,  # 30分钟超时
        )
        return client
    except Exception as e:
        print(f"❌ 豆包客户端初始化失败: {e}")
        return None

def analyze_image_with_doubao(image_base64, prompt="请分析这张图片的内容"):
    """使用豆包API分析图片"""

    if not DOUBAO_AVAILABLE:
        print("❌ 豆包SDK不可用")
        return None

    # 初始化客户端
    client = init_doubao_client()
    if not client:
        return None

    try:
        print("🚀 正在调用豆包API...")
        print(f"📝 模型: {DOUBAO_MODEL}")
        print(f"📝 提示词: {prompt}")

        # 构建消息
        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_base64}"
                        }
                    }
                ]
            }
        ]

        # 调用API
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=messages,
            max_tokens=2000,
            temperature=0.7
        )

        print("✅ API调用成功!")
        return response

    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 豆包API图片分析测试")
    print("=" * 60)
    
    # 图片路径
    image_path = r"F:\Practicum\server_receiver_final\downloaded_files\photo_2024.png"
    
    # 检查图片是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    print(f"📁 图片路径: {image_path}")
    print(f"📏 文件大小: {os.path.getsize(image_path)} 字节")
    
    # 编码图片
    image_base64 = encode_image_to_base64(image_path)
    if not image_base64:
        return
    
    # 分析图片
    prompt = """请详细分析这张图片的内容，包括：
1. 图片中的主要对象和场景
2. 图片的整体风格和特点
3. 如果是文档或截图，请提取其中的文字内容
4. 图片可能的用途或背景
5. 其他值得注意的细节"""
    
    result = analyze_image_with_doubao(image_base64, prompt)
    
    if result:
        print("\n" + "=" * 60)
        print("📋 分析结果:")
        print("=" * 60)
        
        # 提取分析内容
        if hasattr(result, 'choices') and len(result.choices) > 0:
            analysis = result.choices[0].message.content
            print(analysis)

            # 保存结果到文件
            output_file = "doubao_analysis_result.json"
            result_dict = {
                "model": DOUBAO_MODEL,
                "analysis": analysis,
                "usage": {
                    "prompt_tokens": result.usage.prompt_tokens if hasattr(result, 'usage') and hasattr(result.usage, 'prompt_tokens') else None,
                    "completion_tokens": result.usage.completion_tokens if hasattr(result, 'usage') and hasattr(result.usage, 'completion_tokens') else None,
                    "total_tokens": result.usage.total_tokens if hasattr(result, 'usage') and hasattr(result.usage, 'total_tokens') else None
                } if hasattr(result, 'usage') else None,
                "created": result.created if hasattr(result, 'created') else None
            }
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_dict, f, ensure_ascii=False, indent=2)
            print(f"\n💾 完整结果已保存到: {output_file}")

        else:
            print("❌ 响应格式异常")
            print(str(result))
    else:
        print("❌ 分析失败")

if __name__ == "__main__":
    main()
