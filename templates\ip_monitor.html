<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP地址监控</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: #495057;
        }

        .form-group input, .form-group select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .stats {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .records-container {
            padding: 20px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .record-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .record-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .record-username {
            font-weight: bold;
            color: #667eea;
            font-size: 16px;
        }

        .record-time {
            color: #6c757d;
            font-size: 12px;
        }

        .record-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            font-size: 14px;
        }

        .record-detail {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .record-detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 80px;
        }

        .record-detail-value {
            color: #6c757d;
            font-family: 'Courier New', monospace;
        }

        .no-records {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 IP地址监控系统</h1>
            <p>实时监控和记录IP地址变化</p>
        </div>

        <div class="controls">
            <div class="form-group">
                <label for="usernameFilter">用户名过滤:</label>
                <input type="text" id="usernameFilter" placeholder="输入用户名进行过滤">
            </div>
            
            <div class="form-group">
                <label for="limitSelect">显示数量:</label>
                <select id="limitSelect">
                    <option value="20">20条</option>
                    <option value="50" selected>50条</option>
                    <option value="100">100条</option>
                    <option value="200">200条</option>
                </select>
            </div>

            <button class="btn" onclick="loadIPHistory()">🔄 刷新数据</button>
            <button class="btn btn-secondary" onclick="clearFilters()">🗑️ 清除过滤</button>
            <button class="btn btn-secondary" onclick="exportData()">💾 导出数据</button>
        </div>

        <div class="stats" id="statsContainer">
            <!-- 统计信息将在这里显示 -->
        </div>

        <div class="records-container">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在加载IP变化记录...</p>
            </div>
            
            <div id="recordsContainer" style="display: none;">
                <!-- IP变化记录将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        let currentData = null;

        // 页面加载时自动加载数据
        window.addEventListener('load', function() {
            loadIPHistory();
            
            // 设置自动刷新（每30秒）
            setInterval(loadIPHistory, 30000);
        });

        // 加载IP变化历史
        async function loadIPHistory() {
            const loading = document.getElementById('loading');
            const recordsContainer = document.getElementById('recordsContainer');
            
            // 显示加载状态
            loading.style.display = 'block';
            recordsContainer.style.display = 'none';

            try {
                // 获取过滤参数
                const username = document.getElementById('usernameFilter').value.trim();
                const limit = document.getElementById('limitSelect').value;

                // 构建请求URL
                let url = '/api/ip_history?limit=' + limit;
                if (username) {
                    url += '&username=' + encodeURIComponent(username);
                }

                const response = await fetch(url);
                const result = await response.json();

                if (result.status === 'success') {
                    currentData = result.data;
                    displayStats(result.data);
                    displayRecords(result.data.records);
                } else {
                    showError('加载失败: ' + result.message);
                }

            } catch (error) {
                showError('请求失败: ' + error.message);
            } finally {
                // 隐藏加载状态
                loading.style.display = 'none';
                recordsContainer.style.display = 'block';
            }
        }

        // 显示统计信息
        function displayStats(data) {
            const statsContainer = document.getElementById('statsContainer');
            
            // 计算统计数据
            const totalRecords = data.total_count;
            const uniqueUsers = [...new Set(data.records.map(r => r.username))].length;
            const latestRecord = data.records.length > 0 ? data.records[0] : null;
            const timeRange = data.records.length > 1 ? 
                calculateTimeRange(data.records[data.records.length - 1].timestamp, data.records[0].timestamp) : 
                '无数据';

            statsContainer.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${totalRecords}</div>
                    <div class="stat-label">总记录数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${uniqueUsers}</div>
                    <div class="stat-label">用户数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${latestRecord ? latestRecord.username : '无'}</div>
                    <div class="stat-label">最新用户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${timeRange}</div>
                    <div class="stat-label">时间范围</div>
                </div>
            `;
        }

        // 显示记录列表
        function displayRecords(records) {
            const recordsContainer = document.getElementById('recordsContainer');
            
            if (records.length === 0) {
                recordsContainer.innerHTML = `
                    <div class="no-records">
                        <h3>📭 暂无记录</h3>
                        <p>没有找到符合条件的IP变化记录</p>
                    </div>
                `;
                return;
            }

            const recordsHtml = records.map(record => `
                <div class="record-item">
                    <div class="record-header">
                        <div class="record-username">👤 ${record.username}</div>
                        <div class="record-time">🕒 ${record.timestamp}</div>
                    </div>
                    <div class="record-details">
                        <div class="record-detail">
                            <span class="record-detail-label">新IP:</span>
                            <span class="record-detail-value">${record.ip_address}</span>
                        </div>
                        <div class="record-detail">
                            <span class="record-detail-label">客户端IP:</span>
                            <span class="record-detail-value">${record.client_ip}</span>
                        </div>
                        <div class="record-detail">
                            <span class="record-detail-label">用户代理:</span>
                            <span class="record-detail-value" title="${record.user_agent}">${record.user_agent.substring(0, 50)}${record.user_agent.length > 50 ? '...' : ''}</span>
                        </div>
                    </div>
                </div>
            `).join('');

            recordsContainer.innerHTML = recordsHtml;
        }

        // 清除过滤条件
        function clearFilters() {
            document.getElementById('usernameFilter').value = '';
            document.getElementById('limitSelect').value = '50';
            loadIPHistory();
        }

        // 导出数据
        function exportData() {
            if (!currentData || !currentData.records.length) {
                alert('没有数据可以导出');
                return;
            }

            const csvContent = generateCSV(currentData.records);
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', `ip_changes_${new Date().toISOString().slice(0, 10)}.csv`);
            link.style.visibility = 'hidden';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 生成CSV内容
        function generateCSV(records) {
            const headers = ['用户名', 'IP地址', '客户端IP', '时间', '用户代理'];
            const csvRows = [headers.join(',')];
            
            records.forEach(record => {
                const row = [
                    `"${record.username}"`,
                    `"${record.ip_address}"`,
                    `"${record.client_ip}"`,
                    `"${record.timestamp}"`,
                    `"${record.user_agent.replace(/"/g, '""')}"`
                ];
                csvRows.push(row.join(','));
            });
            
            return csvRows.join('\n');
        }

        // 计算时间范围
        function calculateTimeRange(startTime, endTime) {
            const start = new Date(startTime);
            const end = new Date(endTime);
            const diffMs = end - start;
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            
            if (diffHours < 24) {
                return `${diffHours}小时`;
            } else {
                const diffDays = Math.floor(diffHours / 24);
                return `${diffDays}天`;
            }
        }

        // 显示错误信息
        function showError(message) {
            const recordsContainer = document.getElementById('recordsContainer');
            recordsContainer.innerHTML = `
                <div class="error">
                    <h4>❌ 错误</h4>
                    <p>${message}</p>
                </div>
            `;
        }

        // 回车键触发搜索
        document.getElementById('usernameFilter').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadIPHistory();
            }
        });
    </script>
</body>
</html>
