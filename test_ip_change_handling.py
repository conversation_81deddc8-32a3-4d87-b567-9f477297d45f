"""
测试IP地址变化处理功能
"""

import os
import json
import time
from datetime import datetime
from pathlib import Path

def create_test_scenario():
    """创建测试场景"""
    print("="*60)
    print("IP地址变化处理功能测试")
    print("="*60)
    
    # 1. 创建测试用户
    username = "test_ip_change_user"
    user_folder = Path("data") / username
    user_folder.mkdir(parents=True, exist_ok=True)
    print(f"✅ 测试用户文件夹: {user_folder}")
    
    # 2. 模拟第一个IP地址的联系人文件
    ip1 = "*************"
    contacts_file1 = user_folder / f"{ip1.replace('.', '_')}_{username}.json"
    
    initial_contacts = [
        {
            "UserName": "contact_001",
            "NickName": "联系人1",
            "Alias": "",
            "Remark": "初始联系人"
        },
        {
            "UserName": "contact_002",
            "NickName": "联系人2", 
            "Alias": "alias2",
            "Remark": ""
        },
        {
            "UserName": "filehelper",
            "NickName": "文件传输助手",
            "Alias": "",
            "Remark": ""
        }
    ]
    
    with open(contacts_file1, 'w', encoding='utf-8') as f:
        json.dump(initial_contacts, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 第一个IP文件: {contacts_file1}")
    print(f"   IP地址: {ip1}")
    print(f"   联系人数量: {len(initial_contacts)}")
    
    # 3. 模拟IP地址变化
    time.sleep(1)
    ip2 = "*************"
    contacts_file2 = user_folder / f"{ip2.replace('.', '_')}_{username}.json"
    
    # 新IP下的联系人（包含新增）
    updated_contacts = initial_contacts + [
        {
            "UserName": "new_contact_001",
            "NickName": "新增联系人1",
            "Alias": "",
            "Remark": "IP变化后新增"
        }
    ]
    
    with open(contacts_file2, 'w', encoding='utf-8') as f:
        json.dump(updated_contacts, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 第二个IP文件: {contacts_file2}")
    print(f"   IP地址: {ip2}")
    print(f"   联系人数量: {len(updated_contacts)}")
    
    # 4. 创建IP变化记录
    create_ip_change_log(user_folder, username, ip1, ip2)
    
    # 5. 模拟第三次IP变化
    time.sleep(1)
    ip3 = "*********"
    contacts_file3 = user_folder / f"{ip3.replace('.', '_')}_{username}.json"
    
    # 第三个IP下的联系人（再次新增）
    final_contacts = updated_contacts + [
        {
            "UserName": "new_contact_002",
            "NickName": "新增联系人2",
            "Alias": "new2",
            "Remark": "第二次IP变化后新增"
        }
    ]
    
    with open(contacts_file3, 'w', encoding='utf-8') as f:
        json.dump(final_contacts, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 第三个IP文件: {contacts_file3}")
    print(f"   IP地址: {ip3}")
    print(f"   联系人数量: {len(final_contacts)}")
    
    # 6. 更新IP变化记录
    update_ip_change_log(user_folder, ip2, ip3)
    
    # 7. 显示测试结果
    print(f"\n📊 测试结果摘要:")
    display_test_results(user_folder)
    
    # 8. 清理选项
    cleanup = input(f"\n是否清理测试文件? (y/N): ").lower().strip()
    if cleanup == 'y':
        import shutil
        try:
            shutil.rmtree(user_folder)
            print(f"✅ 测试文件已清理")
        except Exception as e:
            print(f"⚠️  清理失败: {str(e)}")
    else:
        print(f"📁 测试文件保留在: {user_folder}")

def create_ip_change_log(user_folder, username, old_ip, new_ip):
    """创建IP变化记录"""
    ip_change_file = user_folder / "ip_change_log.json"
    
    ip_log = {
        "user": username,
        "created_time": datetime.now().isoformat(),
        "description": "IP地址变化记录",
        "changes": [
            {
                "timestamp": datetime.now().isoformat(),
                "change_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "old_ip": old_ip,
                "new_ip": new_ip,
                "old_filename_format": f"{old_ip.replace('.', '_')}_{username}.json",
                "new_filename_format": f"{new_ip.replace('.', '_')}_{username}.json"
            }
        ]
    }
    
    ip_log["last_updated"] = datetime.now().isoformat()
    ip_log["total_changes"] = len(ip_log["changes"])
    
    with open(ip_change_file, 'w', encoding='utf-8') as f:
        json.dump(ip_log, f, ensure_ascii=False, indent=2)
    
    print(f"✅ IP变化记录已创建: {ip_change_file}")

def update_ip_change_log(user_folder, old_ip, new_ip):
    """更新IP变化记录"""
    ip_change_file = user_folder / "ip_change_log.json"
    
    with open(ip_change_file, 'r', encoding='utf-8') as f:
        ip_log = json.load(f)
    
    # 添加新的变化记录
    new_change = {
        "timestamp": datetime.now().isoformat(),
        "change_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "old_ip": old_ip,
        "new_ip": new_ip,
        "old_filename_format": f"{old_ip.replace('.', '_')}_{ip_log['user']}.json",
        "new_filename_format": f"{new_ip.replace('.', '_')}_{ip_log['user']}.json"
    }
    
    ip_log["changes"].append(new_change)
    ip_log["last_updated"] = datetime.now().isoformat()
    ip_log["total_changes"] = len(ip_log["changes"])
    
    with open(ip_change_file, 'w', encoding='utf-8') as f:
        json.dump(ip_log, f, ensure_ascii=False, indent=2)
    
    print(f"✅ IP变化记录已更新")

def display_test_results(user_folder):
    """显示测试结果"""
    print(f"   用户文件夹: {user_folder}")
    
    # 显示所有联系人文件
    json_files = [f for f in os.listdir(user_folder) if f.endswith('.json') and not f.startswith(('new_contacts', 'ip_change'))]
    print(f"   联系人文件数量: {len(json_files)}")
    
    for i, filename in enumerate(sorted(json_files), 1):
        filepath = user_folder / filename
        with open(filepath, 'r', encoding='utf-8') as f:
            contacts = json.load(f)
        
        # 从文件名提取IP
        ip_part = filename.replace('.json', '').split('_')[:4]
        ip = '.'.join(ip_part)
        
        print(f"     {i}. {filename}")
        print(f"        IP: {ip}")
        print(f"        联系人数: {len(contacts)}")
    
    # 显示IP变化记录
    ip_change_file = user_folder / "ip_change_log.json"
    if ip_change_file.exists():
        with open(ip_change_file, 'r', encoding='utf-8') as f:
            ip_log = json.load(f)
        
        print(f"   IP变化记录:")
        print(f"     总变化次数: {ip_log.get('total_changes', 0)}")
        
        for i, change in enumerate(ip_log.get('changes', []), 1):
            print(f"     第{i}次变化:")
            print(f"       时间: {change.get('change_time', '未知')}")
            print(f"       {change.get('old_ip', '未知')} → {change.get('new_ip', '未知')}")
    
    print(f"\n💡 功能验证:")
    print(f"   ✅ 支持同一用户名下的多个IP地址")
    print(f"   ✅ 每个IP地址对应独立的联系人文件")
    print(f"   ✅ 记录IP地址变化历史")
    print(f"   ✅ 可以基于历史数据检测新增联系人")

def show_usage_example():
    """显示使用示例"""
    print("""
🎯 IP地址变化处理功能说明：

1. 用户名相同，IP不同的处理：
   - 用户名: zhang_san
   - IP1: ************* → 文件: 192_168_1_100_zhang_san.json
   - IP2: ************* → 文件: 192_168_1_200_zhang_san.json
   - IP3: *********     → 文件: 10_0_0_50_zhang_san.json

2. 自动功能：
   - 检测IP地址变化
   - 记录变化历史到 ip_change_log.json
   - 加载用户的历史联系人数据
   - 基于历史数据检测新增联系人
   - 按当前IP地址存储最新文件

3. 文件结构：
   data/
   └── zhang_san/
       ├── 192_168_1_100_zhang_san.json  # 第一个IP的联系人
       ├── 192_168_1_200_zhang_san.json  # 第二个IP的联系人
       ├── 10_0_0_50_zhang_san.json      # 第三个IP的联系人
       ├── ip_change_log.json            # IP变化记录
       ├── new_contacts_log.json         # 新增联系人记录
       └── new_contacts_summary.csv      # 新增联系人摘要

4. 优势：
   - 保留所有历史数据
   - 支持IP地址动态变化
   - 准确检测新增联系人
   - 完整的变化追踪
""")

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        create_test_scenario()
    else:
        show_usage_example()

if __name__ == "__main__":
    main()
