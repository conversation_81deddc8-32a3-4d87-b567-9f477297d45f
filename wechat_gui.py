import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
import json
import threading
import csv
import io
import os
import schedule
import time
from datetime import datetime
from pathlib import Path
import pystray
from PIL import Image, ImageDraw
import sys

class WeChatContactsGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("微信联系人获取工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')

        # 初始化状态变量
        self.is_first_run = True
        self.scheduler_running = False
        self.current_username = ""
        self.current_ip = ""  # 当前IP地址
        self.user_folder = ""
        self.last_contacts = {}  # 存储上次的联系人数据
        self.new_contacts_log_file = ""  # 新增联系人记录文件
        self.current_contacts_file = ""  # 当前联系人文件路径

        # 系统托盘相关
        self.tray_icon = None
        self.is_minimized_to_tray = False

        self.setup_ui()

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动定时任务线程
        self.start_scheduler_thread()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="微信联系人获取工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 用户名输入区域
        username_frame = ttk.LabelFrame(main_frame, text="用户信息 (用于生成文件名)", padding="10")
        username_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        username_frame.columnconfigure(1, weight=1)

        ttk.Label(username_frame, text="用户名:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(username_frame, textvariable=self.username_var, width=30)
        self.username_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        self.submit_username_btn = ttk.Button(username_frame, text="提交用户名",
                                            command=self.submit_username)
        self.submit_username_btn.grid(row=0, column=2)

        # 添加说明标签
        self.filename_info_var = tk.StringVar(value="文件将保存为: 客户端IP_用户名.json")
        info_label = ttk.Label(username_frame, textvariable=self.filename_info_var,
                              font=('Arial', 8), foreground='gray')
        info_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # 服务器配置区域
        config_frame = ttk.LabelFrame(main_frame, text="服务器配置", padding="10")
        config_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        ttk.Label(config_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.server_var = tk.StringVar(value="*************:8080")
        self.server_entry = ttk.Entry(config_frame, textvariable=self.server_var, width=30)
        self.server_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        ttk.Label(config_frame, text="客户端IP:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.client_ip_var = tk.StringVar()
        self.client_ip_entry = ttk.Entry(config_frame, textvariable=self.client_ip_var, width=30)
        self.client_ip_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        self.detect_ip_btn = ttk.Button(config_frame, text="自动检测IP",
                                      command=self.detect_client_ip)
        self.detect_ip_btn.grid(row=1, column=2, padx=(10, 0))

        ttk.Label(config_frame, text="MCP端口:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.mcp_port_var = tk.StringVar(value="5030")
        self.mcp_port_entry = ttk.Entry(config_frame, textvariable=self.mcp_port_var, width=30)
        self.mcp_port_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        self.fetch_contacts_btn = ttk.Button(button_frame, text="确认",
                                           command=self.confirm_and_start,
                                           style='Accent.TButton')
        self.fetch_contacts_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 添加状态显示按钮
        self.status_btn = ttk.Button(button_frame, text="查看状态",
                                   command=self.show_status)
        self.status_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.clear_log_btn = ttk.Button(button_frame, text="清空日志", 
                                      command=self.clear_log)
        self.clear_log_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.save_log_btn = ttk.Button(button_frame, text="保存日志", 
                                     command=self.save_log)
        self.save_log_btn.pack(side=tk.LEFT)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 初始化日志
        self.log_message("程序启动完成，请配置服务器信息后获取联系人")

        # 自动检测客户端IP
        self.detect_client_ip()
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def update_status(self, status):
        """更新状态栏"""
        self.status_var.set(status)
        self.root.update_idletasks()

    def detect_client_ip(self):
        """自动检测客户端IP地址"""
        try:
            import socket
            # 方法1: 连接到外部地址来获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]

            self.client_ip_var.set(local_ip)
            self.log_message(f"自动检测到客户端IP: {local_ip}")

        except Exception as e:
            # 方法2: 获取主机名对应的IP
            try:
                import socket
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                self.client_ip_var.set(local_ip)
                self.log_message(f"通过主机名检测到IP: {local_ip}")
            except Exception as e2:
                self.log_message(f"IP检测失败: {str(e)}, {str(e2)}")
                self.client_ip_var.set("*************")  # 默认值
                self.log_message("使用默认IP: *************")
        
    def submit_username(self):
        """提交用户名"""
        username = self.username_var.get().strip()
        if not username:
            messagebox.showwarning("警告", "请输入用户名")
            return

        client_ip = self.client_ip_var.get().strip()
        if not client_ip:
            messagebox.showwarning("警告", "请先检测或输入客户端IP地址")
            return

        # 清理用户名中的特殊字符
        clean_username = "".join(c for c in username if c.isalnum() or c in "._-")
        if not clean_username:
            messagebox.showwarning("警告", "用户名包含无效字符，请使用字母、数字、点、下划线或横线")
            return

        # 设置当前用户名和IP
        self.current_username = clean_username
        self.current_ip = client_ip

        # 创建用户文件夹
        self.user_folder = os.path.join("data", clean_username)
        try:
            Path(self.user_folder).mkdir(parents=True, exist_ok=True)
            self.log_message(f"用户文件夹已创建: {self.user_folder}")

            # 设置新增联系人记录文件
            self.new_contacts_log_file = os.path.join(self.user_folder, "new_contacts_log.json")
            self.init_new_contacts_log()

            # 加载该用户的历史联系人数据（如果存在）
            self.load_user_history()

        except Exception as e:
            self.log_message(f"创建用户文件夹失败: {str(e)}")
            messagebox.showerror("错误", f"创建用户文件夹失败: {str(e)}")
            return

        # 生成文件名预览
        clean_ip = client_ip.replace('.', '_')
        filename = f"{clean_ip}_{clean_username}.json"

        self.log_message(f"用户名已设置: {username}")
        self.log_message(f"客户端IP: {client_ip}")
        self.log_message(f"文件名将使用格式: {filename}")

        # 更新文件名预览
        self.filename_info_var.set(f"文件将保存为: {filename}")

        messagebox.showinfo("成功", f"用户名 '{username}' 已提交\n客户端IP: {client_ip}\n用户文件夹: {self.user_folder}\n文件将保存为: {filename}")
        
    def confirm_and_start(self):
        """确认按钮点击处理"""
        if not self.current_username:
            messagebox.showwarning("警告", "请先提交用户名")
            return

        if self.is_first_run:
            # 首次运行，立即获取联系人
            self.log_message("首次运行，立即获取联系人信息...")
            self.fetch_contacts_threaded()
            self.is_first_run = False

            # 更新按钮文本
            self.fetch_contacts_btn.config(text="手动获取")

            # 启动定时任务
            if not self.scheduler_running:
                self.setup_scheduler()
                self.scheduler_running = True
                self.log_message("定时任务已启动：每天09:00和13:00自动获取联系人")
        else:
            # 后续运行，手动获取
            self.log_message("手动获取联系人信息...")
            self.fetch_contacts_threaded()

    def fetch_contacts_threaded(self):
        """在新线程中获取联系人（避免界面冻结）"""
        thread = threading.Thread(target=self.fetch_contacts, daemon=True)
        thread.start()
        
    def fetch_contacts(self):
        """获取微信联系人"""
        try:
            self.update_status("正在获取联系人...")
            self.fetch_contacts_btn.config(state='disabled')

            server_addr = self.server_var.get().strip()
            if not server_addr:
                raise ValueError("请输入服务器地址")

            # 检查用户名是否已提交
            username = self.username_var.get().strip()
            if not username:
                raise ValueError("请先提交用户名")

            # 检查客户端IP是否已设置
            client_ip = self.client_ip_var.get().strip()
            if not client_ip:
                raise ValueError("请先检测或输入客户端IP地址")

            # 构建请求URL
            if not server_addr.startswith('http'):
                server_addr = f"http://{server_addr}"

            # 先获取联系人数据
            mcp_port = self.mcp_port_var.get().strip() or "5030"
            mcp_url = f"http://127.0.0.1:{mcp_port}/api/v1/contact"
            self.log_message(f"正在从MCP获取联系人: {mcp_url}")

            # 从MCP服务获取联系人数据
            mcp_response = requests.get(mcp_url, timeout=10)
            mcp_response.raise_for_status()
            content = mcp_response.text.replace('\r\n', '\n').strip()
            self.log_message(f"获取到联系人数据长度: {len(content)}")

            # 解析CSV数据
            try:
                f = io.StringIO(content, newline='')
                reader = csv.DictReader(f)
                contacts = [row for row in reader]
                self.log_message(f"解析到 {len(contacts)} 个联系人")
            except Exception as parse_err:
                raise ValueError(f"CSV解析失败: {str(parse_err)}")

            if not contacts:
                raise ValueError("联系人数据为空")

            # 使用用户提交的用户名和客户端IP生成client_id
            # 清理用户名中的特殊字符，避免文件名问题
            clean_username = "".join(c for c in username if c.isalnum() or c in "._-")
            if not clean_username:
                clean_username = "user"

            # 使用检测到的客户端IP
            client_id = f"{client_ip.replace('.', '_')}_{clean_username}"

            self.log_message(f"生成客户端ID: {client_id}")
            self.log_message(f"使用用户名: {username}")
            self.log_message(f"客户端IP: {client_ip}")

            # 检测新增联系人
            new_contacts = self.detect_new_contacts(contacts)

            # 根据当前IP地址生成文件名（支持IP变化）
            filename = f"{client_id}.json"
            filepath = os.path.join(self.user_folder, filename)
            self.current_contacts_file = filepath

            # 如果IP地址发生变化，记录变化信息
            self.check_ip_change(client_ip)

            try:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(contacts, f, ensure_ascii=False, indent=2)
                self.log_message(f"✅ 联系人文件已保存: {filepath}")
            except Exception as e:
                self.log_message(f"❌ 保存文件失败: {str(e)}")
                raise Exception(f"保存文件失败: {str(e)}")

            # 同时上传到服务器
            upload_url = f"{server_addr}/upload_contacts"
            self.log_message(f"正在上传联系人到: {upload_url}")

            upload_data = {
                "client_id": client_id,
                "contacts": contacts
            }

            upload_response = requests.post(upload_url, json=upload_data, timeout=30)
            upload_response.raise_for_status()

            result = upload_response.json()
            self.log_message(f"上传响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            # 更新上次联系人数据
            self.last_contacts = {contact.get("UserName", ""): contact for contact in contacts if contact.get("UserName")}

            if result.get("status") == "success":
                count = result.get("count", len(contacts))

                self.log_message(f"✅ 成功处理 {count} 个联系人")
                self.log_message(f"客户端ID: {client_id}")
                self.log_message(f"用户名: {username}")
                self.log_message(f"客户端IP: {client_ip}")
                self.log_message(f"保存文件: {filepath}")

                if new_contacts:
                    self.log_message(f"🆕 发现 {len(new_contacts)} 个新增联系人")
                    for contact in new_contacts[:5]:  # 显示前5个
                        nickname = contact.get("NickName", "无昵称")
                        self.log_message(f"   - {nickname}")

                    # 记录新增联系人（仅非首次运行时记录）
                    if not self.is_first_run:
                        self.record_new_contacts(new_contacts)

                messagebox.showinfo("成功", f"成功获取并处理 {count} 个微信联系人！\n客户端IP: {client_ip}\n用户名: {username}\n新增联系人: {len(new_contacts)}\n文件保存: {filepath}")

            else:
                error_msg = result.get("message", "未知错误")
                self.log_message(f"❌ 上传失败: {error_msg}")
                messagebox.showerror("错误", f"上传联系人失败:\n{error_msg}")

        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("网络错误", error_msg)

        except Exception as e:
            error_msg = f"操作失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

        finally:
            self.fetch_contacts_btn.config(state='normal')
            self.update_status("就绪")

    def detect_new_contacts(self, current_contacts):
        """检测新增的联系人"""
        if not self.last_contacts:
            # 首次运行，所有联系人都是"新增"的
            return []

        new_contacts = []
        current_usernames = {contact.get("UserName", "") for contact in current_contacts if contact.get("UserName")}
        last_usernames = set(self.last_contacts.keys())

        new_usernames = current_usernames - last_usernames

        for contact in current_contacts:
            username = contact.get("UserName", "")
            if username in new_usernames:
                new_contacts.append(contact)

        return new_contacts

    def init_new_contacts_log(self):
        """初始化新增联系人记录文件"""
        if not os.path.exists(self.new_contacts_log_file):
            initial_log = {
                "user": self.current_username,
                "created_time": datetime.now().isoformat(),
                "description": "新增联系人记录文件",
                "records": []
            }
            try:
                with open(self.new_contacts_log_file, 'w', encoding='utf-8') as f:
                    json.dump(initial_log, f, ensure_ascii=False, indent=2)
                self.log_message(f"新增联系人记录文件已创建: {self.new_contacts_log_file}")
            except Exception as e:
                self.log_message(f"创建新增联系人记录文件失败: {str(e)}")
        else:
            self.log_message(f"新增联系人记录文件已存在: {self.new_contacts_log_file}")

    def record_new_contacts(self, new_contacts):
        """记录新增联系人到文件"""
        if not new_contacts or not self.new_contacts_log_file:
            return

        try:
            # 读取现有记录
            if os.path.exists(self.new_contacts_log_file):
                with open(self.new_contacts_log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            else:
                log_data = {
                    "user": self.current_username,
                    "created_time": datetime.now().isoformat(),
                    "description": "新增联系人记录文件",
                    "records": []
                }

            # 创建新增记录
            new_record = {
                "timestamp": datetime.now().isoformat(),
                "detection_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "count": len(new_contacts),
                "contacts": []
            }

            # 添加新增联系人详情
            for contact in new_contacts:
                contact_info = {
                    "username": contact.get("UserName", ""),
                    "nickname": contact.get("NickName", ""),
                    "alias": contact.get("Alias", ""),
                    "remark": contact.get("Remark", ""),
                    "full_data": contact  # 保存完整数据
                }
                new_record["contacts"].append(contact_info)

            # 添加到记录列表
            log_data["records"].append(new_record)
            log_data["last_updated"] = datetime.now().isoformat()
            log_data["total_records"] = len(log_data["records"])

            # 保存到文件
            with open(self.new_contacts_log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            self.log_message(f"✅ 已记录 {len(new_contacts)} 个新增联系人到: {self.new_contacts_log_file}")

            # 同时创建CSV格式的记录（便于查看）
            self.create_new_contacts_csv()

        except Exception as e:
            self.log_message(f"❌ 记录新增联系人失败: {str(e)}")

    def create_new_contacts_csv(self):
        """创建新增联系人的CSV记录文件"""
        try:
            if not os.path.exists(self.new_contacts_log_file):
                return

            with open(self.new_contacts_log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            csv_file = os.path.join(self.user_folder, "new_contacts_summary.csv")

            with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)

                # 写入表头
                writer.writerow([
                    "检测时间", "用户名", "昵称", "别名", "备注", "记录批次"
                ])

                # 写入数据
                for i, record in enumerate(log_data["records"], 1):
                    detection_time = record["detection_time"]
                    for contact in record["contacts"]:
                        writer.writerow([
                            detection_time,
                            contact["username"],
                            contact["nickname"],
                            contact["alias"],
                            contact["remark"],
                            f"第{i}批"
                        ])

            self.log_message(f"✅ CSV摘要文件已更新: {csv_file}")

        except Exception as e:
            self.log_message(f"❌ 创建CSV摘要失败: {str(e)}")

    def load_user_history(self):
        """加载用户的历史联系人数据"""
        if not self.user_folder or not os.path.exists(self.user_folder):
            return

        try:
            # 查找该用户文件夹中的所有联系人文件
            json_files = [f for f in os.listdir(self.user_folder)
                         if f.endswith('.json') and not f.startswith('new_contacts')]

            if not json_files:
                self.log_message("没有找到历史联系人文件")
                return

            # 按修改时间排序，获取最新的文件
            json_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.user_folder, x)), reverse=True)
            latest_file = json_files[0]
            latest_filepath = os.path.join(self.user_folder, latest_file)

            # 加载最新的联系人数据
            with open(latest_filepath, 'r', encoding='utf-8') as f:
                contacts = json.load(f)

            # 构建联系人字典
            self.last_contacts = {contact.get("UserName", ""): contact
                                for contact in contacts if contact.get("UserName")}

            self.log_message(f"✅ 已加载历史联系人数据: {latest_file}")
            self.log_message(f"   历史联系人数量: {len(self.last_contacts)}")

            # 提取历史IP信息
            if '_' in latest_file:
                parts = latest_file.replace('.json', '').split('_')
                if len(parts) >= 4:
                    historical_ip = '.'.join(parts[:4])
                    self.log_message(f"   历史IP地址: {historical_ip}")

        except Exception as e:
            self.log_message(f"⚠️  加载历史联系人数据失败: {str(e)}")

    def check_ip_change(self, current_ip):
        """检查IP地址是否发生变化"""
        if not self.current_ip:
            self.current_ip = current_ip
            return

        if self.current_ip != current_ip:
            self.log_message(f"🔄 检测到IP地址变化:")
            self.log_message(f"   原IP: {self.current_ip}")
            self.log_message(f"   新IP: {current_ip}")

            # 记录IP变化到日志文件
            self.record_ip_change(self.current_ip, current_ip)

            # 更新当前IP
            self.current_ip = current_ip

    def record_ip_change(self, old_ip, new_ip):
        """记录IP地址变化"""
        try:
            ip_change_file = os.path.join(self.user_folder, "ip_change_log.json")

            # 读取现有记录
            if os.path.exists(ip_change_file):
                with open(ip_change_file, 'r', encoding='utf-8') as f:
                    ip_log = json.load(f)
            else:
                ip_log = {
                    "user": self.current_username,
                    "created_time": datetime.now().isoformat(),
                    "description": "IP地址变化记录",
                    "changes": []
                }

            # 添加新的变化记录
            change_record = {
                "timestamp": datetime.now().isoformat(),
                "change_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "old_ip": old_ip,
                "new_ip": new_ip,
                "old_filename_format": f"{old_ip.replace('.', '_')}_{self.current_username}.json",
                "new_filename_format": f"{new_ip.replace('.', '_')}_{self.current_username}.json"
            }

            ip_log["changes"].append(change_record)
            ip_log["last_updated"] = datetime.now().isoformat()
            ip_log["total_changes"] = len(ip_log["changes"])

            # 保存记录
            with open(ip_change_file, 'w', encoding='utf-8') as f:
                json.dump(ip_log, f, ensure_ascii=False, indent=2)

            self.log_message(f"✅ IP变化已记录到: {ip_change_file}")

        except Exception as e:
            self.log_message(f"❌ 记录IP变化失败: {str(e)}")

    def setup_scheduler(self):
        """设置定时任务"""
        schedule.every().day.at("09:00").do(self.scheduled_fetch)
        schedule.every().day.at("13:00").do(self.scheduled_fetch)
        self.log_message("定时任务已设置：每天09:00和13:00")

    def scheduled_fetch(self):
        """定时获取联系人"""
        self.log_message("⏰ 定时任务触发，开始获取联系人...")
        self.fetch_contacts()

    def start_scheduler_thread(self):
        """启动定时任务线程"""
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次

        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()

    def show_status(self):
        """显示当前状态"""
        # 获取新增联系人记录统计
        new_contacts_stats = self.get_new_contacts_stats()

        # 获取IP变化统计
        ip_change_stats = self.get_ip_change_stats()

        status_info = f"""
当前状态信息：

用户名: {self.current_username or '未设置'}
当前IP: {self.current_ip or '未检测'}
用户文件夹: {self.user_folder or '未创建'}
当前联系人文件: {os.path.basename(self.current_contacts_file) if self.current_contacts_file else '未创建'}
首次运行: {'是' if self.is_first_run else '否'}
定时任务: {'已启动' if self.scheduler_running else '未启动'}
上次联系人数量: {len(self.last_contacts)}

新增联系人记录:
- 记录文件: {os.path.basename(self.new_contacts_log_file) if self.new_contacts_log_file else '未创建'}
- 总记录批次: {new_contacts_stats['total_records']}
- 总新增人数: {new_contacts_stats['total_new_contacts']}
- 最后记录时间: {new_contacts_stats['last_record_time']}

IP地址变化记录:
- 总变化次数: {ip_change_stats['total_changes']}
- 最后变化时间: {ip_change_stats['last_change_time']}
- 当前使用IP: {self.current_ip or '未设置'}

定时计划:
- 每天 09:00 自动获取
- 每天 13:00 自动获取

说明:
- 首次获取的联系人不会记录为"新增"
- 只有后续自动抓取中发现的新联系人才会被记录
- IP地址变化时会自动使用新IP创建文件
- 同一用户名的历史数据会自动加载用于新增检测
"""
        messagebox.showinfo("状态信息", status_info)

    def get_new_contacts_stats(self):
        """获取新增联系人记录统计"""
        stats = {
            'total_records': 0,
            'total_new_contacts': 0,
            'last_record_time': '无记录'
        }

        if not self.new_contacts_log_file or not os.path.exists(self.new_contacts_log_file):
            return stats

        try:
            with open(self.new_contacts_log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            stats['total_records'] = len(log_data.get('records', []))

            total_contacts = 0
            last_time = None

            for record in log_data.get('records', []):
                total_contacts += record.get('count', 0)
                record_time = record.get('detection_time')
                if record_time:
                    last_time = record_time

            stats['total_new_contacts'] = total_contacts
            if last_time:
                stats['last_record_time'] = last_time

        except Exception as e:
            self.log_message(f"读取新增联系人统计失败: {str(e)}")

        return stats

    def get_ip_change_stats(self):
        """获取IP变化统计"""
        stats = {
            'total_changes': 0,
            'last_change_time': '无变化'
        }

        if not self.user_folder:
            return stats

        ip_change_file = os.path.join(self.user_folder, "ip_change_log.json")

        if not os.path.exists(ip_change_file):
            return stats

        try:
            with open(ip_change_file, 'r', encoding='utf-8') as f:
                ip_log = json.load(f)

            stats['total_changes'] = len(ip_log.get('changes', []))

            changes = ip_log.get('changes', [])
            if changes:
                last_change = changes[-1]
                stats['last_change_time'] = last_change.get('change_time', '未知')

        except Exception as e:
            self.log_message(f"读取IP变化统计失败: {str(e)}")

        return stats

    def create_tray_icon(self):
        """创建系统托盘图标"""
        try:
            # 创建一个简单的图标
            image = Image.new('RGB', (64, 64), color='blue')
            draw = ImageDraw.Draw(image)
            draw.ellipse([16, 16, 48, 48], fill='white')
            draw.text((24, 28), "微", fill='blue')

            # 创建托盘菜单
            menu = pystray.Menu(
                pystray.MenuItem("显示窗口", self.show_window),
                pystray.MenuItem("查看状态", self.show_tray_status),
                pystray.MenuItem("手动获取", self.manual_fetch_from_tray),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("退出程序", self.quit_application)
            )

            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                "wechat_contacts",
                image,
                "微信联系人监控工具",
                menu
            )

            return True

        except Exception as e:
            self.log_message(f"创建系统托盘图标失败: {str(e)}")
            return False

    def on_closing(self):
        """窗口关闭事件处理"""
        if self.scheduler_running:
            # 如果定时任务正在运行，最小化到托盘
            result = messagebox.askyesnocancel(
                "最小化到托盘",
                "定时任务正在运行中。\n\n"
                "是：最小化到系统托盘（后台继续运行）\n"
                "否：直接退出程序\n"
                "取消：返回程序"
            )

            if result is True:  # 是 - 最小化到托盘
                self.minimize_to_tray()
            elif result is False:  # 否 - 直接退出
                self.quit_application()
            # 取消 - 什么都不做，返回程序
        else:
            # 如果没有定时任务，直接询问是否退出
            if messagebox.askokcancel("退出", "确定要退出程序吗？"):
                self.quit_application()

    def minimize_to_tray(self):
        """最小化到系统托盘"""
        try:
            if self.create_tray_icon():
                self.root.withdraw()  # 隐藏窗口
                self.is_minimized_to_tray = True

                # 在新线程中运行托盘图标
                tray_thread = threading.Thread(target=self.run_tray, daemon=True)
                tray_thread.start()

                self.log_message("程序已最小化到系统托盘，定时任务继续运行")
            else:
                messagebox.showerror("错误", "无法创建系统托盘图标，程序将继续在前台运行")

        except Exception as e:
            self.log_message(f"最小化到托盘失败: {str(e)}")
            messagebox.showerror("错误", f"最小化到托盘失败: {str(e)}")

    def run_tray(self):
        """运行系统托盘"""
        try:
            self.tray_icon.run()
        except Exception as e:
            self.log_message(f"系统托盘运行失败: {str(e)}")

    def show_window(self, icon=None, item=None):
        """显示窗口"""
        try:
            self.root.deiconify()  # 显示窗口
            self.root.lift()       # 置顶
            self.root.focus_force() # 获取焦点
            self.is_minimized_to_tray = False

            # 停止托盘图标
            if self.tray_icon:
                self.tray_icon.stop()

            self.log_message("程序窗口已恢复显示")

        except Exception as e:
            self.log_message(f"显示窗口失败: {str(e)}")

    def show_tray_status(self, icon=None, item=None):
        """从托盘显示状态信息"""
        try:
            # 获取状态信息
            new_contacts_stats = self.get_new_contacts_stats()
            ip_change_stats = self.get_ip_change_stats()

            status_msg = f"""微信联系人监控工具状态

用户名: {self.current_username or '未设置'}
当前IP: {self.current_ip or '未检测'}
定时任务: {'运行中' if self.scheduler_running else '未启动'}
联系人数量: {len(self.last_contacts)}

新增记录: {new_contacts_stats['total_new_contacts']}个
IP变化: {ip_change_stats['total_changes']}次

定时计划: 每天09:00和13:00
"""

            # 使用Windows通知显示状态
            if self.tray_icon:
                self.tray_icon.notify("状态信息", status_msg)

        except Exception as e:
            if self.tray_icon:
                self.tray_icon.notify("错误", f"获取状态失败: {str(e)}")

    def manual_fetch_from_tray(self, icon=None, item=None):
        """从托盘手动获取联系人"""
        try:
            if not self.current_username:
                if self.tray_icon:
                    self.tray_icon.notify("提示", "请先设置用户名")
                return

            # 在后台线程中执行获取
            fetch_thread = threading.Thread(target=self.fetch_contacts, daemon=True)
            fetch_thread.start()

            if self.tray_icon:
                self.tray_icon.notify("开始获取", "正在获取微信联系人...")

        except Exception as e:
            if self.tray_icon:
                self.tray_icon.notify("错误", f"获取失败: {str(e)}")

    def quit_application(self, icon=None, item=None):
        """退出应用程序"""
        try:
            self.log_message("程序正在退出...")

            # 停止托盘图标
            if self.tray_icon:
                self.tray_icon.stop()

            # 销毁窗口
            if self.root:
                self.root.quit()
                self.root.destroy()

            # 退出程序
            sys.exit(0)

        except Exception as e:
            print(f"退出程序时发生错误: {str(e)}")
            sys.exit(1)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")
        
    def save_log(self):
        """保存日志到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wechat_contacts_log_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
                
            self.log_message(f"日志已保存到: {filename}")
            messagebox.showinfo("成功", f"日志已保存到: {filename}")
            
        except Exception as e:
            error_msg = f"保存日志失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

def main():
    root = tk.Tk()
    WeChatContactsGUI(root)  # 创建GUI实例

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件可以取消注释
        pass
    except:
        pass
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
