#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

def test_ai_message_generation():
    """测试AI消息生成功能"""
    print("🤖 测试AI消息生成功能")
    print("=" * 50)
    
    # 测试数据
    test_data = {
        "user_input": "询问对方是否有时间进行电话面试，我们有一个很好的工作机会",
        "receiver": "张博士"
    }
    
    api_url = "http://127.0.0.1:8080/api/generate_message"
    
    try:
        print(f"📡 API地址: {api_url}")
        print(f"👤 接收人: {test_data['receiver']}")
        print(f"💭 用户意图: {test_data['user_input']}")
        
        response = requests.post(
            api_url,
            json=test_data,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AI消息生成成功!")
            print(f"📄 生成的消息:")
            print(f"   {result.get('generated_message', '无消息内容')}")
            return result.get('generated_message', '')
        else:
            print(f"❌ AI消息生成失败: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None

def test_wechat_send_api():
    """测试微信消息发送API"""
    print("\n📤 测试微信消息发送API")
    print("=" * 50)
    
    # 测试数据
    test_data = {
        "target_host": "localhost:8000",
        "receiver": "小马",  # 请替换为实际的微信联系人昵称
        "msg": "Hello! 这是通过Flask API发送的测试消息。"
    }
    
    api_url = "http://127.0.0.1:8080/api/send_wechat_message"
    
    try:
        print(f"📡 API地址: {api_url}")
        print(f"🎯 目标主机: {test_data['target_host']}")
        print(f"👤 接收人: {test_data['receiver']}")
        print(f"💬 消息内容: {test_data['msg']}")
        
        response = requests.post(
            api_url,
            json=test_data,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 微信消息发送API调用成功!")
            print(f"📄 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ 微信消息发送失败: {response.status_code}")
            try:
                error_result = response.json()
                print(f"📄 错误内容:")
                print(json.dumps(error_result, indent=2, ensure_ascii=False))
            except:
                print(f"📄 错误内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_direct_wechat_api():
    """测试直接调用微信API"""
    print("\n🔗 测试直接调用微信API")
    print("=" * 50)
    
    # 测试数据
    test_data = {
        "receiver": "小马",  # 请替换为实际的微信联系人昵称
        "msg": "Hello! 这是直接调用微信API的测试消息。"
    }
    
    api_url = "http://localhost:8000/api/sendMsg"
    
    try:
        print(f"📡 API地址: {api_url}")
        print(f"👤 接收人: {test_data['receiver']}")
        print(f"💬 消息内容: {test_data['msg']}")
        
        response = requests.post(
            api_url,
            json=test_data,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ 直接微信API调用成功!")
                print(f"📄 响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
            except ValueError:
                print("✅ 直接微信API调用成功!")
                print(f"📄 响应内容: {response.text}")
            return True
        else:
            print(f"❌ 直接微信API调用失败: {response.status_code}")
            print(f"📄 错误内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败 - 微信API服务可能未运行")
        print("💡 请确保微信API服务正在运行在 http://localhost:8000")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_integrated_workflow():
    """测试完整的工作流程：AI生成消息 -> 发送消息"""
    print("\n🔄 测试完整工作流程")
    print("=" * 50)
    
    # 1. 生成AI消息
    print("步骤1: 使用AI生成消息")
    generated_message = test_ai_message_generation()
    
    if not generated_message:
        print("❌ AI消息生成失败，无法继续测试")
        return False
    
    print(f"\n步骤2: 使用生成的消息发送微信")
    time.sleep(1)  # 稍等一下
    
    # 2. 发送生成的消息
    test_data = {
        "target_host": "localhost:8000",
        "receiver": "小马",  # 请替换为实际的微信联系人昵称
        "msg": generated_message
    }
    
    api_url = "http://127.0.0.1:8080/api/send_wechat_message"
    
    try:
        print(f"📡 发送AI生成的消息...")
        print(f"💬 消息内容: {generated_message[:100]}{'...' if len(generated_message) > 100 else ''}")
        
        response = requests.post(
            api_url,
            json=test_data,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 完整工作流程测试成功!")
            print("🎉 AI生成消息并成功发送到微信")
            return True
        else:
            print(f"❌ 消息发送失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 发送请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 微信消息发送功能全面测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: AI消息生成
    print("\n🧪 测试1: AI消息生成功能")
    result1 = test_ai_message_generation()
    test_results.append(("AI消息生成", result1 is not None))
    
    # 测试2: 微信发送API
    print("\n🧪 测试2: 微信消息发送API")
    result2 = test_wechat_send_api()
    test_results.append(("微信发送API", result2))
    
    # 测试3: 直接微信API
    print("\n🧪 测试3: 直接微信API调用")
    result3 = test_direct_wechat_api()
    test_results.append(("直接微信API", result3))
    
    # 测试4: 完整工作流程
    print("\n🧪 测试4: 完整工作流程")
    result4 = test_integrated_workflow()
    test_results.append(("完整工作流程", result4))
    
    # 显示测试结果汇总
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！微信消息发送功能正常")
    else:
        print("⚠️ 部分测试失败，请检查相关服务和配置")
        print("\n💡 常见问题排查:")
        print("1. 确保Flask应用正在运行 (http://127.0.0.1:8080)")
        print("2. 确保微信API服务正在运行 (http://localhost:8000)")
        print("3. 确保接收人昵称正确且为微信好友")
        print("4. 检查网络连接和防火墙设置")

if __name__ == "__main__":
    main()
