#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI标签功能测试脚本
"""

from volcenginesdkarkruntime import Ark

# 豆包AI配置
DOUBAO_API_KEY = "cdd675a2-53d4-4aa1-bba8-33340ada088f"
DOUBAO_MODEL = "doubao-seed-1.6-250615"

def init_doubao_client():
    """初始化豆包客户端"""
    try:
        client = Ark(api_key=DOUBAO_API_KEY)
        return client
    except Exception as e:
        print(f"❌ 豆包客户端初始化失败: {e}")
        return None

def extract_nickname_tags_with_ai(nickname):
    """使用AI分析昵称中的地区和头衔信息"""
    client = init_doubao_client()
    if not client:
        return []
    
    try:
        prompt = f"""
作为一个文本分析助手，请帮我从以下用户昵称中提取关键信息标签。

用户昵称: {nickname}

请提取以下类型的信息标签：
- 技术技能：如Java、Python、前端、后端等
- 职业角色：如工程师、经理、设计师等
- 工作经验：如1年、3年、5年等
- 地理位置：如北京、上海、深圳等城市名
- 教育背景：如教授、博士、研究员等学术称谓

输出格式：
- 只输出找到的标签，用逗号分隔
- 如果没有找到相关信息，输出"无标签"
- 不要输出解释或其他内容

示例输出：Java,后端,北京,5年经验
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,
        )
        
        ai_result = response.choices[0].message.content.strip()
        
        if ai_result and ai_result != "无标签":
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析昵称 '{nickname}' 提取标签: {tags}")
            return tags
        
        return []
        
    except Exception as e:
        print(f"❌ AI昵称分析失败: {e}")
        return []

def extract_conversation_tags_with_ai(content):
    """使用AI分析聊天内容提取标签"""
    client = init_doubao_client()
    if not client:
        return []
    
    try:
        prompt = f"""
作为一个职业信息分析助手，请从以下对话内容中提取职业相关的标签信息。

对话内容：
{content[:1200]}

请提取以下类型的标签：
- 专业技能：如Python、Java、React、设计、产品等
- 工作类型：如全职、兼职、远程等
- 求职状态：如主动求职、被动求职等
- 薪资期望：如10-20万、20-30万等
- 工作意愿：如意愿强烈、意愿一般等
- 其他特征：如可推荐朋友、有作品集等

输出要求：
- 只输出确实能从内容中提取的标签
- 用逗号分隔多个标签
- 标签要简洁明确
- 如果没有相关信息，输出"无相关标签"

示例输出：Python,后端开发,全职,主动求职,30-50万,意愿强烈
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.3,
        )
        
        ai_result = response.choices[0].message.content.strip()
        
        if ai_result:
            tags = [tag.strip() for tag in ai_result.split(',') if tag.strip()]
            print(f"🤖 AI分析聊天内容提取标签: {tags}")
            return tags
        
        return []
        
    except Exception as e:
        print(f"❌ AI聊天内容分析失败: {e}")
        return []

def test_nickname_analysis():
    """测试昵称分析"""
    print("🧪 测试昵称AI分析")
    print("=" * 50)
    
    test_nicknames = [
        "3236 Java后端开发 北京 5年经验",
        "张教授 清华 长江学者",
        "李博导 省青千 上海交大",
        "王总监 腾讯 深圳",
        "陈研究员 中科院 优青",
        "刘工程师 华为 杭州",
        "赵产品经理 字节跳动",
        "孙设计师 阿里巴巴 UI",
        "周数据分析师 美团",
        "吴AI算法工程师 百度"
    ]
    
    for nickname in test_nicknames:
        print(f"\n📝 测试昵称: {nickname}")
        tags = extract_nickname_tags_with_ai(nickname)
        print(f"✅ 提取标签: {tags}")

def test_conversation_analysis():
    """测试聊天内容分析"""
    print("\n🧪 测试聊天内容AI分析")
    print("=" * 50)
    
    test_conversations = [
        "我是一名Python开发工程师，有5年的后端开发经验。目前在考虑全职机会，期望年薪30万左右。对AI和机器学习很感兴趣，希望能找到相关的工作。",
        
        "我在做前端开发，主要用React和Vue。现在是兼职状态，想找个20万左右的全职工作。我可以推荐几个朋友，他们也在找工作。",
        
        "我是清华大学的教授，入选了青年千人计划。主要研究方向是人工智能和深度学习。最近在考虑去企业做技术顾问，薪资要求不高，主要看平台和发展机会。",
        
        "做产品经理3年了，在字节跳动工作。想换个环境，期望薪资40万以上。对这个机会很感兴趣，什么时候可以详细聊聊？",
        
        "我是UI设计师，有作品集。目前在上海工作，考虑远程或者北京的机会。薪资期望25万左右，主要看团队和项目。"
    ]
    
    for i, conversation in enumerate(test_conversations, 1):
        print(f"\n📝 测试对话 {i}:")
        print(f"内容: {conversation[:100]}...")
        tags = extract_conversation_tags_with_ai(conversation)
        print(f"✅ 提取标签: {tags}")

def main():
    """主函数"""
    print("🚀 AI标签提取功能测试")
    print("=" * 60)
    
    # 测试昵称分析
    test_nickname_analysis()
    
    # 测试聊天内容分析
    test_conversation_analysis()
    
    print("\n🏁 测试完成")

if __name__ == "__main__":
    main()
