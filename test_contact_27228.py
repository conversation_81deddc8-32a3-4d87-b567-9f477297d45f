#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试联系人27228的语音消息
"""

import json
import requests

def test_contact_27228():
    """测试联系人27228的消息"""
    print("=" * 60)
    print("🔍 测试联系人27228的语音消息")
    print("=" * 60)
    
    try:
        # 获取聊天记录
        response = requests.get("http://127.0.0.1:8080/api/get_chatlog_by_id/27228")
        if response.status_code != 200:
            print(f"❌ 获取聊天记录失败: {response.status_code}")
            return
        
        chat_data = response.json()
        messages = chat_data.get('data', {}).get('messages', [])
        
        print(f"📊 总消息数: {len(messages)}")
        
        # 查找语音相关消息
        voice_messages = []
        voice_call_messages = []
        
        for i, msg in enumerate(messages):
            content = msg.get('content', '')
            msg_type = msg.get('type', 'unknown')
            url = msg.get('url', '')
            filename = msg.get('filename', '')
            timestamp = msg.get('time', '')
            is_self = msg.get('isSelf', False)
            
            # 检查是否包含语音相关内容
            content_lower = content.lower()
            
            is_voice_related = False
            voice_type = ""
            
            # 检查各种语音格式
            if '[语音](' in content and 'voice/' in content:
                is_voice_related = True
                voice_type = "语音消息(带URL)"
                voice_messages.append({
                    'index': i,
                    'content': content,
                    'url': url,
                    'filename': filename,
                    'timestamp': timestamp,
                    'is_self': is_self,
                    'type': voice_type
                })
            elif '[语音通话]' in content or '语音通话' in content:
                is_voice_related = True
                voice_type = "语音通话"
                voice_call_messages.append({
                    'index': i,
                    'content': content,
                    'url': url,
                    'filename': filename,
                    'timestamp': timestamp,
                    'is_self': is_self,
                    'type': voice_type
                })
            elif 'voice/' in url:
                is_voice_related = True
                voice_type = "语音URL"
                voice_messages.append({
                    'index': i,
                    'content': content,
                    'url': url,
                    'filename': filename,
                    'timestamp': timestamp,
                    'is_self': is_self,
                    'type': voice_type
                })
            elif any(keyword in content_lower for keyword in ['语音', 'voice', 'audio']):
                is_voice_related = True
                voice_type = "语音关键词"
                voice_messages.append({
                    'index': i,
                    'content': content,
                    'url': url,
                    'filename': filename,
                    'timestamp': timestamp,
                    'is_self': is_self,
                    'type': voice_type
                })
            
            if is_voice_related:
                print(f"\n--- 语音相关消息 {len(voice_messages) + len(voice_call_messages)} ---")
                print(f"索引: {i}")
                print(f"类型: {voice_type}")
                print(f"时间: {timestamp}")
                print(f"发送者: {'我' if is_self else '对方'}")
                print(f"内容: {content}")
                print(f"URL: {url}")
                print(f"文件名: {filename}")
        
        print(f"\n📊 统计结果:")
        print(f"语音消息: {len(voice_messages)}条")
        print(f"语音通话: {len(voice_call_messages)}条")
        
        # 保存结果
        result = {
            'contact_id': '27228',
            'total_messages': len(messages),
            'voice_messages': voice_messages,
            'voice_call_messages': voice_call_messages
        }
        
        with open('test_27228_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 结果已保存到: test_27228_result.json")
        
        # 如果找到语音消息，尝试提取URL
        if voice_messages:
            print(f"\n🎤 发现的语音消息:")
            for vm in voice_messages:
                print(f"  - {vm['timestamp']}: {vm['content'][:50]}...")
                if vm['url']:
                    print(f"    URL: {vm['url']}")
        
        if voice_call_messages:
            print(f"\n📞 发现的语音通话:")
            for vc in voice_call_messages:
                print(f"  - {vc['timestamp']}: {vc['content'][:50]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_contact_27228()
