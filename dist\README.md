# SendIp.exe - IP地址监控工具

## 📋 功能说明

SendIp.exe 是一个Windows桌面应用程序，用于监控本机IP地址变化并自动报告给服务器。

## 🚀 使用方法

### 1. 配置服务器地址
编辑 `config.txt` 文件，设置正确的服务器地址：
```
http://your-server:8080/report_ip
```

### 2. 运行程序
双击 `SendIp.exe` 启动程序

### 3. 设置用户名
首次运行时会提示输入用户名，这个用户名将用于标识你的设备

## ⚙️ 功能特性

- **自动IP监控**: 每60秒检查一次IP地址变化
- **图形界面**: 简洁的Windows GUI界面
- **自动报告**: IP变化时自动发送到服务器
- **开机自启**: 可设置开机自动启动
- **系统托盘**: 最小化到系统托盘运行

## 📁 文件说明

- `SendIp.exe` - 主程序文件
- `config.txt` - 配置文件
- `README.md` - 本说明文件

## 🔧 配置选项

### config.txt 格式：
```
# 第一行：服务器地址
http://127.0.0.1:8080/report_ip

# 第二行：检查间隔（秒）
60

# 第三行：用户名（可选）
my_computer
```

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器地址是否正确
   - 确认服务器正在运行
   - 检查网络连接

2. **程序无法启动**
   - 确保config.txt文件存在
   - 检查文件权限
   - 以管理员身份运行

3. **IP检测不准确**
   - 程序会自动检测公网IP
   - 如果在内网环境，可能显示内网IP

## 📊 工作原理

1. 程序启动后会读取配置文件
2. 定期检查当前IP地址
3. 如果IP地址发生变化，发送POST请求到服务器
4. 服务器接收到请求后更新用户文件夹下的JSON文件名

## 🔒 安全说明

- 程序只发送用户名和IP地址信息
- 不收集其他个人信息
- 所有通信使用HTTP协议（可升级为HTTPS）

---
生成时间: 2025-07-15 10:23:26
版本: 1.0.0
