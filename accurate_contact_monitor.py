# 准确监控微信联系人变化的工具 - 通过文件覆盖前后的对比来检测新增和减少的联系人

import json
import os
import time
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import threading
import shutil

class AccurateContactMonitor:
    def __init__(self):
        self.data_dir = Path("data")
        self.excluded_folders = {"test_ip_change_user"}
        self.monitoring = False
        self.backup_dir = Path("contact_backup")  # 备份目录
        self.user_baselines = {}  # 存储每个用户的基准数据
        
        # 创建备份目录
        self.backup_dir.mkdir(exist_ok=True)
        
    def get_all_users(self):
        """获取所有用户文件夹"""
        if not self.data_dir.exists():
            return []
        
        user_folders = [d for d in self.data_dir.iterdir() 
                       if d.is_dir() and d.name not in self.excluded_folders]
        return sorted(user_folders)
    
    def get_contact_usernames_from_file(self, file_path):
        """从联系人文件中提取UserName集合"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                contacts = json.load(f)
            
            if isinstance(contacts, list):
                usernames = {contact.get("UserName", "") for contact in contacts if contact.get("UserName")}
                return usernames, contacts
            else:
                return set(), []
                
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return set(), []
    
    def backup_current_contacts(self):
        """备份当前所有用户的联系人文件"""
        print("💾 备份当前联系人文件...")
        
        user_folders = self.get_all_users()
        backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for user_folder in user_folders:
            username = user_folder.name
            
            # 查找联系人文件
            contact_files = [f for f in user_folder.glob("*.json") 
                           if not f.name.startswith(("new_contacts", "ip_change"))]
            
            if contact_files:
                # 备份最新的联系人文件
                latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
                
                # 创建用户备份目录
                user_backup_dir = self.backup_dir / username
                user_backup_dir.mkdir(exist_ok=True)
                
                # 备份文件
                backup_filename = f"{backup_time}_{latest_file.name}"
                backup_path = user_backup_dir / backup_filename
                
                try:
                    shutil.copy2(latest_file, backup_path)
                    
                    # 提取基准数据
                    usernames, contacts = self.get_contact_usernames_from_file(latest_file)
                    self.user_baselines[username] = {
                        'usernames': usernames,
                        'contacts': contacts,
                        'file_path': str(latest_file),
                        'backup_path': str(backup_path),
                        'backup_time': backup_time,
                        'contact_count': len(contacts)
                    }
                    
                    print(f"   ✅ {username}: 备份 {len(contacts)} 个联系人")
                    
                except Exception as e:
                    print(f"   ❌ {username}: 备份失败 - {e}")
        
        print(f"📊 已备份 {len(self.user_baselines)} 个用户的联系人数据")
    
    def check_for_contact_changes(self):
        """检查联系人变化"""
        if not self.user_baselines:
            print("⚠️  没有基准数据，建立初始基准...")
            self.backup_current_contacts()
            print("✅ 基准数据已建立，下次检查时将能检测到变化")
            return False

        changes_detected = False
        change_details = []

        user_folders = self.get_all_users()

        for user_folder in user_folders:
            username = user_folder.name

            if username not in self.user_baselines:
                continue

            # 查找当前的联系人文件
            contact_files = [f for f in user_folder.glob("*.json")
                            if not f.name.startswith(("new_contacts", "ip_change"))]

            if not contact_files:
                continue

            latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
            baseline = self.user_baselines[username]

            # 检查文件是否有更新
            if str(latest_file) == baseline['file_path']:
                current_mtime = latest_file.stat().st_mtime
                baseline_mtime = Path(baseline['backup_path']).stat().st_mtime
                if current_mtime <= baseline_mtime + 1:
                    continue  # 文件没有更新

            # 文件有更新，进行对比
            current_usernames, current_contacts = self.get_contact_usernames_from_file(latest_file)
            baseline_usernames = baseline['usernames']

            added_usernames = current_usernames - baseline_usernames
            removed_usernames = baseline_usernames - current_usernames

            if added_usernames or removed_usernames:
                changes_detected = True

                current_count = len(current_contacts)
                baseline_count = baseline['contact_count']
                net_change = current_count - baseline_count

                change_info = f"📊 {username}: {baseline_count} → {current_count} ({net_change:+d})"
                change_details.append(change_info)

                # 处理新增联系人
                if added_usernames:
                    added_contacts = [c for c in current_contacts if c.get("UserName") in added_usernames]
                    change_details.append(f"   ➕ 新增: {len(added_contacts)} 个")

                    # 记录新增联系人日志
                    self.record_new_contacts(user_folder, username, added_contacts)

                    # 打印详细信息
                    change_details.append(f"   📋 新增联系人详细信息：")
                    for contact in added_contacts:
                        username_ = contact.get("UserName", "")
                        nickname = contact.get("NickName", "")
                        alias = contact.get("Alias", "")
                        remark = contact.get("Remark", "")
                        change_details.append(
                            f"      - 昵称: {nickname} | 备注: {remark} | 微信号: {alias} | UserName: {username_}"
                        )

                # 处理删除联系人
                if removed_usernames:
                    change_details.append(f"   ➖ 删除: {len(removed_usernames)} 个")

                # ✅ 立即备份当前联系人文件，更新基准
                new_backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                user_backup_dir = self.backup_dir / username
                user_backup_dir.mkdir(exist_ok=True)
                new_backup_name = f"{new_backup_time}_{latest_file.name}"
                new_backup_path = user_backup_dir / new_backup_name

                try:
                    shutil.copy2(latest_file, new_backup_path)
                except Exception as e:
                    print(f"   ❌ 更新备份失败: {e}")
                    new_backup_path = baseline['backup_path']

                # ✅ 更新基准数据
                self.user_baselines[username] = {
                    'usernames': current_usernames,
                    'contacts': current_contacts,
                    'file_path': str(latest_file),
                    'backup_path': str(new_backup_path),
                    'backup_time': new_backup_time,
                    'contact_count': current_count
                }

        if changes_detected:
            print(f"\n🔔 检测到联系人变化 - {datetime.now().strftime('%H:%M:%S')}")
            for detail in change_details:
                print(f"   {detail}")

        return changes_detected

        """检查联系人变化"""
        if not self.user_baselines:
            print("⚠️  没有基准数据，建立初始基准...")
            self.backup_current_contacts()
            print("✅ 基准数据已建立，下次检查时将能检测到变化")
            return False
        
        changes_detected = False
        change_details = []
        
        user_folders = self.get_all_users()
        
        for user_folder in user_folders:
            username = user_folder.name
            
            if username not in self.user_baselines:
                continue
            
            # 查找当前的联系人文件
            contact_files = [f for f in user_folder.glob("*.json") 
                           if not f.name.startswith(("new_contacts", "ip_change"))]
            
            if not contact_files:
                continue
            
            latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
            baseline = self.user_baselines[username]
            
            # 检查文件是否有更新
            if str(latest_file) == baseline['file_path']:
                # 检查文件修改时间
                current_mtime = latest_file.stat().st_mtime
                baseline_mtime = Path(baseline['backup_path']).stat().st_mtime
                
                if current_mtime <= baseline_mtime + 1:  # 允许1秒误差
                    continue  # 文件没有更新
            
            # 文件有更新，进行对比
            current_usernames, current_contacts = self.get_contact_usernames_from_file(latest_file)
            baseline_usernames = baseline['usernames']
            
            # 计算变化
            added_usernames = current_usernames - baseline_usernames
            removed_usernames = baseline_usernames - current_usernames
            
            if added_usernames or removed_usernames:
                changes_detected = True
                
                # 记录变化详情
                current_count = len(current_contacts)
                baseline_count = baseline['contact_count']
                net_change = current_count - baseline_count
                
                change_info = f"📊 {username}: {baseline_count} → {current_count} ({net_change:+d})"
                change_details.append(change_info)
                
                # 处理新增联系人
                if added_usernames:
                    added_contacts = [c for c in current_contacts if c.get("UserName") in added_usernames]
                    change_details.append(f"   ➕ 新增: {len(added_contacts)} 个")
                    
                    # 记录新增联系人到用户日志
                    self.record_new_contacts(user_folder, username, added_contacts)
                    
                    # 显示新增联系人示例
                    # 显示新增联系人详细信息
                    change_details.append(f"   📋 新增联系人详细信息：")
                    for contact in added_contacts:
                        username = contact.get("UserName", "")
                        nickname = contact.get("NickName", "")
                        alias = contact.get("Alias", "")
                        remark = contact.get("Remark", "")
                        
                        change_details.append(f"      - 昵称: {nickname} | 备注: {remark} | 微信号: {alias} | UserName: {username}")

                # 处理删除的联系人
                if removed_usernames:
                    change_details.append(f"   ➖ 删除: {len(removed_usernames)} 个")
                
                # 更新基准数据
                self.user_baselines[username] = {
                    'usernames': current_usernames,
                    'contacts': current_contacts,
                    'file_path': str(latest_file),
                    'backup_path': baseline['backup_path'],  # 保持原备份路径
                    'backup_time': datetime.now().strftime("%Y%m%d_%H%M%S"),
                    'contact_count': current_count
                }
        
        if changes_detected:
            print(f"\n🔔 检测到联系人变化 - {datetime.now().strftime('%H:%M:%S')}")
            for detail in change_details:
                print(f"   {detail}")
        
        return changes_detected
    
    def record_new_contacts(self, user_folder, username, new_contacts):
        """记录新增联系人到用户的new_contacts_log.json"""
        if not new_contacts:
            return
        
        new_contacts_log_file = user_folder / "new_contacts_log.json"
        
        try:
            # 读取现有记录
            if new_contacts_log_file.exists():
                with open(new_contacts_log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            else:
                log_data = {
                    "user": username,
                    "created_time": datetime.now().isoformat(),
                    "description": "新增联系人记录文件",
                    "records": []
                }
            
            # 创建新增记录
            new_record = {
                "timestamp": datetime.now().isoformat(),
                "detection_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "count": len(new_contacts),
                "detection_method": "file_comparison",
                "contacts": []
            }
            
            # 只添加4个关键字段
            for contact in new_contacts:
                contact_info = {
                    "UserName": contact.get("UserName", ""),
                    "Alias": contact.get("Alias", ""),
                    "Remark": contact.get("Remark", ""),
                    "NickName": contact.get("NickName", "")
                }
                new_record["contacts"].append(contact_info)
            
            # 添加到记录列表
            log_data["records"].append(new_record)
            log_data["last_updated"] = datetime.now().isoformat()
            
            # 保存到文件
            with open(new_contacts_log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            print(f"📝 已记录 {len(new_contacts)} 个新增联系人到 {username}/new_contacts_log.json")
            
        except Exception as e:
            print(f"❌ 记录新增联系人失败: {e}")
    
    def monitor_with_backup(self, interval=30):
        """带备份的监控模式"""
        print(f"🔍 开始准确监控联系人变化 (每{interval}秒检查一次)")
        print("💡 监控原理: 文件覆盖前后对比")
        print("按 Ctrl+C 停止监控")
        
        # 初始备份
        self.backup_current_contacts()
        self.monitoring = True
        
        try:
            while self.monitoring:
                # 检查变化
                changes_detected = self.check_for_contact_changes()
                
                if not changes_detected:
                    print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - 监控中，暂无变化")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print(f"\n⏹️  监控已停止 - {datetime.now().strftime('%H:%M:%S')}")
            self.monitoring = False
    
    def manual_check(self):
        """手动检查一次变化"""
        print("🔍 手动检查联系人变化...")
        
        if not self.user_baselines:
            print("📊 首次检查，建立基准数据...")
            self.backup_current_contacts()
            print("✅ 基准数据已建立，下次检查时将能检测到变化")
        else:
            changes_detected = self.check_for_contact_changes()
            if not changes_detected:
                print("📊 未检测到联系人变化")
    
    def run_interactive(self):
        """交互式运行"""
        while True:
            print("\n" + "="*50)
            print("📱 准确联系人变化监控工具")
            print("="*50)
            print("1. 手动检查变化")
            print("2. 开始实时监控")
            print("3. 重新建立基准数据")
            print("4. 显示当前基准状态")
            print("5. 退出")
            
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                self.manual_check()
            elif choice == '2':
                interval = input("请输入监控间隔(秒，默认30): ").strip()
                try:
                    interval = int(interval) if interval else 30
                except ValueError:
                    interval = 30
                self.monitor_with_backup(interval)
            elif choice == '3':
                self.backup_current_contacts()
            elif choice == '4':
                self.display_baseline_status()
            elif choice == '5':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重试")
    
    def display_baseline_status(self):
        """显示当前基准数据状态"""
        if not self.user_baselines:
            print("📊 暂无基准数据")
            return
        
        print(f"\n📊 当前基准数据状态:")
        print(f"{'用户名':<12} {'联系人数':<8} {'备份时间':<16}")
        print("-" * 40)
        
        for username, baseline in self.user_baselines.items():
            print(f"{username:<12} {baseline['contact_count']:<8} {baseline['backup_time']:<16}")

def main():
    monitor = AccurateContactMonitor()
    
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == 'check':
            monitor.manual_check()
        elif sys.argv[1] == 'monitor':
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            monitor.monitor_with_backup(interval)
        else:
            print("用法: python accurate_contact_monitor.py [check|monitor] [间隔秒数]")
    else:
        monitor.run_interactive()

if __name__ == "__main__":
    main()
