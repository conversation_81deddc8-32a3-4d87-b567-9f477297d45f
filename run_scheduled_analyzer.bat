@echo off
echo 启动定时联系人分析工具...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 安装依赖
echo 正在安装依赖包...
pip install -r requirements.txt

REM 检查data目录是否存在
if not exist "data" (
    echo 错误: 未找到data目录，请确保data目录存在并包含JSON文件
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist "history" mkdir history
if not exist "reports" mkdir reports

echo.
echo 选择运行模式:
echo 1. 启动定时任务 (每天09:00和21:00自动运行)
echo 2. 手动运行一次分析
echo 3. 运行传统的重复联系人分析
echo.

set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 启动定时任务模式...
    echo 程序将每天在09:00和21:00自动运行分析
    echo 按Ctrl+C可以停止定时任务
    echo.
    python find_duplicate_contacts.py --schedule
) else if "%choice%"=="2" (
    echo.
    echo 手动运行分析...
    python find_duplicate_contacts.py --manual
) else if "%choice%"=="3" (
    echo.
    echo 运行传统重复联系人分析...
    python find_duplicate_contacts.py
) else (
    echo 无效选择，退出程序
)

echo.
pause
