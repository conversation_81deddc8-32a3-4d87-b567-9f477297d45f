"""
测试聊天记录API功能
"""

import json
import requests
import re
from pathlib import Path

def test_chatlog_api():
    """测试聊天记录API"""
    print("🧪 测试聊天记录API...")
    
    # 读取小熊的联系人文件
    xiaoxiong_file = Path("data/小熊/192_168_2_51_小熊.json")
    
    if not xiaoxiong_file.exists():
        print("❌ 找不到小熊的联系人文件")
        return
    
    with open(xiaoxiong_file, 'r', encoding='utf-8') as f:
        contacts = json.load(f)
    
    print(f"📊 小熊总联系人数: {len(contacts)}")
    
    # 过滤掉明显的群聊和企业微信，测试真正的个人联系人
    personal_contacts = [c for c in contacts
                        if not c.get("UserName", "").endswith("@chatroom")
                        and "@openim" not in c.get("UserName", "")
                        and not c.get("UserName", "").startswith("gh_")]

    print(f"📊 过滤后的个人联系人数: {len(personal_contacts)}")
    test_contacts = personal_contacts[:10]  # 只测试前10个个人联系人
    
    base_url = "http://127.0.0.1:5030/api/v1/chatlog"
    time_range = "2023-01-01~2025-07-07"
    
    for i, contact in enumerate(test_contacts, 1):
        username = contact.get("UserName", "")
        nickname = contact.get("NickName", "") or "无昵称"
        
        print(f"\n{i:2d}. {nickname} ({username})")
        
        # 测试不同的talker参数
        talkers_to_try = [username, nickname]
        alias = contact.get("Alias", "")
        if alias:
            talkers_to_try.append(alias)
        
        for talker in talkers_to_try:
            try:
                params = {"time": time_range, "talker": talker}
                response = requests.get(base_url, params=params, timeout=5)
                response.raise_for_status()
                
                chatlog_text = response.text
                
                if chatlog_text and len(chatlog_text.strip()) > 0:
                    # 分析参与者
                    participant_count, participants = analyze_chatlog_participants(chatlog_text)
                    message_lines = len([line for line in chatlog_text.split('\n') if line.strip()])
                    
                    print(f"    ✅ {talker}: {message_lines} 行, {participant_count} 个参与者")
                    
                    if participant_count >= 3:
                        print(f"    🚨 可能是群聊! 参与者: {participants[:5]}")
                    elif participant_count > 0:
                        print(f"    👤 个人聊天, 参与者: {participants}")
                    
                    break  # 找到有效数据就停止尝试其他talker
                else:
                    print(f"    ⚪ {talker}: 无聊天记录")
                    
            except Exception as e:
                print(f"    ❌ {talker}: 请求失败 - {str(e)}")

def analyze_chatlog_participants(chatlog_text):
    """分析聊天记录中的参与者数量"""
    if not chatlog_text or not isinstance(chatlog_text, str):
        return 0, []
    
    participants = set()
    lines = chatlog_text.strip().split('\n')
    
    # 分析每行聊天记录
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 查找发送者模式：通常是 "发送者名称 时间戳" 或 "发送者名称 日期 时间"
        # 例如: "我 2023-10-14 16:27:26" 或 "张三 2024-01-01 10:30:00"
        sender_match = re.match(r'^([^\s]+)\s+\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', line)
        if sender_match:
            sender = sender_match.group(1)
            participants.add(sender)
        
        # 查找@提及的用户
        mentions = re.findall(r'@([^\s@]+)', line)
        for mention in mentions:
            participants.add(mention)
        
        # 查找群聊中常见的模式，如 "XXX说:" 或 "XXX："
        speaker_match = re.search(r'([^\s]+)[说：:]', line)
        if speaker_match:
            speaker = speaker_match.group(1)
            participants.add(speaker)
    
    # 过滤掉一些明显不是人名的参与者
    filtered_participants = set()
    for participant in participants:
        # 过滤掉太短或明显不是人名的
        if len(participant) >= 1 and not participant.isdigit():
            filtered_participants.add(participant)
    
    return len(filtered_participants), list(filtered_participants)

if __name__ == "__main__":
    test_chatlog_api()
