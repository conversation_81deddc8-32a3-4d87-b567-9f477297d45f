{"analysis_info": {"timestamp": "2025-07-08T11:23:28.032868", "analysis_time": "2025-07-08 11:23:28", "total_users": 3, "total_duplicate_contacts": 3}, "user_summary": {"测试用户A": {"files_count": 1, "total_contacts": 3, "files": ["192_168_1_100_测试用户<PERSON><PERSON>json"]}, "测试用户B": {"files_count": 1, "total_contacts": 4, "files": ["192_168_1_100_测试用户<PERSON><PERSON>json"]}, "测试用户C": {"files_count": 1, "total_contacts": 3, "files": ["192_168_1_100_测试用户C<PERSON>json"]}}, "duplicate_contacts": [{"contact_key": "filehelper", "username": "filehelper", "nickname": "文件传输助手", "alias": "", "remark": "", "appears_in_users": ["测试用户A", "测试用户C", "测试用户B"], "appears_in_files": ["测试用户A/192_168_1_100_测试用户A.json", "测试用户B/192_168_1_100_测试用户B.json", "测试用户C/192_168_1_100_测试用户C.json"], "user_count": 3, "file_count": 3, "full_contact_data": {"UserName": "filehelper", "NickName": "文件传输助手", "Alias": "", "Remark": ""}, "all_appearances": [{"user": "测试用户A", "file": "192_168_1_100_测试用户<PERSON><PERSON>json", "contact": {"UserName": "filehelper", "NickName": "文件传输助手", "Alias": "", "Remark": ""}}, {"user": "测试用户B", "file": "192_168_1_100_测试用户<PERSON><PERSON>json", "contact": {"UserName": "filehelper", "NickName": "文件传输助手", "Alias": "", "Remark": ""}}, {"user": "测试用户C", "file": "192_168_1_100_测试用户C<PERSON>json", "contact": {"UserName": "filehelper", "NickName": "文件传输助手", "Alias": "", "Remark": ""}}]}, {"contact_key": "common_contact_001", "username": "common_contact_001", "nickname": "共同联系人1", "alias": "", "remark": "这是共同联系人", "appears_in_users": ["测试用户A", "测试用户B"], "appears_in_files": ["测试用户A/192_168_1_100_测试用户A.json", "测试用户B/192_168_1_100_测试用户B.json"], "user_count": 2, "file_count": 2, "full_contact_data": {"UserName": "common_contact_001", "NickName": "共同联系人1", "Alias": "", "Remark": "这是共同联系人"}, "all_appearances": [{"user": "测试用户A", "file": "192_168_1_100_测试用户<PERSON><PERSON>json", "contact": {"UserName": "common_contact_001", "NickName": "共同联系人1", "Alias": "", "Remark": "这是共同联系人"}}, {"user": "测试用户B", "file": "192_168_1_100_测试用户<PERSON><PERSON>json", "contact": {"UserName": "common_contact_001", "NickName": "共同联系人1", "Alias": "", "Remark": "这是共同联系人"}}]}, {"contact_key": "weixin", "username": "weixin", "nickname": "微信团队", "alias": "", "remark": "", "appears_in_users": ["测试用户C", "测试用户B"], "appears_in_files": ["测试用户B/192_168_1_100_测试用户B.json", "测试用户C/192_168_1_100_测试用户C.json"], "user_count": 2, "file_count": 2, "full_contact_data": {"UserName": "weixin", "NickName": "微信团队", "Alias": "", "Remark": ""}, "all_appearances": [{"user": "测试用户B", "file": "192_168_1_100_测试用户<PERSON><PERSON>json", "contact": {"UserName": "weixin", "NickName": "微信团队", "Alias": "", "Remark": ""}}, {"user": "测试用户C", "file": "192_168_1_100_测试用户C<PERSON>json", "contact": {"UserName": "weixin", "NickName": "微信团队", "Alias": "", "Remark": ""}}]}]}