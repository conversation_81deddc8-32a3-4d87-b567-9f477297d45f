import json
import os
from collections import defaultdict
from datetime import datetime, timedelta
import csv
import time
import threading
import schedule
import logging
from pathlib import Path

class ContactDuplicateFinder:
    def __init__(self, data_dir="data"):
        self.data_dir = data_dir
        self.all_contacts = {}  # 存储所有联系人 {filename: [contacts]}
        self.duplicate_contacts = defaultdict(list)  # 存储重复联系人 {contact_key: [file_list]}
        
    def load_all_contacts(self):
        """加载所有JSON文件中的联系人"""
        print(f"正在扫描目录: {self.data_dir}")
        
        if not os.path.exists(self.data_dir):
            print(f"错误: 目录 {self.data_dir} 不存在")
            return False
            
        json_files = [f for f in os.listdir(self.data_dir) if f.endswith('.json')]
        
        if not json_files:
            print(f"错误: 在 {self.data_dir} 目录中没有找到JSON文件")
            return False
            
        print(f"找到 {len(json_files)} 个JSON文件")
        
        for filename in json_files:
            filepath = os.path.join(self.data_dir, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    contacts = json.load(f)
                    
                if isinstance(contacts, list):
                    self.all_contacts[filename] = contacts
                    print(f"✅ 加载 {filename}: {len(contacts)} 个联系人")
                else:
                    print(f"⚠️  跳过 {filename}: 不是联系人列表格式")
                    
            except Exception as e:
                print(f"❌ 加载 {filename} 失败: {str(e)}")
                
        print(f"总共加载了 {len(self.all_contacts)} 个文件")
        return len(self.all_contacts) > 0
        
    def generate_contact_key(self, contact):
        """生成联系人的唯一标识符"""
        # 安全获取字段值，处理None值
        def safe_get(field_name):
            value = contact.get(field_name)
            return value.strip() if value and isinstance(value, str) else ""

        # 使用UserName作为主要标识符，因为它是唯一的
        username = safe_get("UserName")
        if username:
            return username

        # 如果没有UserName，尝试使用其他字段组合
        nickname = safe_get("NickName")
        alias = safe_get("Alias")
        remark = safe_get("Remark")

        # 创建组合键
        key_parts = []
        if nickname:
            key_parts.append(f"nick:{nickname}")
        if alias:
            key_parts.append(f"alias:{alias}")
        if remark:
            key_parts.append(f"remark:{remark}")

        return "|".join(key_parts) if key_parts else "unknown"
        
    def find_duplicates(self):
        """查找重复的联系人"""
        print("\n开始查找重复联系人...")
        
        # 创建联系人索引 {contact_key: [(filename, contact_data)]}
        contact_index = defaultdict(list)
        
        for filename, contacts in self.all_contacts.items():
            for contact in contacts:
                contact_key = self.generate_contact_key(contact)
                contact_index[contact_key].append((filename, contact))
                
        # 找出出现在多个文件中的联系人
        for contact_key, file_contact_list in contact_index.items():
            if len(file_contact_list) > 1:  # 出现在多个文件中
                files_with_contact = [item[0] for item in file_contact_list]
                contact_data = file_contact_list[0][1]  # 取第一个作为代表
                
                self.duplicate_contacts[contact_key] = {
                    'contact_data': contact_data,
                    'files': files_with_contact,
                    'count': len(file_contact_list)
                }
                
        print(f"找到 {len(self.duplicate_contacts)} 个重复联系人")
        return len(self.duplicate_contacts)
        
    def save_duplicates_to_json(self, output_file="duplicate_contacts.json"):
        """将重复联系人保存为JSON文件"""
        if not self.duplicate_contacts:
            print("没有重复联系人需要保存")
            return False
            
        # 准备输出数据
        output_data = {
            "scan_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_files_scanned": len(self.all_contacts),
            "duplicate_contacts_count": len(self.duplicate_contacts),
            "duplicate_contacts": []
        }
        
        for contact_key, duplicate_info in self.duplicate_contacts.items():
            contact_data = duplicate_info['contact_data']
            files = duplicate_info['files']
            
            duplicate_entry = {
                "contact_key": contact_key,
                "username": contact_data.get("UserName", ""),
                "nickname": contact_data.get("NickName", ""),
                "alias": contact_data.get("Alias", ""),
                "remark": contact_data.get("Remark", ""),
                "appears_in_files": files,
                "file_count": len(files),
                "full_contact_data": contact_data
            }
            
            output_data["duplicate_contacts"].append(duplicate_entry)
            
        # 按出现次数排序
        output_data["duplicate_contacts"].sort(key=lambda x: x["file_count"], reverse=True)
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 重复联系人已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {str(e)}")
            return False
            
    def save_duplicates_to_csv(self, output_file="duplicate_contacts.csv"):
        """将重复联系人保存为CSV文件"""
        if not self.duplicate_contacts:
            print("没有重复联系人需要保存")
            return False
            
        try:
            with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                
                # 写入表头
                headers = [
                    "联系人标识", "用户名", "昵称", "别名", "备注", 
                    "出现次数", "出现的文件列表"
                ]
                writer.writerow(headers)
                
                # 按出现次数排序
                sorted_duplicates = sorted(
                    self.duplicate_contacts.items(), 
                    key=lambda x: x[1]['count'], 
                    reverse=True
                )
                
                for contact_key, duplicate_info in sorted_duplicates:
                    contact_data = duplicate_info['contact_data']
                    files = duplicate_info['files']
                    
                    row = [
                        contact_key,
                        contact_data.get("UserName", ""),
                        contact_data.get("NickName", ""),
                        contact_data.get("Alias", ""),
                        contact_data.get("Remark", ""),
                        len(files),
                        "; ".join(files)
                    ]
                    writer.writerow(row)
                    
            print(f"✅ 重复联系人已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"❌ 保存CSV失败: {str(e)}")
            return False
            
    def print_summary(self):
        """打印统计摘要"""
        print("\n" + "="*60)
        print("重复联系人分析摘要")
        print("="*60)
        
        print(f"扫描的文件数量: {len(self.all_contacts)}")
        
        total_contacts = sum(len(contacts) for contacts in self.all_contacts.values())
        print(f"总联系人数量: {total_contacts}")
        
        print(f"重复联系人数量: {len(self.duplicate_contacts)}")
        
        if self.duplicate_contacts:
            print("\n重复次数最多的联系人:")
            sorted_duplicates = sorted(
                self.duplicate_contacts.items(), 
                key=lambda x: x[1]['count'], 
                reverse=True
            )
            
            for i, (contact_key, duplicate_info) in enumerate(sorted_duplicates[:10]):
                contact_data = duplicate_info['contact_data']
                files = duplicate_info['files']
                
                nickname = contact_data.get("NickName", "无昵称")
                username = contact_data.get("UserName", "无用户名")
                
                print(f"{i+1:2d}. {nickname} ({username})")
                print(f"    出现次数: {len(files)}")
                print(f"    出现文件: {', '.join(files)}")
                print()
                
        print("="*60)
        
    def run_analysis(self):
        """运行完整的重复联系人分析"""
        print("开始重复联系人分析...")
        print("="*60)
        
        # 1. 加载所有联系人
        if not self.load_all_contacts():
            return False
            
        # 2. 查找重复联系人
        duplicate_count = self.find_duplicates()
        
        if duplicate_count == 0:
            print("没有找到重复的联系人")
            return True
            
        # 3. 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = f"duplicate_contacts_{timestamp}.json"
        csv_file = f"duplicate_contacts_{timestamp}.csv"
        
        self.save_duplicates_to_json(json_file)
        self.save_duplicates_to_csv(csv_file)
        
        # 4. 打印摘要
        self.print_summary()
        
        return True

class ScheduledContactAnalyzer:
    def __init__(self, data_dir="data", history_dir="history", reports_dir="reports"):
        self.data_dir = data_dir
        self.history_dir = history_dir
        self.reports_dir = reports_dir
        self.finder = ContactDuplicateFinder(data_dir)

        # 创建必要的目录
        Path(self.history_dir).mkdir(exist_ok=True)
        Path(self.reports_dir).mkdir(exist_ok=True)

        # 设置日志
        self.setup_logging()

        # 上次分析的数据
        self.last_analysis = None

    def setup_logging(self):
        """设置日志记录"""
        log_file = os.path.join(self.reports_dir, "contact_analyzer.log")
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def save_current_state(self):
        """保存当前联系人状态到历史记录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        history_file = os.path.join(self.history_dir, f"contacts_state_{timestamp}.json")

        try:
            # 加载当前所有联系人
            if not self.finder.load_all_contacts():
                self.logger.error("无法加载联系人数据")
                return None

            # 创建当前状态快照
            current_state = {
                "timestamp": timestamp,
                "scan_time": datetime.now().isoformat(),
                "files": {}
            }

            for filename, contacts in self.finder.all_contacts.items():
                # 为每个文件创建联系人摘要
                contact_summary = []
                for contact in contacts:
                    summary = {
                        "username": contact.get("UserName", ""),
                        "nickname": contact.get("NickName", ""),
                        "alias": contact.get("Alias", ""),
                        "remark": contact.get("Remark", "")
                    }
                    contact_summary.append(summary)

                current_state["files"][filename] = {
                    "count": len(contacts),
                    "contacts": contact_summary
                }

            # 保存状态
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(current_state, f, ensure_ascii=False, indent=2)

            self.logger.info(f"当前状态已保存到: {history_file}")
            return current_state

        except Exception as e:
            self.logger.error(f"保存当前状态失败: {str(e)}")
            return None

    def load_last_state(self):
        """加载最近的历史状态"""
        try:
            history_files = [f for f in os.listdir(self.history_dir) if f.startswith("contacts_state_") and f.endswith(".json")]
            if not history_files:
                self.logger.info("没有找到历史状态文件")
                return None

            # 按时间排序，获取最新的
            history_files.sort(reverse=True)
            latest_file = os.path.join(self.history_dir, history_files[0])

            with open(latest_file, 'r', encoding='utf-8') as f:
                last_state = json.load(f)

            self.logger.info(f"加载历史状态: {latest_file}")
            return last_state

        except Exception as e:
            self.logger.error(f"加载历史状态失败: {str(e)}")
            return None

    def compare_states(self, current_state, last_state):
        """对比当前状态和历史状态，找出新增联系人"""
        if not last_state:
            self.logger.info("没有历史状态，跳过对比")
            return {
                "current_timestamp": current_state["timestamp"],
                "last_timestamp": None,
                "new_contacts": {},
                "new_files": list(current_state["files"].keys()),
                "removed_files": [],
                "summary": {
                    "total_new_contacts": 0,
                    "files_with_new_contacts": 0,
                    "new_files_count": len(current_state["files"]),
                    "removed_files_count": 0
                }
            }

        comparison_result = {
            "current_timestamp": current_state["timestamp"],
            "last_timestamp": last_state["timestamp"],
            "new_contacts": {},  # 每个文件的新增联系人
            "new_files": [],     # 新增的文件
            "removed_files": [], # 删除的文件
            "summary": {}
        }

        current_files = set(current_state["files"].keys())
        last_files = set(last_state["files"].keys())

        # 找出新增和删除的文件
        comparison_result["new_files"] = list(current_files - last_files)
        comparison_result["removed_files"] = list(last_files - current_files)

        # 对比每个文件的联系人变化
        for filename in current_files:
            if filename not in last_state["files"]:
                # 新文件，所有联系人都是新增的
                comparison_result["new_contacts"][filename] = current_state["files"][filename]["contacts"]
            else:
                # 现有文件，对比联系人
                current_contacts = current_state["files"][filename]["contacts"]
                last_contacts = last_state["files"][filename]["contacts"]

                # 创建联系人集合用于对比
                def create_contact_set(contacts):
                    return {contact["username"] for contact in contacts if contact["username"]}

                current_usernames = create_contact_set(current_contacts)
                last_usernames = create_contact_set(last_contacts)

                # 找出新增的联系人
                new_usernames = current_usernames - last_usernames
                new_contacts = [c for c in current_contacts if c["username"] in new_usernames]

                if new_contacts:
                    comparison_result["new_contacts"][filename] = new_contacts

        # 生成摘要
        total_new_contacts = sum(len(contacts) for contacts in comparison_result["new_contacts"].values())
        comparison_result["summary"] = {
            "total_new_contacts": total_new_contacts,
            "files_with_new_contacts": len(comparison_result["new_contacts"]),
            "new_files_count": len(comparison_result["new_files"]),
            "removed_files_count": len(comparison_result["removed_files"])
        }

        return comparison_result

    def generate_comparison_report(self, comparison_result, duplicate_result):
        """生成对比分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # JSON报告
        json_report = os.path.join(self.reports_dir, f"daily_analysis_{timestamp}.json")
        report_data = {
            "analysis_time": datetime.now().isoformat(),
            "comparison": comparison_result,
            "duplicates": duplicate_result,
            "summary": {
                "new_contacts_total": comparison_result["summary"]["total_new_contacts"],
                "duplicate_contacts_total": len(duplicate_result.get("duplicate_contacts", [])),
                "files_analyzed": len(self.finder.all_contacts)
            }
        }

        try:
            with open(json_report, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"JSON报告已生成: {json_report}")
        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {str(e)}")

        # CSV报告 - 新增联系人
        csv_new_contacts = os.path.join(self.reports_dir, f"new_contacts_{timestamp}.csv")
        try:
            with open(csv_new_contacts, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                writer.writerow(["文件名", "用户名", "昵称", "别名", "备注"])

                for filename, contacts in comparison_result["new_contacts"].items():
                    for contact in contacts:
                        writer.writerow([
                            filename,
                            contact.get("username", ""),
                            contact.get("nickname", ""),
                            contact.get("alias", ""),
                            contact.get("remark", "")
                        ])

            self.logger.info(f"新增联系人CSV报告已生成: {csv_new_contacts}")
        except Exception as e:
            self.logger.error(f"生成新增联系人CSV报告失败: {str(e)}")

        return json_report

    def run_scheduled_analysis(self):
        """运行定时分析"""
        self.logger.info("="*60)
        self.logger.info("开始定时联系人分析")
        self.logger.info("="*60)

        try:
            # 1. 加载历史状态
            last_state = self.load_last_state()

            # 2. 保存当前状态
            current_state = self.save_current_state()
            if not current_state:
                self.logger.error("无法获取当前状态，分析终止")
                return

            # 3. 对比状态变化
            comparison_result = self.compare_states(current_state, last_state)

            # 4. 运行重复联系人分析
            duplicate_count = self.finder.find_duplicates()

            # 准备重复联系人结果
            duplicate_result = {
                "duplicate_contacts": []
            }

            for contact_key, duplicate_info in self.finder.duplicate_contacts.items():
                contact_data = duplicate_info['contact_data']
                files = duplicate_info['files']

                duplicate_entry = {
                    "contact_key": contact_key,
                    "username": contact_data.get("UserName", ""),
                    "nickname": contact_data.get("NickName", ""),
                    "appears_in_files": files,
                    "file_count": len(files)
                }
                duplicate_result["duplicate_contacts"].append(duplicate_entry)

            # 5. 生成报告
            report_file = self.generate_comparison_report(comparison_result, duplicate_result)

            # 6. 打印摘要
            self.print_analysis_summary(comparison_result, duplicate_result)

            self.logger.info("定时分析完成")

        except Exception as e:
            self.logger.error(f"定时分析失败: {str(e)}")

    def print_analysis_summary(self, comparison_result, duplicate_result):
        """打印分析摘要"""
        summary = comparison_result["summary"]

        self.logger.info("\n" + "="*60)
        self.logger.info("定时分析摘要")
        self.logger.info("="*60)

        self.logger.info(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"分析文件数: {len(self.finder.all_contacts)}")

        # 新增联系人摘要
        self.logger.info(f"\n📈 新增联系人情况:")
        self.logger.info(f"  总新增联系人: {summary['total_new_contacts']}")
        self.logger.info(f"  有新增的文件数: {summary['files_with_new_contacts']}")
        self.logger.info(f"  新增文件数: {summary['new_files_count']}")

        if comparison_result["new_files"]:
            self.logger.info(f"  新增文件: {', '.join(comparison_result['new_files'])}")

        # 重复联系人摘要
        duplicate_count = len(duplicate_result["duplicate_contacts"])
        self.logger.info(f"\n🔄 重复联系人情况:")
        self.logger.info(f"  重复联系人总数: {duplicate_count}")

        if duplicate_count > 0:
            # 显示重复次数最多的前5个
            sorted_duplicates = sorted(
                duplicate_result["duplicate_contacts"],
                key=lambda x: x["file_count"],
                reverse=True
            )

            self.logger.info("  重复次数最多的联系人:")
            for i, dup in enumerate(sorted_duplicates[:5]):
                nickname = dup["nickname"] or "无昵称"
                self.logger.info(f"    {i+1}. {nickname} - 出现在{dup['file_count']}个文件中")

        self.logger.info("="*60)

    def start_scheduler(self):
        """启动定时任务调度器"""
        self.logger.info("启动联系人分析定时任务...")
        self.logger.info("每天运行2次: 09:00 和 21:00")

        # 设置定时任务
        schedule.every().day.at("09:00").do(self.run_scheduled_analysis)
        schedule.every().day.at("21:00").do(self.run_scheduled_analysis)

        # 可选：立即运行一次
        self.logger.info("立即运行一次分析...")
        self.run_scheduled_analysis()

        # 开始调度循环
        self.logger.info("定时任务已启动，等待调度...")
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次

    def run_manual_analysis(self):
        """手动运行分析（用于测试）"""
        self.logger.info("手动运行联系人分析...")
        self.run_scheduled_analysis()

def main():
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--schedule":
        # 定时模式
        analyzer = ScheduledContactAnalyzer()
        try:
            analyzer.start_scheduler()
        except KeyboardInterrupt:
            analyzer.logger.info("定时任务已停止")
    elif len(sys.argv) > 1 and sys.argv[1] == "--manual":
        # 手动分析模式
        analyzer = ScheduledContactAnalyzer()
        analyzer.run_manual_analysis()
    else:
        # 原始的单次分析模式
        finder = ContactDuplicateFinder()

        try:
            success = finder.run_analysis()
            if success:
                print("\n分析完成！")
            else:
                print("\n分析失败！")
        except KeyboardInterrupt:
            print("\n\n用户中断操作")
        except Exception as e:
            print(f"\n发生错误: {str(e)}")

if __name__ == "__main__":
    main()
