<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信消息发送</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], textarea, select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
        }
        
        .btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        
        .btn:hover {
            background-color: #45a049;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .example {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .example h4 {
            margin-top: 0;
            color: #495057;
        }
        
        .example pre {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .nav-links a {
            color: #4CAF50;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }

        .ai-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .ai-section h3 {
            margin-top: 0;
            color: white;
        }

        .generated-message {
            background-color: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }

        .generated-message.loading {
            background-color: #fff3cd;
            border-color: #ffc107;
        }

        .ai-loading {
            display: none;
            text-align: center;
            margin: 15px 0;
            color: white;
        }

        .ai-loading::after {
            content: '';
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        .message-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-style: italic;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-links">
            <a href="/">← 返回首页</a>
            <a href="/send_message">微信消息发送</a>
        </div>
        
        <h1>🤖 智能微信消息发送工具</h1>

        <form id="messageForm">
            <div class="form-group">
                <label for="target_host">目标主机地址:</label>
                <input type="text" id="target_host" name="target_host" value="localhost:8000"
                       placeholder="例如: localhost:8000 或 *************:8000">
                <small style="color: #666;">微信API服务器的地址和端口</small>
            </div>

            <div class="form-group">
                <label for="receiver">接收人昵称:</label>
                <input type="text" id="receiver" name="receiver" required
                       placeholder="例如: 小马">
                <small style="color: #666;">微信联系人的昵称</small>
            </div>

            <div class="form-group">
                <label for="user_input">您想表达的意思:</label>
                <textarea id="user_input" name="user_input" required
                          placeholder="请输入您想要表达的意思，AI将帮您生成合适的微信消息...&#10;&#10;例如：&#10;- 询问对方是否有时间面试&#10;- 推荐一个新的工作机会&#10;- 感谢对方提供的简历&#10;- 约定下次沟通时间"></textarea>
                <small style="color: #666;">描述您想要传达的意思，AI会生成合适的微信消息</small>
            </div>

            <div class="form-group">
                <button type="button" class="btn" onclick="generateMessage()" style="background-color: #2196F3;">🤖 AI生成消息</button>
                <button type="button" class="btn btn-secondary" onclick="clearUserInput()">🗑️ 清空输入</button>
            </div>

            <div class="form-group">
                <label for="message">生成的消息内容:</label>
                <textarea id="message" name="message" required
                          placeholder="AI生成的消息将显示在这里，您可以进一步编辑..."></textarea>
                <small style="color: #666;">AI生成的消息，您可以根据需要进行修改</small>
            </div>

            <div class="form-group">
                <button type="submit" class="btn">📤 发送消息</button>
                <button type="button" class="btn btn-secondary" onclick="clearForm()">🗑️ 清空表单</button>
            </div>
        </form>
        
        <div class="loading" id="loading">
            正在发送消息，请稍候...
        </div>
        
        <div class="result" id="result"></div>
        
        <div class="example">
            <h4>🤖 AI智能消息生成使用说明</h4>
            <p><strong>1. 输入意图:</strong> 在"您想表达的意思"框中描述您想要传达的内容</p>
            <p><strong>2. AI生成:</strong> 点击"AI生成消息"按钮，AI会根据您的意图生成合适的微信消息</p>
            <p><strong>3. 编辑优化:</strong> 您可以对AI生成的消息进行进一步编辑和优化</p>
            <p><strong>4. 发送消息:</strong> 确认消息内容后点击"发送消息"按钮</p>

            <h4>💡 AI生成示例</h4>
            <div class="message-preview">
                <strong>输入意图:</strong> "询问对方是否有时间进行电话面试"<br>
                <strong>AI生成:</strong> "您好！我是XX公司的招聘顾问，看到您的简历很符合我们的职位要求。请问您最近有时间进行一次电话面试吗？我们可以详细聊聊这个机会。"
            </div>

            <div class="message-preview">
                <strong>输入意图:</strong> "推荐一个新的工作机会"<br>
                <strong>AI生成:</strong> "您好！我这边有一个很不错的工作机会想推荐给您，职位和薪资都比较符合您的背景。方便的话我们可以详细沟通一下，看看是否感兴趣。"
            </div>

            <h4>🔧 技术说明</h4>
            <p>本工具使用豆包AI模型来生成消息，会根据以下因素优化生成内容：</p>
            <ul>
                <li>接收人昵称（生成更个性化的称呼）</li>
                <li>您的表达意图（确保消息符合目的）</li>
                <li>微信聊天的语言风格（更自然的表达）</li>
                <li>商务沟通的专业性（适合猎头场景）</li>
            </ul>

            <h4>💡 使用提示</h4>
            <ul>
                <li>描述意图时尽量具体明确，AI生成的消息会更准确</li>
                <li>可以多次生成不同版本的消息进行对比</li>
                <li>生成后的消息可以根据实际情况进行调整</li>
                <li>确保目标主机的微信API服务正在运行</li>
                <li>接收人必须是微信好友，且昵称要准确</li>
            </ul>
        </div>
    </div>

    <script>
        // AI消息生成功能
        async function generateMessage() {
            const userInput = document.getElementById('user_input').value.trim();
            const receiver = document.getElementById('receiver').value.trim();

            if (!userInput) {
                alert('请先输入您想要表达的意思');
                return;
            }

            if (!receiver) {
                alert('请先输入接收人昵称，这有助于AI生成更合适的消息');
                return;
            }

            // 显示AI加载状态
            const aiLoading = document.createElement('div');
            aiLoading.className = 'ai-loading';
            aiLoading.innerHTML = '🤖 AI正在生成消息...';
            aiLoading.style.display = 'block';

            const messageTextarea = document.getElementById('message');
            messageTextarea.parentNode.insertBefore(aiLoading, messageTextarea);

            try {
                const response = await fetch('/api/generate_message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_input: userInput,
                        receiver: receiver
                    })
                });

                const result = await response.json();

                // 隐藏加载状态
                aiLoading.remove();

                if (result.status === 'success') {
                    // 显示生成的消息
                    messageTextarea.value = result.generated_message;
                    messageTextarea.className = 'generated-message';

                    // 显示成功提示
                    showNotification('✅ AI消息生成成功！您可以进一步编辑后发送', 'success');
                } else {
                    showNotification(`❌ AI消息生成失败: ${result.message}`, 'error');
                }

            } catch (error) {
                // 隐藏加载状态
                aiLoading.remove();
                showNotification(`❌ 请求失败: ${error.message}`, 'error');
            }
        }

        // 显示通知
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `result ${type}`;
            notification.innerHTML = message;
            notification.style.display = 'block';
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '1000';
            notification.style.maxWidth = '400px';

            document.body.appendChild(notification);

            // 3秒后自动消失
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 消息发送功能
        document.getElementById('messageForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = {
                target_host: formData.get('target_host'),
                receiver: formData.get('receiver'),
                msg: formData.get('message')
            };

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';

            try {
                const response = await fetch('/api/send_wechat_message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';

                // 显示结果
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';

                if (result.status === 'success') {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h4>✅ 消息发送成功!</h4>
                        <p><strong>接收人:</strong> ${result.data.receiver}</p>
                        <p><strong>消息内容:</strong> ${result.data.msg}</p>
                        <p><strong>目标主机:</strong> ${result.data.target_host}</p>
                        <p><strong>服务器响应:</strong></p>
                        <pre>${JSON.stringify(result.data.response, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h4>❌ 消息发送失败</h4>
                        <p><strong>错误信息:</strong> ${result.message}</p>
                        ${result.data ? `<p><strong>详细信息:</strong> ${JSON.stringify(result.data, null, 2)}</p>` : ''}
                    `;
                }

            } catch (error) {
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';

                // 显示错误
                const resultDiv = document.getElementById('result');
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h4>❌ 请求失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p>请检查网络连接和服务器状态</p>
                `;
            }
        });

        function clearUserInput() {
            document.getElementById('user_input').value = '';
        }

        function clearForm() {
            document.getElementById('messageForm').reset();
            document.getElementById('result').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
            document.getElementById('message').className = '';
        }

        // 页面加载时的提示
        window.addEventListener('load', function() {
            console.log('智能微信消息发送页面已加载');
        });
    </script>
</body>
</html>
