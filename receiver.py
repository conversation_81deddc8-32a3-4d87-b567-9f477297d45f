import json
import os
from datetime import datetime
from pathlib import Path

DATA_FOLDER = os.path.join(os.path.dirname(__file__), "data")

def parse_client_id(client_id):
    """从client_id中解析IP和用户名"""
    # client_id格式: IP_用户名 (例如: 192_168_1_5_小司)
    parts = client_id.split('_')
    if len(parts) >= 5:  # 至少需要4个IP部分 + 1个用户名部分
        ip = '.'.join(parts[:4])
        username = '_'.join(parts[4:])
        return ip, username
    elif len(parts) == 4:
        # 只有IP，没有用户名
        ip = '.'.join(parts[:4])
        return ip, "unknown"
    return None, None

def create_user_folder_and_logs(username, user_folder_path):
    """创建用户文件夹和相关日志文件"""
    try:
        # 创建用户文件夹
        Path(user_folder_path).mkdir(parents=True, exist_ok=True)

        # 创建新增联系人记录文件（如果不存在）
        new_contacts_log = os.path.join(user_folder_path, "new_contacts_log.json")
        if not os.path.exists(new_contacts_log):
            initial_log = {
                "user": username,
                "created_time": datetime.now().isoformat(),
                "description": "新增联系人记录文件",
                "note": "此文件记录后续新增的联系人，首次获取的联系人不会记录",
                "records": []
            }

            with open(new_contacts_log, 'w', encoding='utf-8') as f:
                json.dump(initial_log, f, ensure_ascii=False, indent=2)

            print(f"📝 已创建新增联系人记录文件: {new_contacts_log}")

        return True

    except Exception as e:
        print(f"❌ 创建用户文件夹失败: {str(e)}")
        return False

def save_contacts(client_id, contacts):
    """保存联系人到用户文件夹"""
    print(f"🔍🔍🔍 RECEIVER.PY 被调用了！client_id: {client_id}")

    # 确保data目录存在
    os.makedirs(DATA_FOLDER, exist_ok=True)

    # 解析client_id获取用户名
    ip, username = parse_client_id(client_id)
    print(f"🔍🔍🔍 解析结果 - IP: {ip}, 用户名: {username}")

    if username and username != "unknown":
        # 创建用户文件夹路径
        user_folder = os.path.join(DATA_FOLDER, username)

        # 创建用户文件夹和日志文件
        if create_user_folder_and_logs(username, user_folder):
            # 保存到用户文件夹中
            filepath = os.path.join(user_folder, f"{client_id}.json")
            print(f"📂 用户文件夹: {user_folder}")
        else:
            # 如果创建文件夹失败，保存到根目录
            filepath = os.path.join(DATA_FOLDER, f"{client_id}.json")
            print(f"⚠️  保存到根目录: {filepath}")
    else:
        # 无法解析用户名，保存到根目录
        filepath = os.path.join(DATA_FOLDER, f"{client_id}.json")
        print(f"⚠️  无法解析用户名，保存到根目录: {filepath}")

    # 保存联系人文件
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(contacts, f, ensure_ascii=False, indent=2)
        print(f"✅ 联系人已保存到: {filepath}")
        print(f"📊 联系人数量: {len(contacts)}")

        # 如果有用户名，记录保存信息
        if username and username != "unknown":
            print(f"👤 用户: {username}")
            print(f"🌐 IP: {ip}")

    except Exception as e:
        print(f"❌ 保存联系人失败: {str(e)}")
        raise
