#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新架构：分离的标签生成和智能分析
"""

import requests
import json
import time

# 测试用的聊天数据
SAMPLE_CHAT_DATA = {
    "contact_info": {
        "id": "test_contact_123",
        "remark": "21胡艺华英国正教授省青千",
        "nickname": "Dr.<PERSON>"
    },
    "messages": [
        {
            "type": "text",
            "content": "你好，我是猎头顾问，想了解一下您的求职意向",
            "time": "2024-01-15 10:00:00",
            "isSelf": True
        },
        {
            "type": "text", 
            "content": "您好，我目前在英国工作，是大学的正教授，入选了省青千计划。最近在考虑回国发展的机会",
            "time": "2024-01-15 10:05:00",
            "isSelf": False
        },
        {
            "type": "text",
            "content": "我主要从事AI和机器学习研究，有10年的工作经验。希望能找到一个好的平台继续我的研究工作",
            "time": "2024-01-15 10:10:00", 
            "isSelf": False
        },
        {
            "type": "text",
            "content": "薪资方面，我期望年薪在80-100万之间，主要看平台和发展前景",
            "time": "2024-01-15 10:15:00",
            "isSelf": False
        },
        {
            "type": "text",
            "content": "我可以推荐一些同领域的朋友，大家都在关注国内的机会",
            "time": "2024-01-15 10:20:00",
            "isSelf": False
        }
    ],
    "total_count": 5
}

def test_auto_tag_generation():
    """测试自动标签生成API"""
    print("🧪 测试1: 自动标签生成")
    print("=" * 60)
    
    contact_id = "test_contact_123"
    url = f"http://127.0.0.1:8080/api/auto_generate_tags/{contact_id}"
    
    print(f"📤 发送请求到: {url}")
    print(f"📄 联系人: {SAMPLE_CHAT_DATA['contact_info']['remark']}")
    print(f"💬 消息数量: {SAMPLE_CHAT_DATA['total_count']} 条")
    
    try:
        start_time = time.time()
        
        response = requests.post(url, json=SAMPLE_CHAT_DATA, timeout=60)
        
        end_time = time.time()
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f} 秒")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 自动标签生成成功")
            print(f"📊 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get('status') == 'success':
                tags = result.get('tags', {})
                print(f"\n🏷️ 生成的标签:")
                
                categories = [
                    ('judgment', '1️⃣ 判断备注'),
                    ('manual', '2️⃣ 人工检索'), 
                    ('ai', '3️⃣ AI对话'),
                    ('remark', '4️⃣ 备注提取'),
                    ('comprehensive', '📋 综合标签')
                ]
                
                for key, name in categories:
                    category_tags = tags.get(key, [])
                    if category_tags:
                        print(f"  {name}: {category_tags}")
                
                return True
            else:
                print(f"⚠️ 标签生成失败: {result.get('message', '未知错误')}")
                return False
        
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
    
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_intelligent_analysis():
    """测试智能分析API（使用缓存数据）"""
    print("\n🧪 测试2: 智能分析（使用缓存数据）")
    print("=" * 60)
    
    contact_id = "test_contact_123"
    url = f"http://127.0.0.1:8080/api/analyze_chat/{contact_id}"
    
    print(f"📤 发送请求到: {url}")
    print(f"💡 应该使用之前缓存的预处理数据")
    
    try:
        start_time = time.time()
        
        # 不发送聊天数据，测试是否能使用缓存
        response = requests.post(url, json={}, timeout=60)
        
        end_time = time.time()
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f} 秒")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 智能分析成功")
            
            if result.get('status') == 'success':
                analysis_data = result.get('data', {})
                analysis_text = analysis_data.get('analysis', '')
                stats = analysis_data.get('stats', {})
                
                print(f"\n📊 分析统计:")
                print(f"  - 联系人: {stats.get('contact_name', '未知')}")
                print(f"  - 消息总数: {stats.get('total_messages', 0)}")
                print(f"  - 文本消息: {stats.get('text_count', 0)}")
                print(f"  - 图片数量: {stats.get('image_count', 0)}")
                print(f"  - 文档数量: {stats.get('document_count', 0)}")
                print(f"  - 语音数量: {stats.get('voice_count', 0)}")
                
                print(f"\n📝 分析报告预览:")
                print(f"{analysis_text[:300]}...")
                
                return True
            else:
                print(f"⚠️ 智能分析失败: {result.get('message', '未知错误')}")
                return False
        
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
    
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_intelligent_analysis_without_cache():
    """测试智能分析API（不使用缓存数据）"""
    print("\n🧪 测试3: 智能分析（不使用缓存，发送完整数据）")
    print("=" * 60)
    
    contact_id = "test_contact_456"  # 使用不同的ID，确保没有缓存
    url = f"http://127.0.0.1:8080/api/analyze_chat/{contact_id}"
    
    print(f"📤 发送请求到: {url}")
    print(f"💡 发送完整聊天数据，测试完整处理流程")
    
    try:
        start_time = time.time()
        
        # 发送完整聊天数据
        response = requests.post(url, json=SAMPLE_CHAT_DATA, timeout=60)
        
        end_time = time.time()
        print(f"⏱️ 请求耗时: {end_time - start_time:.2f} 秒")
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 智能分析成功")
            
            if result.get('status') == 'success':
                analysis_data = result.get('data', {})
                stats = analysis_data.get('stats', {})
                
                print(f"\n📊 分析统计:")
                print(f"  - 联系人: {stats.get('contact_name', '未知')}")
                print(f"  - 消息总数: {stats.get('total_messages', 0)}")
                
                return True
            else:
                print(f"⚠️ 智能分析失败: {result.get('message', '未知错误')}")
                return False
        
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
    
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 新架构测试开始")
    print("=" * 60)
    print("📋 测试计划:")
    print("  1. 自动标签生成（前置处理 + 标签生成）")
    print("  2. 智能分析（使用缓存数据）")
    print("  3. 智能分析（完整处理流程）")
    print("=" * 60)
    
    results = []
    
    # 测试1：自动标签生成
    result1 = test_auto_tag_generation()
    results.append(("自动标签生成", result1))
    
    # 等待一下，确保缓存生效
    time.sleep(2)
    
    # 测试2：智能分析（使用缓存）
    result2 = test_intelligent_analysis()
    results.append(("智能分析(缓存)", result2))
    
    # 测试3：智能分析（完整流程）
    result3 = test_intelligent_analysis_without_cache()
    results.append(("智能分析(完整)", result3))
    
    # 总结
    print("\n🏁 测试完成")
    print("=" * 60)
    print("📊 测试结果:")
    
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  - {test_name}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\n📈 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有测试通过！新架构工作正常")
    else:
        print("⚠️ 部分测试失败，需要检查问题")

if __name__ == "__main__":
    main()
