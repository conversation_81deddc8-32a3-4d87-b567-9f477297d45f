"""
测试分析工具V2的功能
"""

import json
import os
import shutil
from pathlib import Path
from analyze_contacts_v2 import ContactAnalyzerV2

def create_test_data():
    """创建测试数据"""
    print("🧪 创建测试数据...")
    
    test_data_dir = Path("test_data")
    if test_data_dir.exists():
        shutil.rmtree(test_data_dir)
    
    # 创建测试用户数据
    test_users = {
        "测试用户A": [
            {
                "UserName": "filehelper",
                "NickName": "文件传输助手",
                "Alias": "",
                "Remark": ""
            },
            {
                "UserName": "test_contact_001",
                "NickName": "测试联系人1",
                "Alias": "test1",
                "Remark": "用户A的联系人"
            },
            {
                "UserName": "common_contact_001",
                "NickName": "共同联系人1",
                "Alias": "",
                "Remark": "这是共同联系人"
            }
        ],
        "测试用户B": [
            {
                "UserName": "filehelper",
                "NickName": "文件传输助手",
                "Alias": "",
                "Remark": ""
            },
            {
                "UserName": "test_contact_002",
                "NickName": "测试联系人2",
                "Alias": "test2",
                "Remark": "用户B的联系人"
            },
            {
                "UserName": "common_contact_001",
                "NickName": "共同联系人1",
                "Alias": "",
                "Remark": "这是共同联系人"
            },
            {
                "UserName": "weixin",
                "NickName": "微信团队",
                "Alias": "",
                "Remark": ""
            }
        ],
        "测试用户C": [
            {
                "UserName": "filehelper",
                "NickName": "文件传输助手",
                "Alias": "",
                "Remark": ""
            },
            {
                "UserName": "weixin",
                "NickName": "微信团队",
                "Alias": "",
                "Remark": ""
            },
            {
                "UserName": "test_contact_003",
                "NickName": "测试联系人3",
                "Alias": "test3",
                "Remark": "用户C独有的联系人"
            }
        ]
    }
    
    # 创建测试文件结构
    for username, contacts in test_users.items():
        user_dir = test_data_dir / username
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建联系人文件
        contact_file = user_dir / f"192_168_1_100_{username}.json"
        with open(contact_file, 'w', encoding='utf-8') as f:
            json.dump(contacts, f, ensure_ascii=False, indent=2)
        
        # 创建日志文件（应该被忽略）
        log_file = user_dir / "new_contacts_log.json"
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump({"user": username, "records": []}, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 测试数据已创建到: {test_data_dir}")
    return test_data_dir

def test_analyzer():
    """测试分析器"""
    print("\n🔍 开始测试分析器...")
    
    # 创建测试数据
    test_data_dir = create_test_data()
    
    # 创建分析器
    analyzer = ContactAnalyzerV2(
        data_dir=str(test_data_dir),
        reports_dir="test_reports"
    )
    
    # 运行分析
    success = analyzer.run_analysis()
    
    if success:
        print("✅ 分析测试成功！")
        
        # 验证结果
        print("\n📊 验证分析结果:")
        print(f"   用户数: {len(analyzer.user_contacts)}")
        print(f"   重复联系人数: {len(analyzer.duplicate_contacts)}")
        
        # 显示重复联系人
        print("\n🔄 重复联系人:")
        for contact_key, duplicate_info in analyzer.duplicate_contacts.items():
            contact_data = duplicate_info['contact_data']
            nickname = contact_data.get("NickName", contact_key)
            users = duplicate_info['users']
            print(f"   {nickname} - 出现在: {', '.join(users)}")
        
        # 检查报告文件
        reports_dir = Path("test_reports")
        if reports_dir.exists():
            report_files = list(reports_dir.glob("*"))
            print(f"\n📁 生成的报告文件 ({len(report_files)}个):")
            for report_file in report_files:
                print(f"   📄 {report_file.name}")
        
    else:
        print("❌ 分析测试失败！")
    
    return success

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    # 清理测试数据目录
    test_data_dir = Path("test_data")
    if test_data_dir.exists():
        shutil.rmtree(test_data_dir)
        print("✅ 测试数据目录已删除")
    
    # 清理测试报告目录
    test_reports_dir = Path("test_reports")
    if test_reports_dir.exists():
        shutil.rmtree(test_reports_dir)
        print("✅ 测试报告目录已删除")

def main():
    print("="*60)
    print("微信联系人分析工具V2 - 功能测试")
    print("="*60)
    
    try:
        # 运行测试
        success = test_analyzer()
        
        if success:
            print(f"\n🎉 所有测试通过！")
            
            # 询问是否查看报告
            choice = input("\n是否查看测试报告? (y/N): ").lower().strip()
            if choice == 'y':
                import subprocess
                try:
                    subprocess.run(["explorer", "test_reports"], check=True)
                except:
                    print("无法打开报告目录")
        else:
            print(f"\n❌ 测试失败！")
        
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
    finally:
        # 询问是否清理
        cleanup = input("\n是否清理测试数据? (Y/n): ").lower().strip()
        if cleanup != 'n':
            cleanup_test_data()

if __name__ == "__main__":
    main()
