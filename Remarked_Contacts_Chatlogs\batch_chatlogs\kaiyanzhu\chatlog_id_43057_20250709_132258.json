{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "43057", "contact_info": {"UserName": "wxid_kp8m1qgegnlw12", "Alias": "jwg20286", "Remark": "43057 蒋文广", "NickName": "蒋文广"}, "fetch_time": "2025-07-09T13:22:58.910058", "message_count": 161, "chatlog_data": [{"seq": 1705653095000, "time": "2024-01-19T16:31:35+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 10000, "subType": 0, "content": "你已添加了蒋文广，现在可以开始聊天了。"}, {"seq": 1705653104000, "time": "2024-01-19T16:31:44+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博您好"}, {"seq": 1705653387000, "time": "2024-01-19T16:36:27+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我这边是在linkedin上添加您的微信   "}, {"seq": 1705653400000, "time": "2024-01-19T16:36:40+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "你什么时间方便沟通一下呢？"}, {"seq": 1705798211000, "time": "2024-01-21T08:50:11+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "你好，我今明都能交流"}, {"seq": 1705885105000, "time": "2024-01-22T08:58:25+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的哦   蒋博，您现在什么时间方便呢？"}, {"seq": 1705889026000, "time": "2024-01-22T10:03:46+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "现在就行"}, {"seq": 1705900900000, "time": "2024-01-22T13:21:40+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博士  抱歉早上有总结大会，您什么时间方便电话呢？您可以发送一份您的CV吗？"}, {"seq": 1705900917000, "time": "2024-01-22T13:21:57+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1705900932000, "time": "2024-01-22T13:22:12+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我可以推荐合适的单位给您哦"}, {"seq": 1705901202000, "time": "2024-01-22T13:26:42+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n\t\t<title>Resume.pdf</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>6</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<md5>030c3b51156bf2e74f05f0be60d11728</md5>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>132975</totallen>\n\t\t\t<attachid>@cdn_3052020100044b30490201000204d7a54ebf02033d09680204658b2065020465adfc93042438646130323036612d396232642d343336332d616335382d3663643739373539323361640204011c00050201000400_0cdd191a0e26232ee8fc419c0311a635_1</attachid>\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<fileuploadtoken>v1_Rb9t+y+K022MyIg7Y2I76zelzDhRuPf3KxV+Cx7Viqr17MkAmN7Sdfu39etHEBya1PRk/WFxQ5M3Ws8L3mSz8BT5SBwDN2fAhQDd4EHwPJCoprUkNDOm1ss91sH2L0ETXQgRXTzEYmR3kOlookaD1wt9OrXo/wXGHuaovpTFkS8NX4yxb9niFTMtFMxFVYAqZtvJlkxjE6oRFUA3MFD79tbH1JVpngU=</fileuploadtoken>\n\t\t\t<overwrite_newmsgid>6711182413895893827</overwrite_newmsgid>\n\t\t\t<filekey>66721f6cca5013b8ea5ca482df1e1dfc</filekey>\n\t\t\t<cdnattachurl>3052020100044b30490201000204d7a54ebf02033d09680204658b2065020465adfc93042438646130323036612d396232642d343336332d616335382d3663643739373539323361640204011c00050201000400</cdnattachurl>\n\t\t\t<aeskey>0cdd191a0e26232ee8fc419c0311a635</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t</appattach>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_kp8m1qgegnlw12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>7</version>\n\t\t<appname>微信电脑版</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n\u0000", "contents": {"md5": "030c3b51156bf2e74f05f0be60d11728", "title": "Resume.pdf"}}, {"seq": 1705901207000, "time": "2024-01-22T13:26:47+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "你好，这是我的简历"}, {"seq": 1705901229000, "time": "2024-01-22T13:27:09+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "我现在可以电话"}, {"seq": 1705901406000, "time": "2024-01-22T13:30:06+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 10000, "subType": 0, "content": "对方未添加你为朋友。对方添加后，才能进行通话。"}, {"seq": 1705901406002, "time": "2024-01-22T13:30:06+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[未接通]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>0</roomid>\n<roomkey>0</roomkey>\n<inviteid>1705901405</inviteid>\n<msg_type>100</msg_type>\n<timestamp>1705901406603</timestamp>\n<identity><![CDATA[7855610818667472638]]></identity>\n<duration>0</duration>\n<inviteid64>1705901405</inviteid64>\n<business>0</business>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1705901419000, "time": "2024-01-22T13:30:19+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "抱歉  我这边无法拨打哦"}, {"seq": 1705901432000, "time": "2024-01-22T13:30:32+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我这边显示您还没添加我[偷笑]"}, {"seq": 1705901436000, "time": "2024-01-22T13:30:36+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 10000, "subType": 0, "content": "对方未添加你为朋友。对方添加后，才能进行通话。"}, {"seq": 1705901436002, "time": "2024-01-22T13:30:36+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 50, "subType": 0, "content": "<voipmsg type=\"VoIPBubbleMsg\"><VoIPBubbleMsg><msg><![CDATA[未接通]]></msg>\n<room_type>1</room_type>\n<red_dot>false</red_dot>\n<roomid>0</roomid>\n<roomkey>0</roomkey>\n<inviteid>1705901434</inviteid>\n<msg_type>100</msg_type>\n<timestamp>1705901436233</timestamp>\n<identity><![CDATA[7265478691516459533]]></identity>\n<duration>0</duration>\n<inviteid64>1705901434</inviteid64>\n<business>0</business>\n</VoIPBubbleMsg></voipmsg>"}, {"seq": 1705901440000, "time": "2024-01-22T13:30:40+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "添加了"}, {"seq": 1705902778000, "time": "2024-01-22T13:52:58+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博您对传感器有研究吗？"}, {"seq": 1705902788000, "time": "2024-01-22T13:53:08+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "有的，我就是做这方面的"}, {"seq": 1705902800000, "time": "2024-01-22T13:53:20+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那就精准了"}, {"seq": 1705902801000, "time": "2024-01-22T13:53:21+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "我现在的项目是气体传感器"}, {"seq": 1705902810000, "time": "2024-01-22T13:53:30+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "汽车传感器呢？"}, {"seq": 1705902856000, "time": "2024-01-22T13:54:16+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"1fd82548806dcf65c2feced98da6abbe\" encryver=\"1\" cdnthumbaeskey=\"1fd82548806dcf65c2feced98da6abbe\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f7e990204546bf071020465ae02d3042430376166313536332d643238632d346362322d386434342d3933653063343862376430620204011418020201000405004c53d900\" cdnthumblength=\"2993\" cdnthumbheight=\"50\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f7e990204546bf071020465ae02d3042430376166313536332d643238632d346362322d386434342d3933653063343862376430620204011418020201000405004c53d900\" length=\"14313\" md5=\"79bb5424990c7edf24015df0fdcce688\" hevc_mid_size=\"0\" originsourcemd5=\"79bb5424990c7edf24015df0fdcce688\"/></msg>", "contents": {"md5": "79bb5424990c7edf24015df0fdcce688"}}, {"seq": 1705902869000, "time": "2024-01-22T13:54:29+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "如果是加速度传感器，速度传感器，或者陀螺仪之类的，应该也能做"}, {"seq": 1705902921000, "time": "2024-01-22T13:55:21+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "https://www.hinge-tech.com/index.aspx"}, {"seq": 1705902949000, "time": "2024-01-22T13:55:49+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "浙江赫千电子有限公司"}, {"seq": 1705902977000, "time": "2024-01-22T13:56:17+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这个是赫千的官网   您了解一下   "}, {"seq": 1705902982000, "time": "2024-01-22T13:56:22+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1705902998000, "time": "2024-01-22T13:56:38+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"ee9ffb09d0107447aefe9b6792b3cb20\" encryver=\"1\" cdnthumbaeskey=\"ee9ffb09d0107447aefe9b6792b3cb20\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f7e990204546bf071020465ae0361042438373564373631632d663530622d343036632d396432312d3130323763333231653166300204011818020201000405004c56f900\" cdnthumblength=\"13294\" cdnthumbheight=\"102\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f7e990204546bf071020465ae0361042438373564373631632d663530622d343036632d396432312d3130323763333231653166300204011818020201000405004c56f900\" length=\"235220\" md5=\"0c61e0bc0fad220e46eca778c6e19343\" hevc_mid_size=\"0\" originsourcemd5=\"0c61e0bc0fad220e46eca778c6e19343\"/></msg>", "contents": {"md5": "0c61e0bc0fad220e46eca778c6e19343"}}, {"seq": 1705903013000, "time": "2024-01-22T13:56:53+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "ok"}, {"seq": 1705903200000, "time": "2024-01-22T14:00:00+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"0e2056d6944d162df5d4c4b38a57b367\" encryver=\"1\" cdnthumbaeskey=\"0e2056d6944d162df5d4c4b38a57b367\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f7e990204546bf071020465ae042b042430666162653066392d386162652d343934362d386166372d3862613934393735623134360204011418020201000405004c4dfd00\" cdnthumblength=\"6789\" cdnthumbheight=\"50\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f7e990204546bf071020465ae042b042430666162653066392d386162652d343934362d386166372d3862613934393735623134360204011418020201000405004c4dfd00\" length=\"5272\" md5=\"ebcf1c48475cad6e497cf94f4f1158f2\" hevc_mid_size=\"0\" originsourcemd5=\"ebcf1c48475cad6e497cf94f4f1158f2\"/></msg>", "contents": {"md5": "ebcf1c48475cad6e497cf94f4f1158f2"}}, {"seq": 1705903200001, "time": "2024-01-22T14:00:00+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "浙江力夫自控技术股份  官网：https://www.lefoo.cn/"}, {"seq": 1705903230000, "time": "2024-01-22T14:00:30+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "力夫自控和您也是很匹配的  "}, {"seq": 1705903305000, "time": "2024-01-22T14:01:45+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1705903927000, "time": "2024-01-22T14:12:07+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博    力夫自控这边我推荐一下    您看可以吗？"}, {"seq": 1705904390000, "time": "2024-01-22T14:19:50+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1705904394000, "time": "2024-01-22T14:19:54+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "多谢了"}, {"seq": 1705904421000, "time": "2024-01-22T14:20:21+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "赫千呢？", "contents": {"refer": {"seq": 0, "time": "2024-01-22T13:55:49+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6ahbbsar7g8i22", "senderName": "<PERSON><PERSON> Zhu@海归就业创业", "isSelf": false, "type": 1, "subType": 0, "content": "浙江赫千电子有限公司"}}}, {"seq": 1705904914000, "time": "2024-01-22T14:28:34+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博士的期望薪资大概是多少呢？"}, {"seq": 1705905273000, "time": "2024-01-22T14:34:33+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "这个让我思考一下吧"}, {"seq": 1705905284000, "time": "2024-01-22T14:34:44+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "好的", "contents": {"refer": {"seq": 0, "time": "2024-01-22T14:34:33+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "这个让我思考一下吧"}}}, {"seq": 1707030081000, "time": "2024-02-04T15:01:21+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": "我拍了拍\"43057 蒋文广\""}, {"seq": 1707030248000, "time": "2024-02-04T15:04:08+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博您好呀   力夫自控对您的履历很感兴趣   方向很契合    "}, {"seq": 1707030259000, "time": "2024-02-04T15:04:19+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "力夫集团始于2000年，总厂房8万M²，员工超1000人。集团由浙江力夫自控技术股份有限公司、杭州力夫机电制造有限公司、浙江力夫传感技术有限公司组成，涉及压力开关、传感控制、微型泵等产品的研发制造。应用于环境控制、机械装备、智能设备等多领域的装备配套。\n    集团拥有两个基地及辐射海内外的销售网络。配备精益生产设备和高精度精密检测设备和综合实验室。拥有ISO多项体系认证及3C、UL、CE等安规认证。通过研发投入，获得超150项专利认证。\n    力夫致力于服务全球的数字传感技术与压力控制领域知名品牌。"}, {"seq": 1707030299000, "time": "2024-02-04T15:04:59+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 34, "subType": 0, "content": "<msg><voicemsg endflag=\"1\" cancelflag=\"0\" forwardflag=\"0\" voiceformat=\"4\" voicelength=\"25600\" length=\"48940\" bufid=\"0\" aeskey=\"5164af3e7547ea054b83fe87f2d0df55\" voiceurl=\"3052020100044b304902010002044edebcaf02032f54050204a66a42b7020465bf371b042465383031306364612d336465392d343439302d613333372d31663964376334613236636502040114000f0201000400b646ad3c\" voicemd5=\"\" clientmsgid=\"41626161343366336334373031383900311504020424003c0034b19106\" fromusername=\"wxid_6ahbbsar7g8i22\" /></msg>", "contents": {"voice": "8029216782682270588"}}, {"seq": 1707093152000, "time": "2024-02-05T08:32:32+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "我明天应该可以，就是国内5号晚上或者6号早上？"}, {"seq": 1707093876000, "time": "2024-02-05T08:44:36+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   蒋博，我沟通一下  "}, {"seq": 1707102455000, "time": "2024-02-05T11:07:35+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "<PERSON><PERSON> Zhu@海归创业 邀请您参加腾讯会议\n会议主题：43057蒋博-力夫集团对接会议\n会议时间：2024/02/06 09:00-10:00 (GMT+08:00) 中国标准时间 - 北京\n\n点击链接入会，或添加至会议列表：\nhttps://meeting.tencent.com/dm/hxDDCjWxa6zV\n\n#腾讯会议：655-292-705\n\n复制该信息，打开手机腾讯会议即可参与"}, {"seq": 1707102471000, "time": "2024-02-05T11:07:51+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博  您好呀  这是北京时间2月6日（明天）早上9:00的会议连接哦  请查收[握手]"}, {"seq": 1707103265000, "time": "2024-02-05T11:21:05+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的，多谢"}, {"seq": 1707103278000, "time": "2024-02-05T11:21:18+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "应该的    蒋博"}, {"seq": 1707114564000, "time": "2024-02-05T14:29:24+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"0252dc88c7e71d36971cb009e721d702\" encryver=\"1\" cdnthumbaeskey=\"0252dc88c7e71d36971cb009e721d702\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f54050204196a42b7020465c08044042466643632316563612d663935612d346666332d383339322d3562643635303638326332380204011418020201000405004c4d9900\" cdnthumblength=\"5173\" cdnthumbheight=\"56\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f54050204196a42b7020465c08044042466643632316563612d663935612d346666332d383339322d3562643635303638326332380204011418020201000405004c4d9900\" length=\"49176\" md5=\"f002c34a88657b674a057c55adc86f9e\" hevc_mid_size=\"0\" originsourcemd5=\"f002c34a88657b674a057c55adc86f9e\"/></msg>", "contents": {"md5": "f002c34a88657b674a057c55adc86f9e"}}, {"seq": 1707114564001, "time": "2024-02-05T14:29:24+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "<?xml version=\"1.0\"?><msg><img aeskey=\"021a2736c4fed857fd7145b671f8eff3\" encryver=\"1\" cdnthumbaeskey=\"021a2736c4fed857fd7145b671f8eff3\" cdnthumburl=\"3057020100044b30490201000204ff23008602032f54050204196a42b7020465c08045042438643737303265642d643361612d346135302d623930362d3566616332373665363164640204051418020201000405004c4c0900\" cdnthumblength=\"10537\" cdnthumbheight=\"58\" cdnthumbwidth=\"150\" cdnmidheight=\"0\" cdnmidwidth=\"0\" cdnhdheight=\"0\" cdnhdwidth=\"0\" cdnmidimgurl=\"3057020100044b30490201000204ff23008602032f54050204196a42b7020465c08045042438643737303265642d643361612d346135302d623930362d3566616332373665363164640204051418020201000405004c4c0900\" length=\"115144\" md5=\"c65d8c0fb506a2de3c55f95446c1aa0c\" hevc_mid_size=\"0\" originsourcemd5=\"c65d8c0fb506a2de3c55f95446c1aa0c\"/></msg>", "contents": {"md5": "c65d8c0fb506a2de3c55f95446c1aa0c"}}, {"seq": 1707114583000, "time": "2024-02-05T14:29:43+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博  和您确认一下"}, {"seq": 1707114638000, "time": "2024-02-05T14:30:38+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 34, "subType": 0, "content": "<msg><voicemsg endflag=\"1\" cancelflag=\"0\" forwardflag=\"0\" voiceformat=\"4\" voicelength=\"17541\" length=\"33309\" bufid=\"0\" aeskey=\"0b9e0d0ed77f6353654e6aed6281ba1f\" voiceurl=\"3052020100044b304902010002044edebcaf02032f5405020411a7b4de020465c0808e042465336432393533622d373265302d343233322d626635322d61366233643533626331383602040514000f0201000400b646ad3c\" voicemd5=\"\" clientmsgid=\"41626161343366336334373031383900181430020524003c00358f6105\" fromusername=\"wxid_6ahbbsar7g8i22\" /></msg>", "contents": {"voice": "6569310799518088498"}}, {"seq": 1707142755000, "time": "2024-02-05T22:19:15+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "是在Department of Mechanical Engineering的Postdoctoral associate"}, {"seq": 1707179780000, "time": "2024-02-06T08:36:20+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "没关系 蒋博   那您直接说Mechanical Engineer的职位   "}, {"seq": 1707179854000, "time": "2024-02-06T08:37:34+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "你知道对面是谁来和我聊吗？是hr还是研发组的人？"}, {"seq": 1707179870000, "time": "2024-02-06T08:37:50+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "知道的   "}, {"seq": 1707179961000, "time": "2024-02-06T08:39:21+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我给您发送参会人员名单  "}, {"seq": 1707179969000, "time": "2024-02-06T08:39:29+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博   稍等一下   "}, {"seq": 1707180059000, "time": "2024-02-06T08:40:59+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1707180526000, "time": "2024-02-06T08:48:46+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "系统消息", "senderName": "", "isSelf": true, "type": 10000, "subType": 0, "content": ""}, {"seq": 1707180723000, "time": "2024-02-06T08:52:03+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "会议时间：2月 6日（周五）9:00\n参会人员：\n1、蒋博士\n2、企业领导：老板吴总（待确认）、杭州传感公司的刘小进刘总、技术刘春涛刘总工、市场部总监黄良丘、力夫人事小林\n3、乐清人才办：袁老师、李老师、胡老师\n4、小朱\n会议流程：\n1、参会人员介绍\n2、博士个人介绍（个人背景、工作经历、研究领域）\n3、企业分享（企业概况， 行业领域成就）\n4、自由交流（研究领域、专业技术、突破重点）"}, {"seq": 1707180748000, "time": "2024-02-06T08:52:28+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "这么多人，多谢"}, {"seq": 1707180766000, "time": "2024-02-06T08:52:46+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "企业是很重视的    "}, {"seq": 1707183918000, "time": "2024-02-06T09:45:18+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博您好   方便电话吗？"}, {"seq": 1707183929000, "time": "2024-02-06T09:45:29+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "方便的"}, {"seq": 1707414287000, "time": "2024-02-09T01:44:47+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "新春快乐"}, {"seq": 1708239657000, "time": "2024-02-18T15:00:57+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博您好，浙江清华柔性电子技术研究院：https://www.tsinghua-zj.edu.cn/technology/professional/rou-xing-dian-zi-yan-jiu-yuan-3，和您的专业领域也是非常契合的，您可以先线上聊聊了解一下研究院的情况"}, {"seq": 1708239701000, "time": "2024-02-18T15:01:41+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这家研究院在嘉兴，离杭州非常近"}, {"seq": 1708270010000, "time": "2024-02-18T23:26:50+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的，多谢"}, {"seq": 1708303514000, "time": "2024-02-19T08:45:14+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博  那还是北京时间早上9点的时间吗？"}, {"seq": 1708306515000, "time": "2024-02-19T09:35:15+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "几号？"}, {"seq": 1708306602000, "time": "2024-02-19T09:36:42+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "本周您北京时间早上9点可以吗？"}, {"seq": 1708306619000, "time": "2024-02-19T09:36:59+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "可以"}, {"seq": 1708319409000, "time": "2024-02-19T13:10:09+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   蒋博，我和研究院确认时间"}, {"seq": 1708390681000, "time": "2024-02-20T08:58:01+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博   柔电院本周周四北京时间上午9点可以线上沟通"}, {"seq": 1708390713000, "time": "2024-02-20T08:58:33+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "国内时间2.22", "contents": {"refer": {"seq": 0, "time": "2024-02-20T08:58:01+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6ahbbsar7g8i22", "senderName": "<PERSON><PERSON> Zhu@海归就业创业", "isSelf": false, "type": 1, "subType": 0, "content": "蒋博   柔电院本周周四北京时间上午9点可以线上沟通"}}}, {"seq": 1708390729000, "time": "2024-02-20T08:58:49+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我稍后把会议链接发送给您"}, {"seq": 1708391075000, "time": "2024-02-20T09:04:35+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的"}, {"seq": 1708391077000, "time": "2024-02-20T09:04:37+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "多谢"}, {"seq": 1708391167000, "time": "2024-02-20T09:06:07+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "小罗@海归就业创业(小罗@海归就业创业) 邀请您参加企业微信会议\n会议主题：43057蒋博——浙江清华柔电院对接会议\n会议时间：2024/02/22 09:00-2024/02/22 10:00\n\n点击链接直接加入会议：https://work.weixin.qq.com/webapp/tm/kQuRBDbpESg\n\n#企业微信会议：367 870 212\n\n手机一键拨号入会\n+8675536550000,,367870212 (中国大陆)\n+85230018898,,,2,367870212 (中国香港)\n\n根据您的位置拨号\n+86 (中国大陆)\n+85230018898 (中国香港)"}, {"seq": 1708391177000, "time": "2024-02-20T09:06:17+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博   会议链接请查收"}, {"seq": 1708391752000, "time": "2024-02-20T09:15:52+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "收到了"}, {"seq": 1708508463000, "time": "2024-02-21T17:41:03+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "蒋博  实在不好意思，明天的会议柔电院那边省里有人视察，临时要接待工作，之后会议时间我们再确认", "contents": {"refer": {"seq": 0, "time": "2024-02-20T09:06:07+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6ahbbsar7g8i22", "senderName": "<PERSON><PERSON> Zhu@海归就业创业", "isSelf": false, "type": 1, "subType": 0, "content": "小罗@海归就业创业(小罗@海归就业创业) 邀请您参加企业微信会议\n会议主题：43057蒋博——浙江清华柔电院对接会议\n会议时间：2024/02/22 09:00-2024/02/22 10:00\n\n点击链接直接加入会议：https://work.weixin.qq.com/webapp/tm/kQuRBDbpESg\n\n#企业微信会议：367 870 212\n\n手机一键拨号入会\n+8675536550000,,367870212 (中国大陆)\n+85230018898,,,2,367870212 (中国香港)\n\n根据您的位置拨号\n+86 (中国大陆)\n+85230018898 (中国香港)"}}}, {"seq": 1708508491000, "time": "2024-02-21T17:41:31+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "实在不好意思了   蒋博   "}, {"seq": 1708508497000, "time": "2024-02-21T17:41:37+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "非常抱歉"}, {"seq": 1708518940000, "time": "2024-02-21T20:35:40+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的，没事"}, {"seq": 1708562391000, "time": "2024-02-22T08:39:51+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[抱拳]"}, {"seq": 1708562467000, "time": "2024-02-22T08:41:07+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博  之前和您沟通的  您的职位是否可以开具证明呢？"}, {"seq": 1708562519000, "time": "2024-02-22T08:41:59+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "只能开博士后的"}, {"seq": 1708562574000, "time": "2024-02-22T08:42:54+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "research fellow呢？"}, {"seq": 1708562835000, "time": "2024-02-22T08:47:15+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "不行"}, {"seq": 1708563533000, "time": "2024-02-22T08:58:53+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的   蒋博"}, {"seq": 1708563539000, "time": "2024-02-22T08:58:59+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那我清楚了   "}, {"seq": 1709090732000, "time": "2024-02-28T11:25:32+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "Kaiyan你好，我最近找到岗位打算签了。所以最近应该不继续找工作了。多谢你这段时间的帮忙，希望你未来工作顺利。"}, {"seq": 1709090757000, "time": "2024-02-28T11:25:57+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 49, "subType": 57, "content": "蒋博  杭州的吗？", "contents": {"refer": {"seq": 0, "time": "2024-02-28T11:25:32+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "Kaiyan你好，我最近找到岗位打算签了。所以最近应该不继续找工作了。多谢你这段时间的帮忙，希望你未来工作顺利。"}}}, {"seq": 1709090780000, "time": "2024-02-28T11:26:20+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "不是的"}, {"seq": 1709090858000, "time": "2024-02-28T11:27:38+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博   因为我这边考虑到您是杭州人嘛   主要集中在浙江给您看"}, {"seq": 1709090887000, "time": "2024-02-28T11:28:07+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "是的。不过最终我这找的不是杭州的岗位"}, {"seq": 1709090918000, "time": "2024-02-28T11:28:38+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "但还是感谢你的帮助"}, {"seq": 1709091028000, "time": "2024-02-28T11:30:28+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "应该的  蒋博士，现在您找的机会，各方面您觉得合适。您这边综合考虑就好"}, {"seq": 1709091051000, "time": "2024-02-28T11:30:51+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们之后有机会也可以再合作   "}, {"seq": 1709091056000, "time": "2024-02-28T11:30:56+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[抱拳]"}, {"seq": 1709091062000, "time": "2024-02-28T11:31:02+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的。也多谢你的体谅。"}, {"seq": 1709091101000, "time": "2024-02-28T11:31:41+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博士   有更好的机会，我也很开心呀   "}, {"seq": 1709091106000, "time": "2024-02-28T11:31:46+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "[呲牙]"}, {"seq": 1709094534000, "time": "2024-02-28T12:28:54+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "[呲牙]"}, {"seq": 1718243496000, "time": "2024-06-13T09:51:36+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "蒋博，您好！最近回到国内了吗？"}, {"seq": 1718249787000, "time": "2024-06-13T11:36:27+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "啊，没有。还在美国。"}, {"seq": 1718249898000, "time": "2024-06-13T11:38:18+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "上次您说已经有其他机会了，我以为是国内的机会呢"}, {"seq": 1718249997000, "time": "2024-06-13T11:39:57+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "不是，是美国的"}, {"seq": 1718257647000, "time": "2024-06-13T13:47:27+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的蒋博，后面您考虑回国的话，可以随时联系我哟"}, {"seq": 1718257817000, "time": "2024-06-13T13:50:17+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "再次恭喜蒋博拿到满意的offer"}, {"seq": 1718292117000, "time": "2024-06-13T23:21:57+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的，多谢"}, {"seq": 1749108331000, "time": "2025-06-05T15:25:31+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士您好呀！您今年考虑国内的工作机会吗"}, {"seq": 1749141495000, "time": "2025-06-06T00:38:15+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "在考虑，还没决定"}, {"seq": 1749171533000, "time": "2025-06-06T08:58:53+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "现在是在准备浙江省的省级项目，是先入职后认定的形式~"}, {"seq": 1749171540000, "time": "2025-06-06T08:59:00+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "今年的QM您有参与申报没有呀"}, {"seq": 1749177601000, "time": "2025-06-06T10:40:01+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "参与了"}, {"seq": 1749177887000, "time": "2025-06-06T10:44:47+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您是在哪个省份报的嘞"}, {"seq": 1749177904000, "time": "2025-06-06T10:45:04+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "省级和国家级的是可以同步报的哦"}, {"seq": 1749177925000, "time": "2025-06-06T10:45:25+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "报的是国家级的"}, {"seq": 1749177956000, "time": "2025-06-06T10:45:56+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "国家级您依托的企业是哪个省份呀"}, {"seq": 1749177963000, "time": "2025-06-06T10:46:03+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您这边方便同步一份最新简历吗"}, {"seq": 1749177965000, "time": "2025-06-06T10:46:05+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "浙江的"}, {"seq": 1749178008000, "time": "2025-06-06T10:46:48+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n\t\t<title>Resume.pdf</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>6</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<md5>df3d37e3e6972b10d07bd7980745497f</md5>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>133384</totallen>\n\t\t\t<attachid>@cdn_3057020100044b30490201000204d7a54ebf02030f5efb02042f90af2b020468425698042439663032613464382d326263662d346634632d383466612d6263333739346536653865370204011400050201000405004c55cd00_4174ba2489df48bbf0543b8ec0fc9ee4_1</attachid>\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<fileuploadtoken>v1_iYzPJUiB1XV1rhR3Ukq/r3PDQh1giRnunBpHElmYf8MasUzoSsS2vgaH/MhTbCuQrJArJqLPpfvtnj2d8jTgNx4tpUjU4EvoQU0eE12ZMnN+NfRrOqJdv2IRlYO6IBUDcsVRZSgdUm2vKiiwFmXPuYT9GYjTlWLw0Stl3ekTLM/ENyc0FdJ1juUp+px+QC0LLSFUxzXbq8fOxDFyye1s5oM/TC4NXDU9</fileuploadtoken>\n\t\t\t<overwrite_newmsgid>2692755703160217939</overwrite_newmsgid>\n\t\t\t<filekey>85b86d4b1142c7649ee176cc42428829</filekey>\n\t\t\t<cdnattachurl>3057020100044b30490201000204d7a54ebf02030f5efb02042f90af2b020468425698042439663032613464382d326263662d346634632d383466612d6263333739346536653865370204011400050201000405004c55cd00</cdnattachurl>\n\t\t\t<aeskey>4174ba2489df48bbf0543b8ec0fc9ee4</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_kp8m1qgegnlw12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>7</version>\n\t\t<appname>微信电脑版</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n\u0000", "contents": {"md5": "df3d37e3e6972b10d07bd7980745497f", "title": "Resume.pdf"}}, {"seq": 1749178017000, "time": "2025-06-06T10:46:57+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "这是我最新的简历"}, {"seq": 1749178172000, "time": "2025-06-06T10:49:32+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好呀好呀，我们主要也是浙江这边的，我们也可以为您看看呀"}, {"seq": 1749178267000, "time": "2025-06-06T10:51:07+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "那就有劳了"}, {"seq": 1749178348000, "time": "2025-06-06T10:52:28+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那您当时报国家级是有对接过吗还是走的配合申报呀"}, {"seq": 1749178397000, "time": "2025-06-06T10:53:17+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "怎么样算对接啊？"}, {"seq": 1749178411000, "time": "2025-06-06T10:53:31+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "就是和企业那边开会呀"}, {"seq": 1749178428000, "time": "2025-06-06T10:53:48+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "一般来说配合申报是有报酬的，您有没有嘞"}, {"seq": 1749178429000, "time": "2025-06-06T10:53:49+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好像没开过。但指定了一个具体的企业"}, {"seq": 1749178451000, "time": "2025-06-06T10:54:11+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "我也没报酬"}, {"seq": 1749178518000, "time": "2025-06-06T10:55:18+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "这样子，那您应该是配合申报的"}, {"seq": 1749178533000, "time": "2025-06-06T10:55:33+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您本身有全职回国的计划没有嘞"}, {"seq": 1749178557000, "time": "2025-06-06T10:55:57+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "现在还不确定，有计划，但不确凿"}, {"seq": 1749178597000, "time": "2025-06-06T10:56:37+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那不影响，可以先看看嘛，省级的话大部分都是要对接的，到时候可能会开个会什么的"}, {"seq": 1749178667000, "time": "2025-06-06T10:57:47+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "那如果最后决定不去呢？是怎么样的政策？有多少时间来做决定？"}, {"seq": 1749178759000, "time": "2025-06-06T10:59:19+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "省级申报的话是在9月份左右，现在的话主要是匹配对接这些吗，毕竟博士和企业双向选择，所以我们会提前准备的"}, {"seq": 1749178835000, "time": "2025-06-06T11:00:35+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "了解了"}, {"seq": 1749178858000, "time": "2025-06-06T11:00:58+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那您其实是有准备一些材料的哈，已经发给那边啦"}, {"seq": 1749178881000, "time": "2025-06-06T11:01:21+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "有一些材料"}, {"seq": 1749178890000, "time": "2025-06-06T11:01:30+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 49, "subType": 57, "content": "多谢了", "contents": {"refer": {"seq": 0, "time": "2025-06-06T11:00:58+08:00", "talker": "", "talkerName": "", "isChatRoom": false, "sender": "wxid_6ahbbsar7g8i22", "senderName": "<PERSON><PERSON> Zhu@海归就业创业", "isSelf": false, "type": 1, "subType": 0, "content": "那您其实是有准备一些材料的哈，已经发给那边啦"}}}, {"seq": 1749178899000, "time": "2025-06-06T11:01:39+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "“那边”是谁？"}, {"seq": 1749178924000, "time": "2025-06-06T11:02:04+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "就是国家级和您合作的那个单位呀"}, {"seq": 1749178953000, "time": "2025-06-06T11:02:33+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "不是省级吗？"}, {"seq": 1749179015000, "time": "2025-06-06T11:03:35+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "对呀，但是国家级和省级申报所需要的材料差不多，然后您国家级又报过，那材料应该整理过的，应该是有发给另外那个机构呀"}, {"seq": 1749179036000, "time": "2025-06-06T11:03:56+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "直接问那边要才了的意思？"}, {"seq": 1749179040000, "time": "2025-06-06T11:04:00+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "材料"}, {"seq": 1749179087000, "time": "2025-06-06T11:04:47+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "倒也不用，您自己有就OK，到时候我们匹配到单位了也会需要您的材料，我直接跟您联系您发给我就OK"}, {"seq": 1749179102000, "time": "2025-06-06T11:05:02+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "哦，好的"}, {"seq": 1749179256000, "time": "2025-06-06T11:07:36+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "嗯嗯，我这边先为您匹配着哦"}, {"seq": 1749179269000, "time": "2025-06-06T11:07:49+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "wxid_kp8m1qgegnlw12", "senderName": "43057 蒋文广", "isSelf": false, "type": 1, "subType": 0, "content": "好的，多谢"}, {"seq": 1749179311000, "time": "2025-06-06T11:08:31+08:00", "talker": "wxid_kp8m1qgegnlw12", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 47, "subType": 0, "content": "<msg><emoji fromusername=\"wxid_6ahbbsar7g8i22\" tousername=\"wxid_kp8m1qgegnlw12\" type=\"2\" androidmd5=\"26122ec19aed2acce50ee9ded270770c\" androidlen=\"5050\" aeskey=\"772fda894cee44c7857ac9cfbb90ca2f\" encrypturl=\"http://vweixinf.tc.qq.com/110/20402/stodownload?m=7837d8314d2685c916ee03a7068763fe&amp;filekey=30440201010430302e02016e04025348042037383337643833313464323638356339313665653033613730363837363366650203017370040d00000004627466730000000132&amp;hy=SH&amp;storeid=2640b0be10008210b0b0a68d00000006e02004fb25348111418b0b6c6cbfc9&amp;ef=2&amp;bizid=1022\" externurl=\"http://vweixinf.tc.qq.com/110/20403/stodownload?m=807123b2398ad16c99c156a9ef6544c5&amp;filekey=3043020101042f302d02016e0402534804203830373132336232333938616431366339396331353661396566363534346335020213c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2640b0be10008c1ba0b0a68d00000006e03004fb35348111418b0b6c6cbfd4&amp;ef=3&amp;bizid=1022\" externmd5=\"0a02e7bbdef44dc0a6848a4cffe73309\"/></msg>"}]}