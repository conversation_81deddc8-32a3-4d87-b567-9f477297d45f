#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import time

def test_user_json_update():
    """测试用户JSON文件名更新功能"""
    print("🔄 测试用户JSON文件名更新功能")
    print("=" * 60)
    
    # 查看现有用户文件夹
    data_dir = "data"
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return False
    
    # 获取现有用户列表
    users = []
    for item in os.listdir(data_dir):
        item_path = os.path.join(data_dir, item)
        if os.path.isdir(item_path):
            users.append(item)
    
    print(f"📁 找到 {len(users)} 个用户文件夹:")
    for user in users[:5]:  # 只显示前5个
        print(f"   - {user}")
    if len(users) > 5:
        print(f"   ... 还有 {len(users) - 5} 个用户")
    
    # 选择一个测试用户
    if not users:
        print("❌ 没有找到用户文件夹")
        return False
    
    test_user = users[0]  # 使用第一个用户进行测试
    print(f"\n🎯 选择测试用户: {test_user}")
    
    # 查看用户文件夹内容
    user_folder = os.path.join(data_dir, test_user)
    files = os.listdir(user_folder)
    
    print(f"📂 用户文件夹内容:")
    json_files = []
    for file in files:
        print(f"   - {file}")
        if file.endswith(f"_{test_user}.json") and not file.startswith("new_contacts"):
            json_files.append(file)
    
    if not json_files:
        print(f"❌ 用户 {test_user} 没有基准JSON文件")
        return False
    
    current_json_file = json_files[0]
    print(f"📄 当前JSON文件: {current_json_file}")
    
    # 从文件名提取当前IP
    current_ip_part = current_json_file.replace(f"_{test_user}.json", "")
    current_ip = current_ip_part.replace("_", ".")
    print(f"🌐 当前IP地址: {current_ip}")
    
    # 测试不同的IP变化场景
    test_cases = [
        {
            "name": "相同IP地址",
            "new_ip": current_ip,
            "expected": "无需更新"
        },
        {
            "name": "新的内网IP",
            "new_ip": "*************",
            "expected": "文件名更新"
        },
        {
            "name": "另一个内网IP",
            "new_ip": "**********",
            "expected": "文件名更新"
        },
        {
            "name": "公网IP",
            "new_ip": "************",
            "expected": "文件名更新"
        }
    ]
    
    api_url = "http://127.0.0.1:8080/report_ip"
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"🌐 新IP地址: {test_case['new_ip']}")
        print(f"🎯 预期结果: {test_case['expected']}")
        
        # 记录测试前的文件状态
        files_before = set(os.listdir(user_folder))
        
        # 发送IP变化请求
        data = {
            "username": test_user,
            "ip": test_case['new_ip']
        }
        
        try:
            response = requests.post(
                api_url,
                json=data,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ IP报告提交成功!")
                
                # 检查文件变化
                time.sleep(0.5)  # 等待文件操作完成
                files_after = set(os.listdir(user_folder))
                
                # 查找新的JSON文件
                new_json_files = [f for f in files_after if f.endswith(f"_{test_user}.json") and not f.startswith("new_contacts")]
                
                if new_json_files:
                    new_json_file = new_json_files[0]
                    print(f"📄 更新后JSON文件: {new_json_file}")
                    
                    # 验证IP地址是否正确更新
                    expected_filename = f"{test_case['new_ip'].replace('.', '_')}_{test_user}.json"
                    if new_json_file == expected_filename:
                        print("✅ 文件名更新正确!")
                        success_count += 1
                    else:
                        print(f"❌ 文件名更新错误!")
                        print(f"   期望: {expected_filename}")
                        print(f"   实际: {new_json_file}")
                else:
                    print("❌ 未找到更新后的JSON文件")
                
                # 检查是否生成了IP变化日志
                log_file = os.path.join(user_folder, "ip_change_log.json")
                if os.path.exists(log_file):
                    print("✅ IP变化日志已生成")
                    try:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            logs = json.load(f)
                        print(f"📝 日志条目数: {len(logs)}")
                        if logs:
                            latest_log = logs[-1]
                            print(f"   最新记录: {latest_log['new_ip_address']} ({latest_log['timestamp']})")
                    except Exception as e:
                        print(f"⚠️ 读取日志失败: {e}")
                else:
                    print("⚠️ 未找到IP变化日志")
                
            else:
                print(f"❌ IP报告提交失败: {response.status_code}")
                try:
                    error_result = response.json()
                    print(f"📄 错误内容: {error_result.get('message', '未知错误')}")
                except:
                    print(f"📄 错误内容: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        
        # 添加延迟
        if i < len(test_cases):
            time.sleep(1)
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个测试通过")
    return success_count == len(test_cases)

def check_user_data_structure():
    """检查用户数据结构"""
    print("\n🔍 检查用户数据结构")
    print("=" * 60)
    
    data_dir = "data"
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    users = []
    for item in os.listdir(data_dir):
        item_path = os.path.join(data_dir, item)
        if os.path.isdir(item_path):
            users.append(item)
    
    print(f"📁 用户总数: {len(users)}")
    
    # 分析每个用户的文件结构
    structure_summary = {
        "users_with_json": 0,
        "users_with_logs": 0,
        "total_json_files": 0,
        "ip_patterns": set()
    }
    
    for user in users[:10]:  # 检查前10个用户
        user_folder = os.path.join(data_dir, user)
        files = os.listdir(user_folder)
        
        json_files = [f for f in files if f.endswith(f"_{user}.json") and not f.startswith("new_contacts")]
        log_files = [f for f in files if f == "ip_change_log.json"]
        
        if json_files:
            structure_summary["users_with_json"] += 1
            structure_summary["total_json_files"] += len(json_files)
            
            # 提取IP模式
            for json_file in json_files:
                ip_part = json_file.replace(f"_{user}.json", "")
                ip_address = ip_part.replace("_", ".")
                structure_summary["ip_patterns"].add(ip_address.split(".")[0])  # 第一段
        
        if log_files:
            structure_summary["users_with_logs"] += 1
        
        print(f"👤 {user}:")
        print(f"   JSON文件: {len(json_files)} 个")
        print(f"   日志文件: {len(log_files)} 个")
        if json_files:
            print(f"   基准文件: {json_files[0]}")
    
    print(f"\n📊 结构汇总:")
    print(f"   有JSON文件的用户: {structure_summary['users_with_json']}")
    print(f"   有日志文件的用户: {structure_summary['users_with_logs']}")
    print(f"   JSON文件总数: {structure_summary['total_json_files']}")
    print(f"   IP地址段: {', '.join(sorted(structure_summary['ip_patterns']))}")

def main():
    """主测试函数"""
    print("🚀 用户JSON文件更新功能测试")
    print("=" * 70)
    
    # 检查数据结构
    check_user_data_structure()
    
    # 测试文件更新功能
    print("\n" + "=" * 70)
    success = test_user_json_update()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 所有测试都通过了！用户JSON文件更新功能正常工作")
        print("\n💡 功能说明:")
        print("1. 当SendIp.exe报告IP变化时，系统会自动更新对应用户文件夹下的JSON文件名")
        print("2. 文件名格式: {新IP地址}_{用户名}.json (IP中的点替换为下划线)")
        print("3. 系统会在用户文件夹中生成ip_change_log.json记录变化历史")
        print("4. 旧的JSON文件会被重命名为新的文件名，保持数据完整性")
    else:
        print("⚠️ 部分测试失败，请检查服务器状态和数据结构")
        print("\n💡 常见问题排查:")
        print("1. 确保Flask应用正在运行")
        print("2. 检查data目录和用户文件夹权限")
        print("3. 确认用户文件夹下有正确格式的JSON文件")

if __name__ == "__main__":
    main()
