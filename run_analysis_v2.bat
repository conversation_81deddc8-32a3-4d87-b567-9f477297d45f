@echo off
title 微信联系人重复分析工具 V2
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                微信联系人重复分析工具 V2                     ║
echo ║                                                              ║
echo ║  功能: 分析各用户文件夹中的联系人重复情况                   ║
echo ║  输入: data/用户名/联系人文件.json                          ║
echo ║  输出: analysis_reports/ 目录中的分析报告                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查data目录
if not exist "data" (
    echo ❌ 错误: 未找到data目录
    pause
    exit /b 1
)

REM 检查是否有用户文件夹
dir /b data 2>nul | findstr /r "^[^.]" >nul
if errorlevel 1 (
    echo ❌ 错误: data目录中没有用户文件夹
    echo 请确保data目录包含用户文件夹，如: data/张三/, data/李四/
    pause
    exit /b 1
)

REM 创建报告目录
if not exist "analysis_reports" mkdir analysis_reports

echo 📊 当前数据结构:
echo.
for /d %%i in (data\*) do (
    echo    📂 %%~ni/
    for %%j in ("%%i\*.json") do (
        if not "%%~nj"=="new_contacts_log" if not "%%~nj"=="ip_change_log" (
            echo       📄 %%~nxj
        )
    )
)

echo.
echo 🚀 开始分析...
echo.

python analyze_contacts_v2.py

if errorlevel 1 (
    echo.
    echo ❌ 分析过程中发生错误
) else (
    echo.
    echo ✅ 分析完成！
    echo.
    echo 📁 报告文件已生成到 analysis_reports/ 目录:
    echo    📊 duplicate_analysis_*.json     - 详细分析报告
    echo    📋 duplicate_summary_*.csv       - 重复联系人摘要
    echo    🔄 user_comparison_*.csv         - 用户对比表
    echo    📈 statistics_*.txt              - 统计报告
    echo    📝 analysis.log                  - 分析日志
    echo.
    
    set /p open_reports="是否打开报告目录? (y/N): "
    if /i "%open_reports%"=="y" (
        explorer analysis_reports
    )
)

echo.
pause
