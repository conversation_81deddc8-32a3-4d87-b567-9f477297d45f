#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文档内容提取测试脚本
测试PyPDF2库是否能正确提取PDF内容
"""

import os
from pathlib import Path

def extract_pdf_content(file_path):
    """提取PDF文档内容"""
    try:
        # 尝试使用PyPDF2库
        try:
            import PyPDF2
            print(f"📄 正在提取PDF文档内容: {file_path}")
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                content = []
                
                print(f"📊 PDF页数: {len(reader.pages)}")
                
                for i, page in enumerate(reader.pages):
                    page_text = page.extract_text()
                    print(f"📄 第{i+1}页提取字符数: {len(page_text)}")
                    if page_text.strip():
                        content.append(f"=== 第{i+1}页 ===\n{page_text}")
                    else:
                        content.append(f"=== 第{i+1}页 ===\n[此页无法提取文本内容]")
                
            full_content = '\n\n'.join(content)
            print(f"✅ PDF文档内容提取成功，共 {len(full_content)} 字符")
            return full_content
        except ImportError:
            print("⚠️ PyPDF2库未安装，无法提取PDF文档内容")
            return None
        except Exception as e:
            print(f"⚠️ PDF文档解析失败: {e}")
            return None
    except Exception as e:
        print(f"❌ PDF文档内容提取失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("📄 PDF文档内容提取测试")
    print("=" * 60)
    
    # 查找downloaded_files文件夹中的PDF文件
    download_folder = Path("downloaded_files")
    if not download_folder.exists():
        print(f"❌ 下载文件夹不存在: {download_folder}")
        return
    
    # 查找所有PDF文件
    pdf_files = list(download_folder.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ 在downloaded_files文件夹中没有找到PDF文件")
        print("📁 文件夹内容:")
        for file in download_folder.iterdir():
            print(f"   - {file.name}")
        return
    
    print(f"📁 找到 {len(pdf_files)} 个PDF文件:")
    for pdf_file in pdf_files:
        print(f"   - {pdf_file.name}")
    
    # 测试每个PDF文件
    for pdf_file in pdf_files:
        print("\n" + "=" * 60)
        print(f"🔍 测试文件: {pdf_file.name}")
        print("=" * 60)
        
        print(f"📏 文件大小: {pdf_file.stat().st_size} 字节")
        
        # 提取内容
        content = extract_pdf_content(pdf_file)
        
        if content:
            print("\n📋 提取的内容预览（前500字符）:")
            print("-" * 40)
            print(content[:500])
            if len(content) > 500:
                print("\n[内容已截断...]")
            print("-" * 40)
            
            # 保存提取的内容到文件
            output_file = f"pdf_extracted_{pdf_file.stem}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"PDF文件: {pdf_file.name}\n")
                f.write(f"提取时间: {Path().cwd()}\n")
                f.write("=" * 60 + "\n")
                f.write(content)
            print(f"💾 提取内容已保存到: {output_file}")
        else:
            print("❌ 无法提取PDF内容")

if __name__ == "__main__":
    main()
