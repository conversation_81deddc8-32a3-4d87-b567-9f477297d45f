/* 微信聊天记录查看器样式 */

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar,
.contacts-list::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track,
.contacts-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb,
.contacts-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover,
.contacts-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 消息气泡动画 */
.message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 按钮悬停效果 */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* 联系人项目悬停效果 */
.contact-item {
    position: relative;
    overflow: hidden;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(7,193,96,0.1), transparent);
    transition: left 0.3s;
}

.contact-item:hover::before {
    left: 100%;
}

/* 头像渐变效果 */
.avatar {
    background: linear-gradient(135deg, #07c160, #06ad56);
    box-shadow: 0 2px 8px rgba(7,193,96,0.3);
}

.message.self .avatar {
    background: linear-gradient(135deg, #1989fa, #1677ff);
    box-shadow: 0 2px 8px rgba(25,137,250,0.3);
}

/* 消息气泡阴影 */
.message-bubble {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.message.self .message-bubble {
    box-shadow: 0 1px 3px rgba(7,193,96,0.3);
}

/* 搜索框聚焦效果 */
.search-input:focus {
    box-shadow: 0 0 0 3px rgba(7,193,96,0.1);
}

/* 响应式优化 */
@media (max-width: 480px) {
    .avatar {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
    
    .message-bubble {
        font-size: 14px;
        padding: 10px 12px;
    }
    
    .contact-item {
        padding: 12px 15px;
    }
    
    .contact-id {
        font-size: 14px;
    }
    
    .contact-name {
        font-size: 13px;
    }
}

/* 加载动画 */
.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #07c160;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border-radius: 8px;
    margin: 20px;
}

/* 空状态样式 */
.empty-state {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    margin: 20px;
}

.empty-state .icon {
    background: linear-gradient(135deg, #6c757d, #495057);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 统计信息样式 */
.stats {
    background: rgba(7,193,96,0.1);
    padding: 8px 12px;
    border-radius: 15px;
    margin-top: 8px;
    display: inline-flex;
}

.stat-item {
    background: rgba(255,255,255,0.8);
    padding: 2px 8px;
    border-radius: 10px;
    margin-right: 8px;
}

.stat-item:last-child {
    margin-right: 0;
}

/* 聊天标题区域 */
.chat-header {
    background: linear-gradient(135deg, #fafafa, #f0f0f0);
    border-bottom: 2px solid #e0e0e0;
}

/* 消息时间样式优化 */
.message-time {
    background: rgba(0,0,0,0.05);
    padding: 2px 6px;
    border-radius: 8px;
    display: inline-block;
    margin-top: 3px;
}

/* 活跃联系人样式 */
.contact-item.active {
    background: linear-gradient(135deg, #e7f7e7, #d4f4d4);
    border-left: 4px solid #07c160;
    box-shadow: 0 2px 8px rgba(7,193,96,0.2);
}

/* 主容器阴影 */
.container > div {
    transition: box-shadow 0.3s ease;
}

.container > div:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

/* 搜索区域样式 */
.search-section {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}
