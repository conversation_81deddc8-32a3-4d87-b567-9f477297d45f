"""
测试GUI工作流程
模拟用户操作流程
"""

import os
import json
import time
from pathlib import Path

def test_workflow():
    """测试完整的工作流程"""
    print("="*60)
    print("GUI工作流程测试")
    print("="*60)
    
    # 1. 检查data目录结构
    print("\n1. 检查目录结构...")
    data_dir = Path("data")
    if data_dir.exists():
        print(f"✅ data目录存在")
        subdirs = [d for d in data_dir.iterdir() if d.is_dir()]
        print(f"   现有用户文件夹: {[d.name for d in subdirs]}")
    else:
        print(f"⚠️  data目录不存在，将会自动创建")
    
    # 2. 模拟用户文件夹创建
    test_username = "test_user"
    test_folder = data_dir / test_username
    
    print(f"\n2. 模拟创建用户文件夹: {test_folder}")
    try:
        test_folder.mkdir(parents=True, exist_ok=True)
        print(f"✅ 用户文件夹创建成功: {test_folder}")
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        return
    
    # 3. 模拟联系人文件保存
    print(f"\n3. 模拟联系人文件保存...")
    test_contacts = [
        {
            "UserName": "test_contact_001",
            "NickName": "测试联系人1",
            "Alias": "",
            "Remark": "这是一个测试联系人"
        },
        {
            "UserName": "test_contact_002", 
            "NickName": "测试联系人2",
            "Alias": "testalias",
            "Remark": ""
        },
        {
            "UserName": "filehelper",
            "NickName": "文件传输助手",
            "Alias": "",
            "Remark": ""
        }
    ]
    
    test_file = test_folder / "192_168_1_116_test_user.json"
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_contacts, f, ensure_ascii=False, indent=2)
        print(f"✅ 联系人文件保存成功: {test_file}")
        print(f"   包含 {len(test_contacts)} 个联系人")
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")
        return
    
    # 4. 模拟新增联系人检测
    print(f"\n4. 模拟新增联系人检测...")
    
    # 添加新联系人
    new_contacts = test_contacts + [
        {
            "UserName": "new_contact_001",
            "NickName": "新增联系人1", 
            "Alias": "",
            "Remark": "这是新增的联系人"
        }
    ]
    
    # 检测新增
    old_usernames = {contact["UserName"] for contact in test_contacts}
    new_usernames = {contact["UserName"] for contact in new_contacts}
    added_usernames = new_usernames - old_usernames
    
    print(f"   原有联系人: {len(test_contacts)}")
    print(f"   更新后联系人: {len(new_contacts)}")
    print(f"   新增联系人: {len(added_usernames)}")
    
    if added_usernames:
        print(f"   新增的用户名: {list(added_usernames)}")
        
        # 更新文件
        try:
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(new_contacts, f, ensure_ascii=False, indent=2)
            print(f"✅ 文件已更新，替换原文件")
        except Exception as e:
            print(f"❌ 更新失败: {str(e)}")
    
    # 5. 显示最终状态
    print(f"\n5. 最终状态...")
    print(f"   用户文件夹: {test_folder}")
    print(f"   联系人文件: {test_file}")
    print(f"   文件大小: {test_file.stat().st_size} 字节")
    
    # 6. 清理测试文件（可选）
    cleanup = input(f"\n是否清理测试文件? (y/N): ").lower().strip()
    if cleanup == 'y':
        try:
            test_file.unlink()
            test_folder.rmdir()
            print(f"✅ 测试文件已清理")
        except Exception as e:
            print(f"⚠️  清理失败: {str(e)}")
    else:
        print(f"📁 测试文件保留在: {test_folder}")
    
    print(f"\n" + "="*60)
    print("测试完成！")
    print("="*60)

def show_gui_usage():
    """显示GUI使用说明"""
    print("""
🎯 GUI使用流程说明：

1. 启动程序
   python wechat_gui.py

2. 输入用户名并提交
   - 在"用户名"输入框中输入您的用户名
   - 点击"提交用户名"按钮
   - 系统会自动创建 data/用户名/ 文件夹

3. 首次点击"确认"
   - 立即获取一次所有联系人信息
   - 保存到用户文件夹中
   - 启动定时任务（每天09:00和13:00）

4. 后续自动运行
   - 每天09:00自动获取联系人
   - 每天13:00自动获取联系人
   - 自动检测新增人员
   - 替换原文件（保持最新状态）

5. 查看状态
   - 点击"查看状态"按钮查看当前状态
   - 查看日志了解详细信息

📁 文件结构：
data/
├── 用户名1/
│   └── IP_用户名1.json
├── 用户名2/
│   └── IP_用户名2.json
└── ...

⏰ 定时任务：
- 09:00 - 自动获取联系人
- 13:00 - 自动获取联系人

🆕 新增检测：
- 自动对比上次联系人列表
- 记录新增的联系人
- 在日志中显示新增信息
""")

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        test_workflow()
    else:
        show_gui_usage()

if __name__ == "__main__":
    main()
