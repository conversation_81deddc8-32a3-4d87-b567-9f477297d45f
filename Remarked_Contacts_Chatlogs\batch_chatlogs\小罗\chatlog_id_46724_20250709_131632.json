{"user": "小罗", "contact_id": "46724", "contact_info": {"UserName": "Agile406015", "Alias": "", "Remark": "46724 张晓涛", "NickName": "对方正在P图…"}, "fetch_time": "2025-07-09T13:16:32.435289", "message_count": 35, "chatlog_data": [{"seq": 1722582382000, "time": "2024-08-02T15:06:22+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "我通过了你的朋友验证请求，现在我们可以开始聊天了"}, {"seq": 1722582838000, "time": "2024-08-02T15:13:58+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "张博 您好 我是刚才电话的toptalents的小罗"}, {"seq": 1722582849000, "time": "2024-08-02T15:14:09+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "你好"}, {"seq": 1722583142000, "time": "2024-08-02T15:19:02+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "张博 您这边有了解过国内的人才计划吗？"}, {"seq": 1722583364000, "time": "2024-08-02T15:22:44+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "有了解过，qm，hy这些"}, {"seq": 1722583380000, "time": "2024-08-02T15:23:00+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "有申报过吗？"}, {"seq": 1722583388000, "time": "2024-08-02T15:23:08+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "有"}, {"seq": 1722583403000, "time": "2024-08-02T15:23:23+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "2024的QM吗？"}, {"seq": 1722583852000, "time": "2024-08-02T15:30:52+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "是的"}, {"seq": 1722583886000, "time": "2024-08-02T15:31:26+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "依托哪里的单位申报的呢？"}, {"seq": 1722585503000, "time": "2024-08-02T15:58:23+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "是依托四川的单位"}, {"seq": 1722585618000, "time": "2024-08-02T16:00:18+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您对国内的工作地点有要求吗？"}, {"seq": 1722585810000, "time": "2024-08-02T16:03:30+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "目前没有吧"}, {"seq": 1722585872000, "time": "2024-08-02T16:04:32+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "好的 我现在在做浙江的SQ项目 要求博士全职回国入职 再进行标准化认定 您是符合标准化 入选的概率会很高"}, {"seq": 1722585926000, "time": "2024-08-02T16:05:26+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "请问认定标准是什么呢"}, {"seq": 1722586026000, "time": "2024-08-02T16:07:06+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "博士毕业后 具有3+的海外工作经验 目前工作的单位是世界500强或者QS排名前100的高校"}, {"seq": 1722586752000, "time": "2024-08-02T16:19:12+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "请问QS认定一般在什么时候呢"}, {"seq": 1722586798000, "time": "2024-08-02T16:19:58+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "一般在博士入职3个月之后进行认定"}, {"seq": 1722587361000, "time": "2024-08-02T16:29:21+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "没有统一规定的认定时间吗"}, {"seq": 1722587392000, "time": "2024-08-02T16:29:52+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "今年应该是在年底"}, {"seq": 1722587454000, "time": "2024-08-02T16:30:54+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "年底是在12月还是"}, {"seq": 1722587589000, "time": "2024-08-02T16:33:09+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是的 在12月左右"}, {"seq": 1723191814000, "time": "2024-08-09T16:23:34+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "张博 https://cn.elecontro.com/ 太科 您看看这家单位有没有兴趣考虑？"}, {"seq": 1724663157000, "time": "2024-08-26T17:05:57+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": " 张博 这边有一个山东的单位 看了您的履历 对您很感兴趣 想跟您对接 "}, {"seq": 1724663157000, "time": "2024-08-26T17:05:57+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 49, "subType": 6, "content": "", "contents": {"md5": "5ba3de1c7c1663151c319a84354adc93", "title": "胜宁ppt正式版(1).pptx"}}, {"seq": 1726302608000, "time": "2024-09-14T16:30:08+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 3, "subType": 0, "content": "", "contents": {"md5": "ee2cf92591363b702c689c2c5974eb48"}}, {"seq": 1726302608000, "time": "2024-09-14T16:30:08+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "月圆家圆人圆事圆圆圆团团，国和家和人和事和和和美美，祝您全家幸福和气满堂，阖家欢乐！"}, {"seq": 1728608069000, "time": "2024-10-11T08:54:29+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "张博 您好 最近怎么样啊？还在考虑国内的机会吗？"}, {"seq": 1735198435000, "time": "2024-12-26T15:33:55+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "张博 您好! 我这边有一个不错的单位 跟您的背景很匹配 您看一下有没有兴趣考虑对接？"}, {"seq": 1735198443000, "time": "2024-12-26T15:34:03+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "https://cn.chinajack.com/"}, {"seq": 1736759670000, "time": "2025-01-13T17:14:30+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "您好，我暂时先不考虑其他的就业机会了，感谢您的帮助🙏"}, {"seq": 1736759719000, "time": "2025-01-13T17:15:19+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您已经有合适的单位了吗？"}, {"seq": 1736759763000, "time": "2025-01-13T17:16:03+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "Agile406015", "senderName": "46724 张晓涛", "isSelf": false, "type": 1, "subType": 0, "content": "是的"}, {"seq": 1736759854000, "time": "2025-01-13T17:17:34+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "是回国还是继续在国外啊？"}, {"seq": 1747891766000, "time": "2025-05-22T13:29:26+08:00", "talker": "Agile406015", "talkerName": "", "isChatRoom": false, "sender": "wxid_8rgvh4mwvr3y22", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您好！为汇聚创新力量，特诚邀您参加创客天下创业大赛。以下是赛事奖励及扶持要点：\n\n专项赛资助 ：专项赛综合排名 3 - 5 名项目市里资助 100 万元；总决赛一、二、三等奖项目市里分别资助 500 万、300 万、200 万元。\n总决赛奖金 ：总决赛设一等奖 1 个（20 万元）、二等奖 5 个（10 万元）、三等奖 10 个（5 万元），奖金与专项赛按就高不重复兑付。\n差旅补助 ：参赛选手可享差旅补助，标准为非亚洲国际 7000 元 / 人、亚洲地区 3500 元 / 人、长三角外 2000 元 / 人、长三角内浙江省外 1000 元 / 人、省内 500 元 / 人，交通补贴按最高限额凭票报销，国际旅费 7 日内、国内差旅 3 日内到杭，限经济舱。\n落地资助 ：同一项目按就高不重复原则给予落地资助。\n这是一场展示创新成果、对接资源的盛会，期待您加入，共创未来！"}]}