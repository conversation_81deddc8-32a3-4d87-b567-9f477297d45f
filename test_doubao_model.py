#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包模型测试脚本
测试不同类型的提示词，找出模型拒绝回答的原因
"""

from volcenginesdkarkruntime import Ark
import json

# 豆包AI配置
DOUBAO_API_KEY = "cdd675a2-53d4-4aa1-bba8-33340ada088f"
DOUBAO_MODEL = "doubao-seed-1.6-250615"

def init_doubao_client():
    """初始化豆包客户端"""
    try:
        client = Ark(api_key=DOUBAO_API_KEY)
        return client
    except Exception as e:
        print(f"❌ 豆包客户端初始化失败: {e}")
        return None

def test_simple_question():
    """测试简单问题"""
    print("🧪 测试1: 简单问题")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": "你好，请介绍一下你自己"}
            ],
            max_tokens=200,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_text_analysis():
    """测试文本分析任务"""
    print("\n🧪 测试2: 文本分析任务")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        prompt = """
请分析以下文本内容，提取关键信息：

文本内容：
"我是一名软件工程师，有5年的Java开发经验，目前在北京工作。我对人工智能很感兴趣，希望能找到相关的工作机会。"

请提取以下信息：
1. 职业：
2. 技能：
3. 工作地点：
4. 兴趣方向：

请简洁回答。
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_tag_extraction():
    """测试标签提取任务"""
    print("\n🧪 测试3: 标签提取任务")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        prompt = """
请从以下文本中提取关键标签：

文本: "张教授是清华大学的长江学者，专门研究人工智能"

请提取标签，用逗号分隔：
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_chat_analysis():
    """测试聊天记录分析任务"""
    print("\n🧪 测试4: 聊天记录分析任务")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        prompt = """
请分析以下对话内容：

对话记录：
用户A: 你好，我想了解一下贵公司的职位
用户B: 您好，请问您的专业背景是什么？
用户A: 我是计算机专业，有3年Java开发经验
用户B: 很好，我们正好有Java开发的职位

请分析：
1. 对话主题是什么？
2. 用户A的背景如何？
3. 对话结果如何？

请简洁回答。
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=400,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_sensitive_content():
    """测试可能敏感的内容"""
    print("\n🧪 测试5: 可能敏感的内容")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        prompt = """
请分析以下微信聊天记录，提取候选人信息：

聊天记录：
猎头: 您好，我是猎头顾问
候选人: 你好，我目前在找工作
猎头: 请问您的期望薪资是多少？
候选人: 希望年薪30万左右

请提取：候选人的求职状态和薪资期望
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=300,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_original_prompt():
    """测试原始的昵称分析提示词"""
    print("\n🧪 测试6: 原始昵称分析提示词")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        prompt = """作为一个文本分析助手，请帮我从以下用户昵称中提取关键信息标签。

用户昵称: 21胡艺华英国正教授省青千

请提取以下类型的信息标签：
- 技术技能：如Java、Python、前端、后端等
- 职业角色：如工程师、经理、设计师等  
- 工作经验：如1年、3年、5年等
- 地理位置：如北京、上海、深圳等城市名
- 教育背景：如教授、博士、研究员等学术称谓
- 特殊头衔：如长江学者、青千、优青等

输出格式：
- 只输出找到的标签，用逗号分隔
- 如果没有找到相关信息，输出"无标签"
- 不要输出解释或其他内容

示例输出：Java,后端,北京,5年经验"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_simplified_prompt():
    """测试简化的提示词"""
    print("\n🧪 测试7: 简化的提示词")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        prompt = """
请从这个文本中找出关键词：

文本: "21胡艺华英国正教授省青千"

请找出：地点、职业、头衔等关键词，用逗号分隔。
"""
        
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": prompt}
            ],
            max_tokens=200,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_model_info():
    """测试模型信息"""
    print("\n🧪 测试8: 模型信息")
    print("-" * 50)
    
    client = init_doubao_client()
    if not client:
        return
    
    try:
        response = client.chat.completions.create(
            model=DOUBAO_MODEL,
            messages=[
                {"role": "user", "content": "请告诉我你的模型名称和版本"}
            ],
            max_tokens=200,
            temperature=0.3,
        )
        
        result = response.choices[0].message.content
        print(f"✅ 回答: {result}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 豆包模型测试开始")
    print("=" * 60)
    print(f"模型: {DOUBAO_MODEL}")
    print(f"API Key: {DOUBAO_API_KEY[:20]}...")
    print("=" * 60)
    
    # 运行所有测试
    test_simple_question()
    test_text_analysis()
    test_tag_extraction()
    test_chat_analysis()
    test_sensitive_content()
    test_original_prompt()
    test_simplified_prompt()
    test_model_info()
    
    print("\n🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
