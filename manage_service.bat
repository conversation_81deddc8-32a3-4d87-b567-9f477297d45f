@echo off
echo 微信联系人分析服务管理工具
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 错误: 需要管理员权限来管理Windows服务
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 选择操作:
echo 1. 安装服务
echo 2. 启动服务
echo 3. 停止服务
echo 4. 卸载服务
echo 5. 查看服务状态
echo 6. 以控制台模式运行 (用于测试)
echo.

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" (
    echo.
    echo 正在安装服务...
    pip install pywin32
    python contact_analyzer_service.py install
    if %errorLevel% equ 0 (
        echo 服务安装成功！
        echo 服务名称: ContactAnalyzerService
        echo 显示名称: 微信联系人分析服务
    ) else (
        echo 服务安装失败！
    )
) else if "%choice%"=="2" (
    echo.
    echo 正在启动服务...
    python contact_analyzer_service.py start
    if %errorLevel% equ 0 (
        echo 服务启动成功！
    ) else (
        echo 服务启动失败！
    )
) else if "%choice%"=="3" (
    echo.
    echo 正在停止服务...
    python contact_analyzer_service.py stop
    if %errorLevel% equ 0 (
        echo 服务停止成功！
    ) else (
        echo 服务停止失败！
    )
) else if "%choice%"=="4" (
    echo.
    echo 正在卸载服务...
    python contact_analyzer_service.py remove
    if %errorLevel% equ 0 (
        echo 服务卸载成功！
    ) else (
        echo 服务卸载失败！
    )
) else if "%choice%"=="5" (
    echo.
    echo 查看服务状态...
    sc query ContactAnalyzerService
) else if "%choice%"=="6" (
    echo.
    echo 以控制台模式运行...
    echo 按Ctrl+C可以停止程序
    echo.
    python contact_analyzer_service.py
) else (
    echo 无效选择，退出程序
)

echo.
pause
