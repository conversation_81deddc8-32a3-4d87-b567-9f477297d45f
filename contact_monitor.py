# 监控所有用户的微信联系人变化，包括新增联系人记录、联系人总数变化、IP地址变化等信息的实时监控工具

import json
import os
import time
from pathlib import Path
from datetime import datetime
from collections import defaultdict
import threading

class ContactMonitor:
    def __init__(self):
        self.data_dir = Path("data")
        self.excluded_folders = {"test_ip_change_user"}
        self.monitoring = False
        self.last_check_time = None
        self.user_stats = {}
        self.baseline_data = {}  # 存储基准数据用于比较
        self.change_log = []     # 记录所有检测到的变化
        self.baseline_file = Path("contact_baseline.json")  # 基准数据文件
        
    def get_all_users(self):
        """获取所有用户文件夹"""
        if not self.data_dir.exists():
            print("❌ data目录不存在")
            return []
        
        user_folders = [d for d in self.data_dir.iterdir() 
                       if d.is_dir() and d.name not in self.excluded_folders]
        return sorted(user_folders)
    
    def get_user_contact_stats(self, user_folder):
        """获取单个用户的联系人统计信息"""
        username = user_folder.name
        stats = {
            'username': username,
            'total_contacts': 0,
            'contact_files': [],
            'new_contacts_records': 0,
            'total_new_contacts': 0,
            'last_new_contact_time': '无记录',
            'ip_changes': 0,
            'last_update_time': '未知',
            'current_ip': '未知'
        }
        
        try:
            # 1. 分析联系人文件
            contact_files = [f for f in user_folder.glob("*.json") 
                           if not f.name.startswith(("new_contacts", "ip_change"))]
            
            if contact_files:
                # 按修改时间排序，获取最新文件
                latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
                stats['last_update_time'] = datetime.fromtimestamp(latest_file.stat().st_mtime).strftime("%Y-%m-%d %H:%M:%S")
                
                # 从文件名提取IP地址
                filename = latest_file.name
                if '_' in filename:
                    parts = filename.replace('.json', '').split('_')
                    if len(parts) >= 4:
                        stats['current_ip'] = '_'.join(parts[:-1])
                
                # 读取联系人数量
                try:
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        contacts = json.load(f)
                        if isinstance(contacts, list):
                            stats['total_contacts'] = len(contacts)
                except Exception as e:
                    print(f"读取联系人文件失败 {latest_file}: {e}")
                
                stats['contact_files'] = [f.name for f in contact_files]
            
            # 2. 分析新增联系人记录
            new_contacts_file = user_folder / "new_contacts_log.json"
            if new_contacts_file.exists():
                try:
                    with open(new_contacts_file, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)
                    
                    records = log_data.get('records', [])
                    stats['new_contacts_records'] = len(records)
                    
                    total_new = 0
                    last_time = None
                    
                    for record in records:
                        total_new += record.get('count', 0)
                        record_time = record.get('detection_time')
                        if record_time:
                            last_time = record_time
                    
                    stats['total_new_contacts'] = total_new
                    if last_time:
                        stats['last_new_contact_time'] = last_time
                        
                except Exception as e:
                    print(f"读取新增联系人记录失败 {new_contacts_file}: {e}")
            
            # 3. 分析IP变化记录
            ip_change_file = user_folder / "ip_change_log.json"
            if ip_change_file.exists():
                try:
                    with open(ip_change_file, 'r', encoding='utf-8') as f:
                        ip_data = json.load(f)
                    
                    changes = ip_data.get('changes', [])
                    stats['ip_changes'] = len(changes)
                    
                except Exception as e:
                    print(f"读取IP变化记录失败 {ip_change_file}: {e}")
        
        except Exception as e:
            print(f"分析用户 {username} 失败: {e}")
        
        return stats
    
    def display_current_status(self):
        """显示当前所有用户的状态"""
        print("\n" + "="*80)
        print(f"📊 微信联系人监控状态报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        user_folders = self.get_all_users()
        if not user_folders:
            print("❌ 没有找到用户数据")
            return
        
        total_contacts = 0
        total_new_contacts = 0
        
        print(f"{'用户名':<12} {'联系人数':<8} {'新增记录':<8} {'总新增':<8} {'当前IP':<15} {'最后更新':<20}")
        print("-" * 80)
        
        for user_folder in user_folders:
            stats = self.get_user_contact_stats(user_folder)
            self.user_stats[stats['username']] = stats
            
            total_contacts += stats['total_contacts']
            total_new_contacts += stats['total_new_contacts']
            
            print(f"{stats['username']:<12} "
                  f"{stats['total_contacts']:<8} "
                  f"{stats['new_contacts_records']:<8} "
                  f"{stats['total_new_contacts']:<8} "
                  f"{stats['current_ip']:<15} "
                  f"{stats['last_update_time']:<20}")
        
        print("-" * 80)
        print(f"{'总计':<12} {total_contacts:<8} {'':<8} {total_new_contacts:<8}")
        print()
        
        # 显示详细的新增联系人信息
        self.display_new_contacts_details()
    
    def display_new_contacts_details(self):
        """显示新增联系人的详细信息"""
        print("🆕 新增联系人详情:")
        print("-" * 60)
        
        has_new_contacts = False
        
        for username, stats in self.user_stats.items():
            if stats['total_new_contacts'] > 0:
                has_new_contacts = True
                print(f"\n👤 {username}:")
                print(f"   总新增: {stats['total_new_contacts']} 个")
                print(f"   记录批次: {stats['new_contacts_records']} 次")
                print(f"   最后记录: {stats['last_new_contact_time']}")
                
                # 显示最近的新增记录
                self.show_recent_new_contacts(username)
        
        if not has_new_contacts:
            print("   暂无新增联系人记录")
    
    def show_recent_new_contacts(self, username):
        """显示用户最近的新增联系人"""
        new_contacts_file = self.data_dir / username / "new_contacts_log.json"
        
        if not new_contacts_file.exists():
            return
        
        try:
            with open(new_contacts_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
            
            records = log_data.get('records', [])
            if not records:
                return
            
            # 显示最近的记录
            recent_record = records[-1]
            contacts = recent_record.get('contacts', [])
            
            print(f"   最近一次新增 ({recent_record.get('detection_time', '未知时间')}):")
            for i, contact in enumerate(contacts[:3], 1):  # 只显示前3个
                nickname = contact.get('nickname', '无昵称')
                username_id = contact.get('username', '')
                print(f"     {i}. {nickname} ({username_id[:20]}...)")
            
            if len(contacts) > 3:
                print(f"     ... 还有 {len(contacts) - 3} 个")
                
        except Exception as e:
            print(f"   读取详细记录失败: {e}")
    
    def monitor_changes(self, interval=60):
        """持续监控文件变化"""
        print(f"🔍 开始监控联系人变化 (每{interval}秒检查一次)")
        print("按 Ctrl+C 停止监控")
        print("💡 提示: 监控会检测联系人数量变化、新增记录、IP变化等")

        self.monitoring = True
        self.initialize_baseline()  # 初始化基准数据

        try:
            while self.monitoring:
                # 检查是否有实际变化
                changes_detected = self.check_for_changes()

                if not changes_detected:
                    # 显示监控状态
                    print(f"⏰ {datetime.now().strftime('%H:%M:%S')} - 监控中，暂无变化")

                time.sleep(interval)

        except KeyboardInterrupt:
            print(f"\n⏹️  监控已停止 - {datetime.now().strftime('%H:%M:%S')}")
            self.monitoring = False

            # 自动保存变化日志
            if self.change_log:
                print("💾 自动保存变化日志...")
                self.save_change_log()
    
    def save_baseline_data(self):
        """保存基准数据到文件"""
        try:
            # 转换set为list以便JSON序列化
            baseline_to_save = {}
            for username, data in self.baseline_data.items():
                baseline_to_save[username] = data.copy()
                if 'contact_usernames' in data:
                    baseline_to_save[username]['contact_usernames'] = list(data['contact_usernames'])

            baseline_info = {
                "created_time": datetime.now().isoformat(),
                "description": "联系人监控基准数据",
                "baseline_data": baseline_to_save
            }

            with open(self.baseline_file, 'w', encoding='utf-8') as f:
                json.dump(baseline_info, f, ensure_ascii=False, indent=2)

            print(f"💾 基准数据已保存到 {self.baseline_file}")

        except Exception as e:
            print(f"❌ 保存基准数据失败: {e}")

    def load_baseline_data(self):
        """从文件加载基准数据"""
        if not self.baseline_file.exists():
            return False

        try:
            with open(self.baseline_file, 'r', encoding='utf-8') as f:
                baseline_info = json.load(f)

            baseline_data = baseline_info.get("baseline_data", {})

            # 转换list回set
            for username, data in baseline_data.items():
                if 'contact_usernames' in data:
                    data['contact_usernames'] = set(data['contact_usernames'])

            self.baseline_data = baseline_data
            created_time = baseline_info.get("created_time", "未知")
            print(f"📊 已加载基准数据 (创建时间: {created_time[:19]})")
            return True

        except Exception as e:
            print(f"❌ 加载基准数据失败: {e}")
            return False

    def initialize_baseline(self):
        """初始化基准数据"""
        # 先尝试加载现有基准数据
        if self.load_baseline_data():
            print(f"✅ 已加载 {len(self.baseline_data)} 个用户的基准数据")
            return

        print("📊 创建新的基准数据...")
        user_folders = self.get_all_users()

        for user_folder in user_folders:
            username = user_folder.name
            self.baseline_data[username] = self.get_user_contact_stats(user_folder)

            # 额外记录联系人的UserName列表用于精确比较
            contact_files = [f for f in user_folder.glob("*.json")
                           if not f.name.startswith(("new_contacts", "ip_change"))]

            if contact_files:
                latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)
                try:
                    with open(latest_file, 'r', encoding='utf-8') as f:
                        contacts = json.load(f)

                    if isinstance(contacts, list):
                        usernames = {contact.get("UserName", "") for contact in contacts if contact.get("UserName")}
                        self.baseline_data[username]['contact_usernames'] = usernames

                except Exception as e:
                    print(f"读取联系人文件失败 {latest_file}: {e}")
                    self.baseline_data[username]['contact_usernames'] = set()
            else:
                self.baseline_data[username]['contact_usernames'] = set()

        print(f"✅ 已初始化 {len(self.baseline_data)} 个用户的基准数据")

        # 保存基准数据
        self.save_baseline_data()

    def check_for_changes(self):
        """检查是否有实际的联系人变化"""
        if not self.baseline_data:
            self.initialize_baseline()
            return False

        user_folders = self.get_all_users()
        changes_detected = False
        change_details = []

        for user_folder in user_folders:
            username = user_folder.name
            current_stats = self.get_user_contact_stats(user_folder)

            if username not in self.baseline_data:
                # 新用户
                self.baseline_data[username] = current_stats
                changes_detected = True
                change_details.append(f"🆕 发现新用户: {username}")
                continue

            baseline = self.baseline_data[username]

            # 1. 检查联系人总数变化
            if current_stats['total_contacts'] != baseline['total_contacts']:
                diff = current_stats['total_contacts'] - baseline['total_contacts']
                changes_detected = True
                if diff > 0:
                    change_details.append(f"📈 {username}: 联系人增加 {diff} 个 ({baseline['total_contacts']} → {current_stats['total_contacts']})")
                else:
                    change_details.append(f"📉 {username}: 联系人减少 {abs(diff)} 个 ({baseline['total_contacts']} → {current_stats['total_contacts']})")

                # 详细分析新增/删除的联系人
                self.analyze_contact_changes(user_folder, username, baseline, current_stats, change_details)

            # 2. 检查新增联系人记录变化
            if current_stats['total_new_contacts'] != baseline['total_new_contacts']:
                diff = current_stats['total_new_contacts'] - baseline['total_new_contacts']
                if diff > 0:
                    changes_detected = True
                    change_details.append(f"🔔 {username}: 新增联系人记录增加 {diff} 个")

            # 3. 检查IP地址变化
            if current_stats['current_ip'] != baseline['current_ip']:
                changes_detected = True
                change_details.append(f"🌐 {username}: IP地址变化 ({baseline['current_ip']} → {current_stats['current_ip']})")

            # 更新基准数据
            self.baseline_data[username] = current_stats

        # 记录变化日志
        if changes_detected:
            change_record = {
                'timestamp': datetime.now().isoformat(),
                'changes': change_details
            }
            self.change_log.append(change_record)

            # 显示变化详情
            print(f"\n🔔 检测到变化 - {datetime.now().strftime('%H:%M:%S')}")
            for detail in change_details:
                print(f"   {detail}")

            # 保存更新后的基准数据
            self.save_baseline_data()

        return changes_detected

    def analyze_contact_changes(self, user_folder, username, baseline, current_stats, change_details):
        """分析联系人的具体变化"""
        # 读取当前联系人列表
        contact_files = [f for f in user_folder.glob("*.json")
                        if not f.name.startswith(("new_contacts", "ip_change"))]

        if not contact_files:
            return

        latest_file = max(contact_files, key=lambda x: x.stat().st_mtime)

        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                contacts = json.load(f)

            if isinstance(contacts, list):
                current_usernames = {contact.get("UserName", "") for contact in contacts if contact.get("UserName")}
                baseline_usernames = baseline.get('contact_usernames', set())

                # 找出新增和删除的联系人
                added_usernames = current_usernames - baseline_usernames
                removed_usernames = baseline_usernames - current_usernames

                if added_usernames:
                    added_contacts = [c for c in contacts if c.get("UserName") in added_usernames]
                    change_details.append(f"   ➕ 新增联系人: {len(added_contacts)} 个")
                    for contact in added_contacts[:3]:  # 显示前3个
                        nickname = contact.get("NickName", "无昵称")
                        change_details.append(f"      - {nickname}")
                    if len(added_contacts) > 3:
                        change_details.append(f"      - ... 还有 {len(added_contacts) - 3} 个")

                    # 将新增联系人记录到用户的new_contacts_log.json文件中
                    self.record_new_contacts_to_user_log(user_folder, username, added_contacts)

                if removed_usernames:
                    change_details.append(f"   ➖ 删除联系人: {len(removed_usernames)} 个")

                # 更新基准数据中的联系人列表
                self.baseline_data[username]['contact_usernames'] = current_usernames

        except Exception as e:
            change_details.append(f"   ❌ 分析联系人变化失败: {e}")

    def record_new_contacts_to_user_log(self, user_folder, username, new_contacts):
        """将新增联系人记录到用户的new_contacts_log.json文件中"""
        if not new_contacts:
            return

        new_contacts_log_file = user_folder / "new_contacts_log.json"

        try:
            # 读取现有记录
            if new_contacts_log_file.exists():
                with open(new_contacts_log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            else:
                log_data = {
                    "user": username,
                    "created_time": datetime.now().isoformat(),
                    "description": "新增联系人记录文件",
                    "records": []
                }

            # 创建新增记录
            new_record = {
                "timestamp": datetime.now().isoformat(),
                "detection_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "count": len(new_contacts),
                "detection_method": "monitor_auto_detection",  # 标识这是监控自动检测的
                "contacts": []
            }

            # 只添加新增联系人的关键信息（4个字段）
            for contact in new_contacts:
                contact_info = {
                    "UserName": contact.get("UserName", ""),
                    "Alias": contact.get("Alias", ""),
                    "Remark": contact.get("Remark", ""),
                    "NickName": contact.get("NickName", "")
                }
                new_record["contacts"].append(contact_info)

            # 添加到记录列表
            log_data["records"].append(new_record)
            log_data["last_updated"] = datetime.now().isoformat()

            # 保存到文件
            with open(new_contacts_log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)

            print(f"📝 已将 {len(new_contacts)} 个新增联系人记录到 {username}/new_contacts_log.json")

            # 同时生成CSV摘要文件
            self.generate_new_contacts_csv(user_folder, username, new_contacts)

        except Exception as e:
            print(f"❌ 记录新增联系人到用户日志失败: {e}")

    def generate_new_contacts_csv(self, user_folder, username, new_contacts):
        """生成新增联系人的CSV摘要文件"""
        try:
            csv_file = user_folder / "new_contacts_summary.csv"

            # 检查CSV文件是否存在，如果不存在则创建表头
            file_exists = csv_file.exists()

            with open(csv_file, 'a', encoding='utf-8-sig', newline='') as f:
                if not file_exists:
                    f.write("检测时间,昵称,UserName,别名,备注,检测方式\n")

                detection_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                for contact in new_contacts:
                    nickname = contact.get("NickName", "").replace(',', '，')  # 替换逗号避免CSV格式问题
                    username_id = contact.get("UserName", "")
                    alias = contact.get("Alias", "").replace(',', '，')
                    remark = contact.get("Remark", "").replace(',', '，')

                    f.write(f"{detection_time},{nickname},{username_id},{alias},{remark},监控自动检测\n")

            print(f"📊 已更新 {username}/new_contacts_summary.csv")

        except Exception as e:
            print(f"❌ 生成CSV摘要失败: {e}")


    
    def save_change_log(self):
        """保存变化日志到文件"""
        if not self.change_log:
            return

        log_file = Path("analysis_reports") / "contact_changes_log.json"
        log_file.parent.mkdir(exist_ok=True)

        try:
            # 读取现有日志
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    existing_log = json.load(f)
            else:
                existing_log = {
                    "created_time": datetime.now().isoformat(),
                    "description": "微信联系人变化监控日志",
                    "changes": []
                }

            # 添加新的变化记录
            existing_log["changes"].extend(self.change_log)
            existing_log["last_updated"] = datetime.now().isoformat()

            # 保存到文件
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(existing_log, f, ensure_ascii=False, indent=2)

            print(f"📄 变化日志已保存到: {log_file}")
            self.change_log.clear()  # 清空已保存的日志

        except Exception as e:
            print(f"❌ 保存变化日志失败: {e}")

    def display_change_history(self):
        """显示变化历史"""
        log_file = Path("analysis_reports") / "contact_changes_log.json"

        if not log_file.exists():
            print("📝 暂无变化历史记录")
            return

        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)

            changes = log_data.get("changes", [])
            if not changes:
                print("📝 暂无变化记录")
                return

            print(f"\n📈 联系人变化历史 (共 {len(changes)} 条记录)")
            print("-" * 60)

            # 显示最近的10条记录
            recent_changes = changes[-10:]
            for change in recent_changes:
                timestamp = change.get("timestamp", "")
                if timestamp:
                    dt = datetime.fromisoformat(timestamp)
                    time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    time_str = "未知时间"

                print(f"\n🕒 {time_str}")
                for detail in change.get("changes", []):
                    print(f"   {detail}")

            if len(changes) > 10:
                print(f"\n... 还有 {len(changes) - 10} 条历史记录")

        except Exception as e:
            print(f"❌ 读取变化历史失败: {e}")

    def reset_baseline_data(self):
        """重置基准数据"""
        if self.baseline_file.exists():
            self.baseline_file.unlink()
            print("🔄 已删除现有基准数据文件")

        self.baseline_data.clear()
        print("🔄 已清空内存中的基准数据")

        # 重新初始化
        self.initialize_baseline()
        print("✅ 基准数据已重置")

    def run_interactive_monitor(self):
        """运行交互式监控"""
        while True:
            print("\n" + "="*50)
            print("📱 微信联系人监控工具")
            print("="*50)
            print("1. 显示当前状态")
            print("2. 开始实时监控")
            print("3. 显示变化历史")
            print("4. 保存变化日志")
            print("5. 重置基准数据")
            print("6. 手动检查变化")
            print("7. 退出")

            choice = input("\n请选择操作 (1-7): ").strip()

            if choice == '1':
                self.display_current_status()
            elif choice == '2':
                interval = input("请输入监控间隔(秒，默认60): ").strip()
                try:
                    interval = int(interval) if interval else 60
                except ValueError:
                    interval = 60
                self.monitor_changes(interval)
            elif choice == '3':
                self.display_change_history()
            elif choice == '4':
                self.save_change_log()
            elif choice == '5':
                self.reset_baseline_data()
            elif choice == '6':
                self.check_for_changes()
            elif choice == '7':
                print("👋 再见!")
                break
            else:
                print("❌ 无效选择，请重试")

def main():
    monitor = ContactMonitor()

    # 如果有命令行参数，直接执行对应功能
    import sys
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == 'status':
            monitor.display_current_status()
        elif command == 'monitor':
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            monitor.monitor_changes(interval)
        elif command == 'check':
            monitor.check_for_changes()
        elif command == 'reset':
            monitor.reset_baseline_data()
        elif command == 'history':
            monitor.display_change_history()
        else:
            print("用法: python contact_monitor.py [status|monitor|check|reset|history] [参数]")
            print("  status  - 显示当前状态")
            print("  monitor [间隔] - 开始实时监控")
            print("  check   - 手动检查变化")
            print("  reset   - 重置基准数据")
            print("  history - 显示变化历史")
    else:
        # 交互式模式
        monitor.run_interactive_monitor()

if __name__ == "__main__":
    main()
