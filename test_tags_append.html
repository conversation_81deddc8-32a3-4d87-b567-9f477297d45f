<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签追加功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chat-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .tag {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin: 2px;
            border: 1px solid #bbdefb;
        }
        
        .tag-comprehensive {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }
        
        .tag-delete {
            margin-left: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .tag-delete:hover {
            color: #f44336;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn-secondary {
            background: #2196F3;
        }
        
        .btn-secondary:hover {
            background: #1976D2;
        }
        
        .add-tag-input {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
        
        .add-tag-input input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>标签追加功能测试</h1>
        
        <div class="chat-info" id="chatInfo">
            <div>联系人: 张博士_英国_AI专家</div>
            <div>昵称: Dr.Zhang | 用户: test_user</div>
            <div class="stats">
                <span class="stat-item">总消息: <span class="stat-value">25</span></span>
                <span class="stat-item">文本消息: <span class="stat-value">20</span></span>
            </div>
        </div>
        
        <div class="test-controls">
            <h3>测试操作</h3>
            <button class="btn" onclick="addInitialTags()">1. 添加初始标签</button>
            <button class="btn btn-secondary" onclick="addAnalysisTags()">2. 模拟智能分析标签</button>
            <button class="btn btn-secondary" onclick="addMoreTags()">3. 再次追加标签</button>
            <button class="btn" onclick="clearAllTags()">清除所有标签</button>
        </div>
        
        <div class="test-results">
            <h3>测试结果</h3>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // 模拟 updateContactTagsDisplay 函数
        function updateContactTagsDisplay(newTags) {
            const chatInfo = document.getElementById('chatInfo');
            if (!chatInfo) {
                console.error('❌ 未找到 chatInfo 区域');
                return;
            }

            // 查找现有的标签区域
            let existingTagsSection = chatInfo.querySelector('.contact-tags-section');
            let existingTags = [];

            // 如果已经有标签区域，获取现有标签
            if (existingTagsSection) {
                const existingTagElements = existingTagsSection.querySelectorAll('.tag-text');
                existingTags = Array.from(existingTagElements)
                    .map(tag => tag.textContent.trim())
                    .filter(tag => tag.length > 0);
            }

            // 合并标签，去重
            const allTags = [...new Set([...existingTags, ...newTags])];

            // 如果已有标签区域，先移除
            if (existingTagsSection) {
                existingTagsSection.remove();
            }

            // 创建新的标签区域
            const tagsHtml = `
                <div class="contact-tags-section" style="margin-top: 15px;">
                    <div class="tags-header">
                        <div style="font-size: 14px; color: #333; margin-bottom: 10px; font-weight: 600;">🏷️ 联系人标签</div>
                        <div class="tags-controls">
                            <button onclick="addNewTag()" class="add-tag-btn" title="添加标签">➕</button>
                        </div>
                    </div>

                    <!-- 综合标签 -->
                    <div class="comprehensive-tags" style="margin-bottom: 15px;">
                        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">综合标签:</div>
                        <div class="tags-container" id="comprehensiveTagsContainer">
                            ${allTags.map(tag => `<span class="tag tag-comprehensive editable-tag">
                                <span class="tag-text">${tag}</span>
                                <span class="tag-delete" onclick="deleteTag(this)" title="删除标签">×</span>
                            </span>`).join('')}
                        </div>
                    </div>

                    <!-- 添加新标签输入框（默认隐藏） -->
                    <div class="add-tag-input" id="addTagInput" style="display: none; margin-top: 10px;">
                        <input type="text" placeholder="输入新标签" style="padding: 5px; border: 1px solid #ddd; border-radius: 4px; margin-right: 5px;">
                        <button onclick="confirmAddTag()" style="padding: 5px 10px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">确定</button>
                        <button onclick="cancelAddTag()" style="padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 5px;">取消</button>
                    </div>
                </div>
            `;

            // 追加到 chatInfo 区域
            chatInfo.insertAdjacentHTML('beforeend', tagsHtml);

            console.log(`✅ 已更新联系人标签显示，共 ${allTags.length} 个标签`);
            logTestResult(`追加了 ${newTags.length} 个新标签，当前总共 ${allTags.length} 个标签`);
        }

        function deleteTag(deleteBtn) {
            const tagElement = deleteBtn.closest('.tag');
            const tagText = tagElement.querySelector('.tag-text').textContent;
            
            if (confirm(`确定要删除标签 "${tagText}" 吗？`)) {
                tagElement.remove();
                logTestResult(`删除了标签: ${tagText}`);
            }
        }

        function addNewTag() {
            const addTagInput = document.getElementById('addTagInput');
            if (addTagInput) {
                addTagInput.style.display = 'block';
                const input = addTagInput.querySelector('input');
                input.focus();
                input.value = '';
            }
        }

        function confirmAddTag() {
            const addTagInput = document.getElementById('addTagInput');
            const input = addTagInput.querySelector('input');
            const newTagText = input.value.trim();

            if (newTagText.length === 0) {
                alert('标签内容不能为空');
                return;
            }

            // 检查是否已存在
            const existingTags = Array.from(document.querySelectorAll('#comprehensiveTagsContainer .tag-text'))
                .map(tag => tag.textContent.trim());

            if (existingTags.includes(newTagText)) {
                alert('标签已存在');
                return;
            }

            // 添加新标签到综合标签容器
            const tagsContainer = document.getElementById('comprehensiveTagsContainer');
            if (tagsContainer) {
                const newTagHtml = `
                    <span class="tag tag-comprehensive editable-tag">
                        <span class="tag-text">${newTagText}</span>
                        <span class="tag-delete" onclick="deleteTag(this)" title="删除标签">×</span>
                    </span>
                `;
                tagsContainer.insertAdjacentHTML('beforeend', newTagHtml);
                addTagInput.style.display = 'none';
                logTestResult(`手动添加了标签: ${newTagText}`);
            }
        }

        function cancelAddTag() {
            const addTagInput = document.getElementById('addTagInput');
            addTagInput.style.display = 'none';
        }

        // 测试函数
        function addInitialTags() {
            const initialTags = ['博士', 'AI专家', '英国'];
            updateContactTagsDisplay(initialTags);
        }

        function addAnalysisTags() {
            const analysisTags = ['5年经验', '求职积极', '沟通良好', 'Python'];
            updateContactTagsDisplay(analysisTags);
        }

        function addMoreTags() {
            const moreTags = ['机器学习', '深度学习', '全职'];
            updateContactTagsDisplay(moreTags);
        }

        function clearAllTags() {
            const tagsSection = document.querySelector('.contact-tags-section');
            if (tagsSection) {
                tagsSection.remove();
                logTestResult('清除了所有标签');
            }
        }

        function logTestResult(message) {
            const results = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        }
    </script>
</body>
</html>
