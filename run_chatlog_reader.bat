@echo off
echo 聊天记录读取器
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查data目录是否存在
if not exist "data" (
    echo 错误: 未找到data目录，请确保data目录存在并包含JSON文件
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "chatlogs" mkdir chatlogs

echo 选择操作:
echo 1. 处理所有联系人文件的聊天记录
echo 2. 处理指定文件的聊天记录
echo 3. 生成聊天记录摘要报告
echo 4. 查看输出目录
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 开始处理所有文件...
    echo 这可能需要一些时间，请耐心等待...
    echo.
    python chatlog_reader.py --all
    echo.
    echo 处理完成！聊天记录已保存到 chatlogs/ 目录
    
) else if "%choice%"=="2" (
    echo.
    echo 可用的JSON文件:
    dir /b data\*.json
    echo.
    set /p filename="请输入要处理的文件名 (不含路径): "
    echo.
    echo 正在处理文件: %filename%
    python chatlog_reader.py --file "%filename%"
    echo.
    echo 处理完成！
    
) else if "%choice%"=="3" (
    echo.
    echo 生成摘要报告...
    python chatlog_reader.py --report
    echo.
    echo 报告生成完成！
    
) else if "%choice%"=="4" (
    echo.
    echo 打开输出目录...
    if exist "chatlogs" (
        explorer chatlogs
    ) else (
        echo chatlogs目录不存在，请先运行聊天记录读取
    )
    
) else (
    echo 无效选择，退出程序
)

echo.
pause
