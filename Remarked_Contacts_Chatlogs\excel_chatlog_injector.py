# Excel在线表聊天记录自动注入工具

import json
import re
import pandas as pd
from pathlib import Path
from datetime import datetime
import openpyxl
from openpyxl.styles import Alignment, Font
import requests

class ExcelChatlogInjector:
    def __init__(self):
        self.chatlog_dir = Path(".")
        self.output_dir = Path("excel_output")
        self.output_dir.mkdir(exist_ok=True)
        
    def extract_id_from_remark(self, remark):
        """从备注中提取ID（前面的数字）"""
        if not remark:
            return None
        
        # 匹配开头的数字
        match = re.match(r'^(\d+)', str(remark).strip())
        if match:
            return match.group(1)
        return None
    
    def format_chatlog_for_excel(self, chatlog_data, max_messages=50):
        """格式化聊天记录用于Excel显示"""
        if not chatlog_data or not isinstance(chatlog_data, list):
            return "无聊天记录"
        
        if len(chatlog_data) == 0:
            return "无聊天记录"
        
        # 限制消息数量避免Excel单元格过大
        messages = chatlog_data[-max_messages:] if len(chatlog_data) > max_messages else chatlog_data
        
        formatted_messages = []
        for msg in messages:
            # 根据实际API返回的数据结构调整字段名
            timestamp = msg.get('timestamp', msg.get('time', ''))
            sender = msg.get('sender', msg.get('from', ''))
            content = msg.get('content', msg.get('message', ''))
            msg_type = msg.get('type', msg.get('msgType', ''))
            
            # 格式化时间
            if timestamp:
                try:
                    if 'T' in timestamp:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    else:
                        dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                    time_str = dt.strftime('%m-%d %H:%M')
                except:
                    time_str = str(timestamp)[:16]
            else:
                time_str = "未知时间"
            
            # 处理消息内容
            if msg_type == 'text' or not msg_type:
                msg_content = str(content)[:100]  # 限制长度
            elif msg_type == 'image':
                msg_content = "[图片]"
            elif msg_type == 'voice':
                msg_content = "[语音]"
            elif msg_type == 'video':
                msg_content = "[视频]"
            elif msg_type == 'file':
                msg_content = "[文件]"
            else:
                msg_content = f"[{msg_type}]"
            
            # 格式化单条消息
            formatted_msg = f"{time_str} | {msg_content}"
            formatted_messages.append(formatted_msg)
        
        # 如果消息太多，添加提示
        result = "\n".join(formatted_messages)
        if len(chatlog_data) > max_messages:
            result = f"[显示最近{max_messages}条，共{len(chatlog_data)}条]\n" + result
        
        return result
    
    def load_chatlog_data(self, username):
        """加载用户的聊天记录数据"""
        chatlog_file = self.chatlog_dir / username / f"chatlog_{username}.json"
        
        if not chatlog_file.exists():
            print(f"   ❌ 未找到 {username} 的聊天记录文件")
            return None
        
        try:
            with open(chatlog_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"   ❌ 读取 {username} 聊天记录失败: {e}")
            return None
    
    def create_chatlog_mapping(self, chatlog_data):
        """创建ID到聊天记录的映射"""
        if not chatlog_data or 'chatlogs' not in chatlog_data:
            return {}
        
        id_to_chatlog = {}
        
        for entry in chatlog_data['chatlogs']:
            contact_info = entry.get('contact_info', {})
            remark = contact_info.get('Remark', '')
            
            # 提取ID
            contact_id = self.extract_id_from_remark(remark)
            if contact_id:
                chatlog_messages = entry.get('chatlog_data', [])
                formatted_chatlog = self.format_chatlog_for_excel(chatlog_messages)
                
                id_to_chatlog[contact_id] = {
                    'remark': remark,
                    'nickname': contact_info.get('NickName', ''),
                    'chatlog': formatted_chatlog,
                    'message_count': len(chatlog_messages) if isinstance(chatlog_messages, list) else 0,
                    'last_update': entry.get('fetch_time', '')
                }
        
        return id_to_chatlog
    
    def create_excel_template(self, id_to_chatlog):
        """创建Excel模板文件"""
        # 创建数据列表
        data = []
        for contact_id, info in id_to_chatlog.items():
            data.append({
                'ID': contact_id,
                '备注': info['remark'],
                '昵称': info['nickname'],
                '消息数量': info['message_count'],
                '聊天记录': info['chatlog'],
                '最后更新': info['last_update'][:19] if info['last_update'] else ''
            })
        
        # 按ID排序
        data.sort(key=lambda x: int(x['ID']) if x['ID'].isdigit() else 0)
        
        return pd.DataFrame(data)
    
    def save_to_excel(self, df, username, filename=None):
        """保存到Excel文件并设置格式"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chatlog_excel_{username}_{timestamp}.xlsx"
        
        output_file = self.output_dir / filename
        
        # 使用openpyxl保存以便设置格式
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='聊天记录', index=False)
            
            # 获取工作表
            worksheet = writer.sheets['聊天记录']
            
            # 设置列宽
            column_widths = {
                'A': 10,  # ID
                'B': 25,  # 备注
                'C': 15,  # 昵称
                'D': 12,  # 消息数量
                'E': 80,  # 聊天记录
                'F': 20   # 最后更新
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
            
            # 设置聊天记录列的文本换行
            for row in range(2, len(df) + 2):  # 从第2行开始（第1行是标题）
                cell = worksheet[f'E{row}']  # 聊天记录列
                cell.alignment = Alignment(wrap_text=True, vertical='top')
                
                # 设置行高
                worksheet.row_dimensions[row].height = min(200, max(30, len(str(cell.value).split('\n')) * 15))
            
            # 设置标题行格式
            for col in range(1, 7):
                cell = worksheet.cell(row=1, column=col)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
        
        return output_file
    
    def inject_to_online_excel(self, excel_file_path, sheet_name="聊天记录"):
        """注入数据到在线Excel表格（需要根据具体平台实现）"""
        print("📊 在线Excel注入选项:")
        print("1. Microsoft Excel Online (Office 365)")
        print("2. Google Sheets")
        print("3. 腾讯文档")
        print("4. 石墨文档")
        print("5. 导出为CSV格式")
        
        choice = input("请选择目标平台 (1-5): ").strip()
        
        if choice == "1":
            return self.inject_to_office365(excel_file_path)
        elif choice == "2":
            return self.inject_to_google_sheets(excel_file_path)
        elif choice == "3":
            return self.inject_to_tencent_docs(excel_file_path)
        elif choice == "4":
            return self.inject_to_shimo_docs(excel_file_path)
        elif choice == "5":
            return self.export_to_csv(excel_file_path)
        else:
            print("❌ 无效选择")
            return False
    
    def inject_to_office365(self, excel_file_path):
        """注入到Office 365 Excel Online"""
        print("📋 Office 365 Excel Online 注入步骤:")
        print("1. 打开 https://office.com 并登录")
        print("2. 创建新的Excel工作簿或打开现有工作簿")
        print("3. 使用以下方法之一导入数据:")
        print("   - 方法A: 直接上传生成的Excel文件")
        print("   - 方法B: 复制粘贴数据")
        print("   - 方法C: 使用Microsoft Graph API (需要开发)")
        print(f"4. 生成的文件位置: {excel_file_path}")

        # 自动打开文件所在文件夹
        import subprocess
        import os
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(['explorer', '/select,', str(excel_file_path)], check=True)
            elif os.name == 'posix':  # macOS/Linux
                subprocess.run(['open', '-R', str(excel_file_path)], check=True)
        except:
            print(f"   📁 请手动打开文件夹: {excel_file_path.parent}")

        return True
    
    def inject_to_google_sheets(self, excel_file_path):
        """注入到Google Sheets"""
        print("📋 Google Sheets 注入步骤:")
        print("1. 打开 https://sheets.google.com")
        print("2. 创建新的电子表格")
        print("3. 使用 文件 > 导入 > 上传 导入Excel文件")
        print("4. 或者使用Google Sheets API进行自动化")
        print(f"5. 生成的文件位置: {excel_file_path}")
        
        # TODO: 实现Google Sheets API集成
        return True
    
    def inject_to_tencent_docs(self, excel_file_path):
        """注入到腾讯文档"""
        print("📋 腾讯文档注入步骤:")
        print("1. 打开 https://docs.qq.com")
        print("2. 创建新的在线表格")
        print("3. 使用导入功能上传Excel文件")
        print(f"4. 生成的文件位置: {excel_file_path}")
        return True
    
    def inject_to_shimo_docs(self, excel_file_path):
        """注入到石墨文档"""
        print("📋 石墨文档注入步骤:")
        print("1. 打开 https://shimo.im")
        print("2. 创建新的表格")
        print("3. 使用导入功能上传Excel文件")
        print(f"4. 生成的文件位置: {excel_file_path}")
        return True
    
    def export_to_csv(self, excel_file_path):
        """导出为CSV格式"""
        try:
            df = pd.read_excel(excel_file_path)
            csv_file = excel_file_path.with_suffix('.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"✅ CSV文件已生成: {csv_file}")
            return True
        except Exception as e:
            print(f"❌ CSV导出失败: {e}")
            return False
    
    def process_user(self, username):
        """处理单个用户的聊天记录注入"""
        print(f"\n📊 处理用户: {username}")
        print("-" * 50)
        
        # 加载聊天记录数据
        chatlog_data = self.load_chatlog_data(username)
        if not chatlog_data:
            return False
        
        # 创建ID到聊天记录的映射
        id_to_chatlog = self.create_chatlog_mapping(chatlog_data)
        
        if not id_to_chatlog:
            print(f"   ❌ 没有找到有效的ID和聊天记录映射")
            return False
        
        print(f"   ✅ 找到 {len(id_to_chatlog)} 个有ID的联系人")
        
        # 创建Excel数据
        df = self.create_excel_template(id_to_chatlog)
        
        # 保存到Excel文件
        excel_file = self.save_to_excel(df, username)
        print(f"   💾 Excel文件已生成: {excel_file}")
        
        # 询问是否注入到在线表格
        inject_online = input("   🌐 是否注入到在线Excel表格? (y/N): ").strip().lower()
        if inject_online == 'y':
            self.inject_to_online_excel(excel_file)
        
        return True
    
    def process_all_users(self):
        """处理所有用户"""
        print("📊 Excel聊天记录注入工具")
        print("="*60)
        
        # 查找所有用户文件夹
        user_folders = [d for d in self.chatlog_dir.iterdir() 
                       if d.is_dir() and not d.name.startswith('.')]
        
        if not user_folders:
            print("❌ 没有找到用户聊天记录文件夹")
            return
        
        print(f"📂 找到 {len(user_folders)} 个用户文件夹:")
        for i, folder in enumerate(user_folders, 1):
            print(f"   {i}. {folder.name}")
        
        # 选择处理方式
        print("\n处理选项:")
        print("1. 处理所有用户")
        print("2. 选择特定用户")
        
        choice = input("请选择 (1-2): ").strip()
        
        if choice == "1":
            for folder in user_folders:
                try:
                    self.process_user(folder.name)
                except Exception as e:
                    print(f"❌ 处理用户 {folder.name} 失败: {e}")
        elif choice == "2":
            user_choice = input(f"请输入用户编号 (1-{len(user_folders)}): ").strip()
            try:
                user_index = int(user_choice) - 1
                if 0 <= user_index < len(user_folders):
                    self.process_user(user_folders[user_index].name)
                else:
                    print("❌ 无效的用户编号")
            except ValueError:
                print("❌ 请输入有效的数字")
        else:
            print("❌ 无效选择")

def main():
    """主函数"""
    injector = ExcelChatlogInjector()
    injector.process_all_users()

if __name__ == "__main__":
    main()
