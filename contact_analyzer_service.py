"""
Windows服务版本的联系人分析器
可以作为Windows服务在后台运行定时任务
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    import win32serviceutil
    import win32service
    import win32event
    import servicemanager
    WINDOWS_SERVICE_AVAILABLE = True
except ImportError:
    WINDOWS_SERVICE_AVAILABLE = False
    print("警告: 无法导入Windows服务模块，将以普通模式运行")

from find_duplicate_contacts import ScheduledContactAnalyzer

class ContactAnalyzerService(win32serviceutil.ServiceFramework if WINDOWS_SERVICE_AVAILABLE else object):
    _svc_name_ = "ContactAnalyzerService"
    _svc_display_name_ = "微信联系人分析服务"
    _svc_description_ = "定时分析微信联系人的新增和重复情况"

    def __init__(self, args=None):
        if WINDOWS_SERVICE_AVAILABLE:
            win32serviceutil.ServiceFramework.__init__(self, args)
            self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        
        # 设置工作目录
        os.chdir(current_dir)
        
        # 初始化分析器
        self.analyzer = ScheduledContactAnalyzer()
        self.running = False
        
        # 设置服务日志
        self.setup_service_logging()
        
    def setup_service_logging(self):
        """设置服务专用日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / "service.log"
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('ContactAnalyzerService')
        
    def SvcStop(self):
        """停止服务"""
        if WINDOWS_SERVICE_AVAILABLE:
            self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
            win32event.SetEvent(self.hWaitStop)
        
        self.running = False
        self.logger.info("服务停止请求已接收")
        
    def SvcDoRun(self):
        """运行服务"""
        if WINDOWS_SERVICE_AVAILABLE:
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, '')
            )
        
        self.logger.info("微信联系人分析服务已启动")
        self.main_loop()
        
    def main_loop(self):
        """主循环"""
        self.running = True
        
        # 导入调度模块
        import schedule
        
        # 设置定时任务
        schedule.every().day.at("09:00").do(self.run_analysis)
        schedule.every().day.at("21:00").do(self.run_analysis)
        
        self.logger.info("定时任务已设置: 每天09:00和21:00")
        
        # 立即运行一次
        self.logger.info("立即运行一次分析...")
        self.run_analysis()
        
        # 主循环
        while self.running:
            try:
                schedule.run_pending()
                
                if WINDOWS_SERVICE_AVAILABLE:
                    # 检查服务停止信号
                    if win32event.WaitForSingleObject(self.hWaitStop, 60000) == win32event.WAIT_OBJECT_0:
                        break
                else:
                    time.sleep(60)
                    
            except Exception as e:
                self.logger.error(f"主循环错误: {str(e)}")
                time.sleep(60)
                
        self.logger.info("服务主循环已退出")
        
    def run_analysis(self):
        """运行分析"""
        try:
            self.logger.info("开始定时分析...")
            self.analyzer.run_scheduled_analysis()
            self.logger.info("定时分析完成")
        except Exception as e:
            self.logger.error(f"分析过程中发生错误: {str(e)}")

def run_as_console():
    """以控制台模式运行"""
    print("以控制台模式运行联系人分析服务...")
    
    service = ContactAnalyzerService()
    
    try:
        service.main_loop()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止...")
        service.SvcStop()
        print("服务已停止")

def main():
    if len(sys.argv) == 1:
        # 没有参数，以控制台模式运行
        run_as_console()
    elif WINDOWS_SERVICE_AVAILABLE:
        # 有参数且支持Windows服务，按服务模式处理
        win32serviceutil.HandleCommandLine(ContactAnalyzerService)
    else:
        print("不支持Windows服务模式，以控制台模式运行...")
        run_as_console()

if __name__ == '__main__':
    main()
