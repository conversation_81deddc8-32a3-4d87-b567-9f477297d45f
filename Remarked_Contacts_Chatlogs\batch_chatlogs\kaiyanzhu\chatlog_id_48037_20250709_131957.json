{"user": "<PERSON><PERSON><PERSON><PERSON>", "contact_id": "48037", "contact_info": {"UserName": "wxid_ss6caqhs2mj022", "Alias": "better-wf", "Remark": "48037王峰", "NickName": "<PERSON>"}, "fetch_time": "2025-07-09T13:19:57.002286", "message_count": 20, "chatlog_data": [{"seq": 1724918065000, "time": "2024-08-29T15:54:25+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 1, "subType": 0, "content": "我通过了你的朋友验证请求，现在我们可以开始聊天了"}, {"seq": 1724918275000, "time": "2024-08-29T15:57:55+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博您好，方便发一份cv了解一下吗？"}, {"seq": 1724918425000, "time": "2024-08-29T16:00:25+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"wx7d838ad162ef2640\" sdkver=\"0\">\n\t\t<title>王峰的简历.pdf</title>\n\t\t<des />\n\t\t<type>6</type>\n\t\t<appattach>\n\t\t\t<totallen>277258</totallen>\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<attachid>@cdn_3057020100044b3049020100020474e9085c02030f5d6502042d7faf2b020466d02a76042435393739343437322d643631612d343031382d623837372d3735646636316539616561300204011c00050201000405004c55cd00_7b047d152184097aebe02e790e77b9f2_1</attachid>\n\t\t\t<cdnattachurl>3057020100044b3049020100020474e9085c02030f5d6502042d7faf2b020466d02a76042435393739343437322d643631612d343031382d623837372d3735646636316539616561300204011c00050201000405004c55cd00</cdnattachurl>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey>7b047d152184097aebe02e790e77b9f2</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t\t<filekey>wxid_6ahbbsar7g8i22_5_1724918425</filekey>\n\t\t\t<overwrite_newmsgid>5050265751903700486</overwrite_newmsgid>\n\t\t\t<fileuploadtoken>v1_6BvkG6MpWyLU7geeLuZct5gVcXRcFn9iB/7nKPZURCdkSqgdw0Csi+m+XH/uXrJHdpeMoTUhTLnwU1rfxd0WsrWM28ycTof2uVW8bV+2gN+2S8+zQNQKlExJauDJIGXQDV0KSPv93dGOf+3P1cdPE3Mzb5rCRXIDbuIhfxGwNRvkuCGi4IVl6CE4RzoNbrH6GOlCvzZnROZ3/ONHWIZC63Q=</fileuploadtoken>\n\t\t</appattach>\n\t\t<md5>67dc849dce9644a77043542f9f5ff77e</md5>\n\t\t<statextstr>GhQKEnd4N2Q4MzhhZDE2MmVmMjY0MA==</statextstr>\n\t\t<recorditem><![CDATA[(null)]]></recorditem>\n\t\t<uploadpercent>98</uploadpercent>\n\t</appmsg>\n\t<fromusername>wxid_ss6caqhs2mj022</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>9</version>\n\t\t<appname>猎聘APP</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n\u0000", "contents": {"md5": "67dc849dce9644a77043542f9f5ff77e", "title": "王峰的简历.pdf"}}, {"seq": 1724918434000, "time": "2024-08-29T16:00:34+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 1, "subType": 0, "content": "好久没更新简历了"}, {"seq": 1724918828000, "time": "2024-08-29T16:07:08+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "目前现在的项目都需要回来入职，您近期不打算回国的话，只有后面国家级的项目可以参与"}, {"seq": 1724925040000, "time": "2024-08-29T17:50:40+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 1, "subType": 0, "content": "大概两年内不回国"}, {"seq": 1724977990000, "time": "2024-08-30T08:33:10+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "明年国家级可以兼职，我到时候可以帮您联系"}, {"seq": 1748595532000, "time": "2025-05-30T16:58:52+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "王博士，下午好！ 我们之前有联系过的，目前我们在准备创业大赛，您有兴趣参加吗         https://www.fujiantalent.com/#/\n这个是大赛的链接，您可以先了解一下"}, {"seq": 1748595682000, "time": "2025-05-30T17:01:22+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 1, "subType": 0, "content": "没有什么可报的项目[破涕为笑]"}, {"seq": 1748600121506, "time": "2025-05-30T18:15:21+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "您是回国了没有呀"}, {"seq": 1748600134595, "time": "2025-05-30T18:15:34+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "浙江省这边的省千也可以了解下呀"}, {"seq": 1748600331000, "time": "2025-05-30T18:18:51+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 1, "subType": 0, "content": "还没回国"}, {"seq": 1748600396444, "time": "2025-05-30T18:19:56+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我们省千的话都是一些真实的需求，您有没有回国发展的打算呀"}, {"seq": 1748600433000, "time": "2025-05-30T18:20:33+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 1, "subType": 0, "content": "有的"}, {"seq": 1748602594800, "time": "2025-05-30T18:56:34+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "那您可以发一下您的简历呀"}, {"seq": 1748602606720, "time": "2025-05-30T18:56:46+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "我这边可以帮您看一下省千的一些单位呀"}, {"seq": 1748602833000, "time": "2025-05-30T19:00:33+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 1, "subType": 0, "content": "过会哈"}, {"seq": 1748602870057, "time": "2025-05-30T19:01:10+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "嗯嗯，不着急～"}, {"seq": 1748603061000, "time": "2025-05-30T19:04:21+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "wxid_ss6caqhs2mj022", "senderName": "48037王峰", "isSelf": false, "type": 49, "subType": 6, "content": "<?xml version=\"1.0\"?>\n<msg>\n\t<appmsg appid=\"wx6618f1cfc6c132f8\" sdkver=\"0\">\n\t\t<title>简历-王峰-202505.pdf</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>6</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<md5>da1a9101e34c9043da1e9cf0f332b305</md5>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>337910</totallen>\n\t\t\t<attachid>@cdn_3057020100044b3049020100020474e9085c02030f5d650204457faf2b0204683990b5042465373561376662392d643461642d343036332d393966302d3338393536316335333434630204011c00050201000405004c511d00_2c668f7859e6185167b29c4c8ca49423_1</attachid>\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext>pdf</fileext>\n\t\t\t<fileuploadtoken>v1_vK5qT3jufl6FYxLJZ/QE5qffohtN0OaEfdro5A2W4jLAE7O/lX7YGn1+zGh0J/M2kps386eHrtP0z+kSmtBRPACr6kf+1CTCoTlqlBzwBfvxHptJMDOydA2pEWlffnNYiP9MF1XtA50gj5rh7MMkvfhEoiZuajQG+d4xUhJQO/xE6lMr6M4MuXz8BI9zMRyv+wAOHlQmDgR46/NjE/MaJMXat23QzcDW+BtgGhSybUiKZD1x</fileuploadtoken>\n\t\t\t<overwrite_newmsgid>5717480343949135159</overwrite_newmsgid>\n\t\t\t<filekey>4584c2131afd0682d64ced72ca394a8e</filekey>\n\t\t\t<cdnattachurl>3057020100044b3049020100020474e9085c02030f5d650204457faf2b0204683990b5042465373561376662392d643461642d343036332d393966302d3338393536316335333434630204011c00050201000405004c511d00</cdnattachurl>\n\t\t\t<aeskey>2c668f7859e6185167b29c4c8ca49423</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_ss6caqhs2mj022</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>7</version>\n\t\t<appname>微信电脑版</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n\u0000", "contents": {"md5": "da1a9101e34c9043da1e9cf0f332b305", "title": "简历-王峰-202505.pdf"}}, {"seq": 1748604572344, "time": "2025-05-30T19:29:32+08:00", "talker": "wxid_ss6caqhs2mj022", "talkerName": "", "isChatRoom": false, "sender": "", "senderName": "", "isSelf": true, "type": 1, "subType": 0, "content": "收到"}]}